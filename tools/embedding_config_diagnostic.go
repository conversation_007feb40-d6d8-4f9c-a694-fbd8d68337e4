package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
)

// EmbeddingConfigDiagnostic Azure OpenAI Embedding 配置診斷工具
type EmbeddingConfigDiagnostic struct {
	BaseURL    string
	APIKey     string
	APIVersion string
	Model      string
}

func main() {
	if len(os.Args) < 5 {
		fmt.Println("使用方法: go run embedding_config_diagnostic.go <base_url> <api_key> <api_version> <model>")
		fmt.Println("示例: go run embedding_config_diagnostic.go https://your-resource.openai.azure.com your-api-key 2023-05-15 your-deployment-name")
		os.Exit(1)
	}

	diagnostic := &EmbeddingConfigDiagnostic{
		BaseURL:    os.Args[1],
		APIKey:     os.Args[2],
		APIVersion: os.Args[3],
		Model:      os.Args[4],
	}

	ctx := context.Background()
	diagnostic.RunDiagnostic(ctx)
}

func (d *EmbeddingConfigDiagnostic) RunDiagnostic(ctx context.Context) {
	fmt.Println("🔍 Azure OpenAI Embedding 配置診斷")
	fmt.Println("=====================================")

	// 1. 檢查配置格式
	d.checkConfigFormat()

	// 2. 檢查網絡連接
	d.checkNetworkConnectivity(ctx)

	// 3. 檢查 API 端點
	d.checkAPIEndpoint(ctx)

	// 4. 測試 embedding 調用
	d.testEmbeddingCall(ctx)
}

func (d *EmbeddingConfigDiagnostic) checkConfigFormat() {
	fmt.Println("\n1. 📋 檢查配置格式")
	fmt.Println("-------------------")

	// 檢查 BaseURL
	if !strings.HasPrefix(d.BaseURL, "https://") {
		fmt.Printf("❌ BaseURL 應該以 https:// 開頭: %s\n", d.BaseURL)
	} else if strings.Contains(d.BaseURL, "/openai/") {
		fmt.Printf("⚠️  BaseURL 不應包含路徑，只需要基礎域名: %s\n", d.BaseURL)
	} else {
		fmt.Printf("✅ BaseURL 格式正確: %s\n", d.BaseURL)
	}

	// 檢查 API Key
	if len(d.APIKey) < 10 {
		fmt.Printf("❌ API Key 長度太短，可能不正確: %d 字符\n", len(d.APIKey))
	} else {
		fmt.Printf("✅ API Key 長度正常: %d 字符\n", len(d.APIKey))
	}

	// 檢查 API Version
	supportedVersions := []string{"2023-05-15", "2023-12-01-preview", "2024-02-15-preview"}
	isSupported := false
	for _, version := range supportedVersions {
		if d.APIVersion == version {
			isSupported = true
			break
		}
	}
	if isSupported {
		fmt.Printf("✅ API Version 受支持: %s\n", d.APIVersion)
	} else {
		fmt.Printf("⚠️  API Version 可能不受支持: %s\n", d.APIVersion)
		fmt.Printf("   支持的版本: %v\n", supportedVersions)
	}

	// 檢查 Model (部署名稱)
	if strings.Contains(d.Model, "text-embedding") {
		fmt.Printf("⚠️  Model 字段應該是部署名稱，不是模型名稱: %s\n", d.Model)
		fmt.Println("   請使用您在 Azure OpenAI Studio 中創建的部署名稱")
	} else {
		fmt.Printf("✅ Model (部署名稱) 格式正確: %s\n", d.Model)
	}
}

func (d *EmbeddingConfigDiagnostic) checkNetworkConnectivity(ctx context.Context) {
	fmt.Println("\n2. 🌐 檢查網絡連接")
	fmt.Println("-------------------")

	client := g.Client()
	client.SetTimeout(10 * time.Second)

	// 簡單的連接測試
	resp, err := client.Get(ctx, d.BaseURL)
	if err != nil {
		fmt.Printf("❌ 無法連接到 %s: %v\n", d.BaseURL, err)
		return
	}
	defer resp.Close()

	fmt.Printf("✅ 網絡連接正常，狀態碼: %d\n", resp.StatusCode)
}

func (d *EmbeddingConfigDiagnostic) checkAPIEndpoint(ctx context.Context) {
	fmt.Println("\n3. 🔗 檢查 API 端點")
	fmt.Println("-------------------")

	// 構建完整的 API URL
	apiURL := fmt.Sprintf("%s/openai/deployments/%s/embeddings?api-version=%s",
		strings.TrimSuffix(d.BaseURL, "/"),
		d.Model,
		d.APIVersion)

	fmt.Printf("測試 URL: %s\n", apiURL)

	client := gclient.New()
	client.SetTimeout(30 * time.Second)
	client.SetHeader("api-key", d.APIKey)
	client.SetHeader("Content-Type", "application/json")

	// 測試 API 端點（使用 HEAD 請求）
	resp, err := client.Head(ctx, apiURL)
	if err != nil {
		fmt.Printf("❌ API 端點測試失敗: %v\n", err)
		return
	}
	defer resp.Close()

	switch resp.StatusCode {
	case 200, 405: // 405 是正常的，因為 HEAD 請求可能不被支持
		fmt.Printf("✅ API 端點可訪問，狀態碼: %d\n", resp.StatusCode)
	case 401:
		fmt.Printf("❌ 認證失敗 (401)，請檢查 API Key\n")
	case 404:
		fmt.Printf("❌ 資源未找到 (404)，請檢查:\n")
		fmt.Printf("   - 部署名稱是否正確: %s\n", d.Model)
		fmt.Printf("   - 資源名稱是否正確: %s\n", d.BaseURL)
		fmt.Printf("   - API 版本是否正確: %s\n", d.APIVersion)
	default:
		fmt.Printf("⚠️  意外的狀態碼: %d\n", resp.StatusCode)
	}
}

func (d *EmbeddingConfigDiagnostic) testEmbeddingCall(ctx context.Context) {
	fmt.Println("\n4. 🧪 測試 Embedding 調用")
	fmt.Println("-------------------------")

	// 構建 API URL
	apiURL := fmt.Sprintf("%s/openai/deployments/%s/embeddings?api-version=%s",
		strings.TrimSuffix(d.BaseURL, "/"),
		d.Model,
		d.APIVersion)

	// 構建請求體
	requestBody := map[string]interface{}{
		"input": []string{"測試文本"},
	}

	client := gclient.New()
	client.SetTimeout(30 * time.Second)
	client.SetHeader("api-key", d.APIKey)
	client.SetHeader("Content-Type", "application/json")

	resp, err := client.Post(ctx, apiURL, requestBody)
	if err != nil {
		fmt.Printf("❌ Embedding 調用失敗: %v\n", err)
		return
	}
	defer resp.Close()

	responseBody := resp.ReadAllString()

	switch resp.StatusCode {
	case 200:
		fmt.Printf("✅ Embedding 調用成功！\n")

		// 解析響應
		jsonResp := gjson.New(responseBody)
		if data := jsonResp.Get("data"); data != nil {
			if dataArray := data.Array(); len(dataArray) > 0 {
				dataItem := gjson.New(dataArray[0])
				if embedding := dataItem.Get("embedding"); embedding != nil {
					if embArray := embedding.Array(); len(embArray) > 0 {
						fmt.Printf("   向量維度: %d\n", len(embArray))
						if len(embArray) >= 5 {
							fmt.Printf("   前5個值: %v\n", embArray[:5])
						} else {
							fmt.Printf("   所有值: %v\n", embArray)
						}
					}
				}
			}
		}
	case 401:
		fmt.Printf("❌ 認證失敗 (401): API Key 無效\n")
	case 404:
		fmt.Printf("❌ 資源未找到 (404): 部署不存在或配置錯誤\n")
		fmt.Printf("   請檢查部署名稱: %s\n", d.Model)
	case 429:
		fmt.Printf("⚠️  請求過於頻繁 (429): 請稍後重試\n")
	default:
		fmt.Printf("❌ 調用失敗，狀態碼: %d\n", resp.StatusCode)
		fmt.Printf("   響應內容: %s\n", responseBody)
	}
}
