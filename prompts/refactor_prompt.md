# 代码重构

## 目标 
撰写代码重构计划， 输出到 docs/代码重构计划.md 
## 要求
- 移除 storage 和相关代码 
- 将原来 storage 相关的功能方法 使用 AssetManagementService(ams.svc) 来进行替代 
- AssetManagementService 的 api 接口定义请参考  docs/assetManagementService_ api规格.md 
- AssetManagementService 服务的名称定义和scheme 定义从配置文件中读取  
- nacos 上的配置文件內容，請參考 docs/nacos_brainHub 配置文件.md 
- 常量的定義要放到consts.go中定義 （例如 uri 的定義） 
- 請評估目前項目中使用和 storage 相關的操作，並不一定都要使用AssetManagementService下的所有 api 
- AssetManagementService 下已經實線和附件相關的存儲功能和爬文功能。所以要看當前 project 具體要使用哪些功能。也就是說實現的部分已經在AssetManagementService 工程下實現了。如果當前項目要用只是通過api 調用即可 
- 利用 nacos 的注冊發現服務，來呼叫 AssetManagementService 的 api 接口 且要使用goframe的方式做實現。  

** 注意 ** 你要對 project 完整的看過之後進行深度思考 之後才開始。 只是產出分析報告 ，先不要修改任何代碼  

