// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package embedding

import (
	"context"

	"brainHub/api/embedding/v1"
)

type IEmbeddingV1 interface {
	GenerateEmbeddings(ctx context.Context, req *v1.GenerateEmbeddingsReq) (res *v1.GenerateEmbeddingsRes, err error)
	GetEmbeddingHealth(ctx context.Context, req *v1.GetEmbeddingHealthReq) (res *v1.GetEmbeddingHealthRes, err error)
	GetEmbeddingMetrics(ctx context.Context, req *v1.GetEmbeddingMetricsReq) (res *v1.GetEmbeddingMetricsRes, err error)
}
