package v1

import (
	"brainHub/internal/model/embedding"
	"github.com/gogf/gf/v2/frame/g"
)

// BaseRes 基礎響應結構
type BaseRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// GenerateEmbeddingsReq 生成向量請求
type GenerateEmbeddingsReq struct {
	g.Meta    `path:"/v1/embedding/generate" method:"post" tags:"Embedding" summary:"Generate text embeddings"`
	TenantID  string   `json:"tenant_id" v:"required#租戶ID不能為空"`
	ServiceID string   `json:"service_id" v:"required#服務ID不能為空"`
	Texts     []string `json:"texts" v:"required#文本列表不能為空"`
	Model     string   `json:"model,omitempty" dc:"可選：指定使用的模型"`
}

// GenerateEmbeddingsRes 生成向量響應
type GenerateEmbeddingsRes struct {
	BaseRes
	Data *GenerateEmbeddingsResData `json:"data"`
}

// GenerateEmbeddingsResData 生成向量響應數據
type GenerateEmbeddingsResData struct {
	Embeddings  [][]float32     `json:"embeddings"`   // 生成的向量列表
	Model       string          `json:"model"`        // 實際使用的模型名稱
	Provider    string          `json:"provider"`     // 提供商名稱
	Usage       embedding.Usage `json:"usage"`        // Token 使用統計
	ProcessTime int64           `json:"process_time"` // 處理時間（毫秒）
	Dimensions  int             `json:"dimensions"`   // 向量維度
}

// GetEmbeddingHealthReq 獲取健康狀態請求
type GetEmbeddingHealthReq struct {
	g.Meta    `path:"/v1/embedding/health" method:"post" tags:"Embedding" summary:"Check embedding service health"`
	TenantID  string `json:"tenant_id" v:"required#租戶ID不能為空"`
	ServiceID string `json:"service_id" v:"required#服務ID不能為空"`
}

// GetEmbeddingHealthRes 獲取健康狀態響應
type GetEmbeddingHealthRes struct {
	BaseRes
	Data *GetEmbeddingHealthResData `json:"data"`
}

// GetEmbeddingHealthResData 健康狀態響應數據
type GetEmbeddingHealthResData struct {
	Healthy   bool   `json:"healthy"`    // 是否健康
	Provider  string `json:"provider"`   // 提供商名稱
	Model     string `json:"model"`      // 模型名稱
	CheckTime int64  `json:"check_time"` // 檢查時間戳
}

// GetEmbeddingMetricsReq 獲取性能指標請求
type GetEmbeddingMetricsReq struct {
	g.Meta    `path:"/v1/embedding/metrics" method:"post" tags:"Embedding" summary:"Get embedding service metrics"`
	TenantID  string `json:"tenant_id" v:"required#租戶ID不能為空"`
	ServiceID string `json:"service_id" v:"required#服務ID不能為空"`
}

// GetEmbeddingMetricsRes 獲取性能指標響應
type GetEmbeddingMetricsRes struct {
	BaseRes
	Data *embedding.EmbeddingMetrics `json:"data"`
}
