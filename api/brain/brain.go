// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package brain

import (
	"context"

	"brainHub/api/brain/v1"
)

type IBrainV1 interface {
	Chat(ctx context.Context, req *v1.ChatReq) (res *v1.ChatRes, err error)
	ChatWithAttachment(ctx context.Context, req *v1.ChatWithAttachmentReq) (res *v1.ChatWithAttachmentRes, err error)
	GenerateContent(ctx context.Context, req *v1.GenerateContentReq) (res *v1.GenerateContentRes, err error)
}
