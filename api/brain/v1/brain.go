package v1

import (
	"brainHub/internal/model/llm"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type BaseRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}
type ChatReq struct {
	g.Meta      `path:"/v1/line/chat" method:"post" tags:"brain" summary:"chat with llm"`
	TenantID    string `json:"tenant_id"`
	UserID      string `json:"user_id"`
	ServiceID   string `json:"service_id"`
	Message     string `json:"message"`
	Channel     string `json:"channel"`
	DisplayName string `json:"display_name"`
}

type ChatRes struct {
	BaseRes
	*llm.ResponseData
}

type ChatWithAttachmentReq struct {
	g.Meta     `path:"/v1/line/chat/attachment" method:"post" mime:"multipart/form-data" tags:"brain" summary:"chat with attachment "`
	Attachment *ghttp.UploadFile `json:"attachment" type:"file" dc:"attachment file"`
	UserID     string            `json:"user_id"`
	ServiceID  string            `json:"service_id"`
	TenantID   string            `json:"tenant_id"`
	Mime       string            `json:"mime"`
	Channel    string            `json:"channel"`
}
type ChatWithAttachmentRes struct {
	BaseRes
	*llm.ResponseData
}

type GenerateContentReq struct {
	g.Meta    `path:"/v1/brain/generate_content" tags:"Brain" method:"post" summary:"Generate content using unified LLM interface"`
	TenantID  string `json:"tenant_id" v:"required#租戶ID不能為空"`
	ServiceID string `json:"service_id" v:"required#服務ID不能為空"`
	Channel   string `json:"channel" v:"required#渠道標識不能為空"`
	Prompt    string `json:"prompt" v:"required#提示詞不能為空"`
}

type GenerateContentRes struct {
	Code    int                     `json:"code"`
	Message string                  `json:"message"`
	Data    *GenerateContentResData `json:"data"`
}

type GenerateContentResData struct {
	LLMName           string   `json:"llm_name"`                   // 使用的模型名稱
	OutputContent     string   `json:"output_content"`             // 生成的內容
	InputTokens       int32    `json:"input_tokens"`               // 輸入 Token 數量
	OutputTokens      int32    `json:"output_tokens"`              // 輸出 Token 數量
	TotalTokens       int32    `json:"total_tokens"`               // 總 Token 數量
	ContinuationCount int      `json:"continuation_count"`         // 續寫次數
	IsComplete        bool     `json:"is_complete"`                // 內容是否完整
	GenerationTime    int64    `json:"generation_time"`            // 生成耗時（毫秒）
	ThinkingProcess   string   `json:"thinking_process,omitempty"` // 思考過程（僅 Gemini）
	SafetyWarnings    []string `json:"safety_warnings,omitempty"`  // 安全警告
	FinishReason      string   `json:"finish_reason,omitempty"`    // 完成原因
}
