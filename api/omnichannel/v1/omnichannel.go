package v1

import "github.com/gogf/gf/v2/frame/g"

type BaseRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type ChatReq struct {
	g.Meta                `path:"/v1/omnichannel/chat" method:"post" tags:"omnichannel" summary:"chat with llm"`
	TenantID              string   `json:"tenant_id" v:"required" dc:"tenant id"`
	Question              string   `json:"question" v:"required"`
	Source                []string `json:"source" `
	Version               []string `json:"version" d:"*"`
	Tags                  []string `json:"tags" ,d:"*"`
	Status                string   `json:"status" d:"online"`
	Channel               string   `json:"channel"`
	ServiceId             string   `json:"service_id"`
	SessionId             string   `json:"session_id"`
	MessageType           string   `json:"message_type"`
	UserId                string   `json:"user_id"`
	SensitiveWordsEnabled bool     `json:"sensitive_words_enabled"`
	VoiceParams           string   `json:"voice_params"`
}

type ChatRes struct {
	g.Meta `mime:"application/json"`
	BaseRes
	Answer           string   `json:"answer"`
	Hit              bool     `json:"hit"`
	Sensitive        bool     `json:"sensitive"`
	RelID            []string `json:"rel_id"`
	AnswerFrom       string   `json:"answer_from"`
	SequenceId       string   `json:"sequence_id"`
	AnswerFromSource string   `json:"answer_from_source"`
}
