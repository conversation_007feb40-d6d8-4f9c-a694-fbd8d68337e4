// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package omnichannel

import (
	"context"

	"brainHub/api/omnichannel/v1"
)

type IOmnichannelV1 interface {
	Chat(ctx context.Context, req *v1.ChatReq) (res *v1.ChatRes, err error)
}
