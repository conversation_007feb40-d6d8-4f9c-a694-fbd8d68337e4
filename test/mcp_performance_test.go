package test

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"
	"github.com/gogf/gf/v2/test/gtest"
)

// BenchmarkMCPToolManager_GetToolDefinitions 測試獲取工具定義的性能
func BenchmarkMCPToolManager_GetToolDefinitions(b *testing.B) {
	ctx := context.Background()
	
	config := &mcp.MCPConfig{
		Enabled: false, // 禁用以避免實際連接
		Servers: []mcp.MCPServerConfig{},
		Global: mcp.MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:          "5m",
			LogLevel:          "info",
		},
	}
	
	manager := mcp.NewMCPToolManager(config)
	_ = manager.Initialize(ctx)
	defer manager.Close()
	
	b.ResetTimer()
	b.Report<PERSON>llocs()
	
	for i := 0; i < b.N; i++ {
		_, _ = manager.GetToolDefinitions(ctx)
	}
}

// BenchmarkMCPToolManager_CallTool 測試工具調用的性能
func BenchmarkMCPToolManager_CallTool(b *testing.B) {
	ctx := context.Background()
	
	config := &mcp.MCPConfig{
		Enabled: false,
		Servers: []mcp.MCPServerConfig{},
		Global: mcp.MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:          "5m",
			LogLevel:          "info",
		},
	}
	
	manager := mcp.NewMCPToolManager(config)
	_ = manager.Initialize(ctx)
	defer manager.Close()
	
	args := map[string]interface{}{
		"param1": "value1",
		"param2": 42,
	}
	
	b.ResetTimer()
	b.ReportAllocs()
	
	for i := 0; i < b.N; i++ {
		_, _ = manager.CallTool(ctx, "nonexistent.tool", args)
	}
}

// BenchmarkMCPClientManager_GetAllClients 測試獲取所有客戶端的性能
func BenchmarkMCPClientManager_GetAllClients(b *testing.B) {
	config := &mcp.MCPConfig{
		Enabled: false,
		Servers: []mcp.MCPServerConfig{},
		Global: mcp.MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:          "5m",
			LogLevel:          "info",
		},
	}
	
	manager := mcp.NewMCPClientManager(config)
	defer manager.Close()
	
	b.ResetTimer()
	b.ReportAllocs()
	
	for i := 0; i < b.N; i++ {
		_ = manager.GetAllClients()
	}
}

// TestMCPConcurrentAccess 測試並發訪問性能
func TestMCPConcurrentAccess(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		config := &mcp.MCPConfig{
			Enabled: false,
			Servers: []mcp.MCPServerConfig{},
			Global: mcp.MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 100, // 增加並發限制
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		manager := mcp.NewMCPToolManager(config)
		err := manager.Initialize(ctx)
		t.AssertNil(err)
		defer manager.Close()
		
		// 並發測試參數
		numGoroutines := 100
		numOperationsPerGoroutine := 10
		
		var wg sync.WaitGroup
		startTime := time.Now()
		
		// 啟動多個 goroutine 進行並發測試
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				
				for j := 0; j < numOperationsPerGoroutine; j++ {
					// 測試獲取工具定義
					_, err := manager.GetToolDefinitions(ctx)
					if err != nil {
						t.Logf("Goroutine %d operation %d failed: %v", id, j, err)
					}
					
					// 測試工具調用
					args := map[string]interface{}{
						"test_param": fmt.Sprintf("value_%d_%d", id, j),
					}
					_, _ = manager.CallTool(ctx, "test.tool", args)
					
					// 測試健康檢查
					_ = manager.HealthCheck(ctx)
				}
			}(i)
		}
		
		// 等待所有 goroutine 完成
		wg.Wait()
		duration := time.Since(startTime)
		
		totalOperations := numGoroutines * numOperationsPerGoroutine * 3 // 3 operations per iteration
		operationsPerSecond := float64(totalOperations) / duration.Seconds()
		
		t.Logf("Concurrent test completed:")
		t.Logf("- Goroutines: %d", numGoroutines)
		t.Logf("- Operations per goroutine: %d", numOperationsPerGoroutine)
		t.Logf("- Total operations: %d", totalOperations)
		t.Logf("- Duration: %v", duration)
		t.Logf("- Operations per second: %.2f", operationsPerSecond)
		
		// 驗證性能要求（示例：至少 1000 ops/sec）
		t.Assert(operationsPerSecond > 1000, fmt.Sprintf("Performance too low: %.2f ops/sec", operationsPerSecond))
	})
}

// TestMCPMemoryUsage 測試內存使用情況
func TestMCPMemoryUsage(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 記錄初始內存使用
		var m1 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)
		
		config := &mcp.MCPConfig{
			Enabled: false,
			Servers: []mcp.MCPServerConfig{},
			Global: mcp.MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		// 創建多個管理器實例
		managers := make([]*mcp.MCPToolManager, 100)
		for i := 0; i < 100; i++ {
			managers[i] = mcp.NewMCPToolManager(config)
			_ = managers[i].Initialize(ctx)
		}
		
		// 執行一些操作
		for i := 0; i < 100; i++ {
			for j := 0; j < 10; j++ {
				_, _ = managers[i].GetToolDefinitions(ctx)
				_, _ = managers[i].CallTool(ctx, "test.tool", map[string]interface{}{"test": "value"})
			}
		}
		
		// 記錄操作後的內存使用
		var m2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m2)
		
		// 清理資源
		for i := 0; i < 100; i++ {
			_ = managers[i].Close()
		}
		
		// 記錄清理後的內存使用
		var m3 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m3)
		
		memoryUsed := m2.Alloc - m1.Alloc
		memoryFreed := m2.Alloc - m3.Alloc
		
		t.Logf("Memory usage analysis:")
		t.Logf("- Initial memory: %d bytes", m1.Alloc)
		t.Logf("- After operations: %d bytes", m2.Alloc)
		t.Logf("- After cleanup: %d bytes", m3.Alloc)
		t.Logf("- Memory used: %d bytes (%.2f MB)", memoryUsed, float64(memoryUsed)/1024/1024)
		t.Logf("- Memory freed: %d bytes (%.2f MB)", memoryFreed, float64(memoryFreed)/1024/1024)
		
		// 驗證內存使用合理（示例：不超過 100MB）
		maxMemoryMB := float64(100)
		actualMemoryMB := float64(memoryUsed) / 1024 / 1024
		t.Assert(actualMemoryMB < maxMemoryMB, 
			fmt.Sprintf("Memory usage too high: %.2f MB (max: %.2f MB)", actualMemoryMB, maxMemoryMB))
	})
}

// TestMCPCachePerformance 測試緩存性能
func TestMCPCachePerformance(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		config := &mcp.MCPConfig{
			Enabled: false,
			Servers: []mcp.MCPServerConfig{},
			Global: mcp.MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "1m", // 短緩存時間用於測試
				LogLevel:          "info",
			},
		}
		
		manager := mcp.NewMCPToolManager(config)
		err := manager.Initialize(ctx)
		t.AssertNil(err)
		defer manager.Close()
		
		// 測試緩存命中性能
		numRequests := 1000
		
		// 第一次調用（緩存未命中）
		startTime := time.Now()
		for i := 0; i < numRequests; i++ {
			_, _ = manager.GetToolDefinitions(ctx)
		}
		firstCallDuration := time.Since(startTime)
		
		// 第二次調用（緩存命中）
		startTime = time.Now()
		for i := 0; i < numRequests; i++ {
			_, _ = manager.GetToolDefinitions(ctx)
		}
		cachedCallDuration := time.Since(startTime)
		
		t.Logf("Cache performance test:")
		t.Logf("- First call duration: %v", firstCallDuration)
		t.Logf("- Cached call duration: %v", cachedCallDuration)
		t.Logf("- Speed improvement: %.2fx", float64(firstCallDuration)/float64(cachedCallDuration))
		
		// 驗證緩存確實提高了性能
		t.Assert(cachedCallDuration < firstCallDuration, "Cache should improve performance")
	})
}

// BenchmarkMCPToolManager_HealthCheck 測試健康檢查性能
func BenchmarkMCPToolManager_HealthCheck(b *testing.B) {
	ctx := context.Background()
	
	config := &mcp.MCPConfig{
		Enabled: false,
		Servers: []mcp.MCPServerConfig{},
		Global: mcp.MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:          "5m",
			LogLevel:          "info",
		},
	}
	
	manager := mcp.NewMCPToolManager(config)
	_ = manager.Initialize(ctx)
	defer manager.Close()
	
	b.ResetTimer()
	b.ReportAllocs()
	
	for i := 0; i < b.N; i++ {
		_ = manager.HealthCheck(ctx)
	}
}

// TestMCPResourceLimits 測試資源限制
func TestMCPResourceLimits(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		config := &mcp.MCPConfig{
			Enabled: false,
			Servers: []mcp.MCPServerConfig{},
			Global: mcp.MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "1s", // 短超時時間
				MaxConcurrentCalls: 5,    // 低並發限制
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		manager := mcp.NewMCPToolManager(config)
		err := manager.Initialize(ctx)
		t.AssertNil(err)
		defer manager.Close()
		
		// 測試並發限制
		var wg sync.WaitGroup
		numGoroutines := 10 // 超過 MaxConcurrentCalls
		
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				_, _ = manager.GetToolDefinitions(ctx)
			}()
		}
		
		wg.Wait()
		
		// 測試應該成功完成，即使超過並發限制
		// 實際的限制應該在客戶端管理器中實現
		t.Log("Resource limits test completed successfully")
	})
}
