2025-07-07T16:46:50.015+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:46:50.015+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:46:50.015+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-013a79bc-66d0-43e4-a343-50950aa30bcf)
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-013a79bc-66d0-43e4-a343-50950aa30bcf try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:46:50.140+0800	INFO	rpc/rpc_client.go:337	config-0-013a79bc-66d0-43e4-a343-50950aa30bcf success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878010126_192.168.3.3_61362
2025-07-07T16:46:50.140+0800	INFO	rpc/rpc_client.go:486	config-0-013a79bc-66d0-43e4-a343-50950aa30bcf notify connected event to listeners , connectionId=1751878010126_192.168.3.3_61362
2025-07-07T16:46:50.141+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:53:51.930+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:53:51.931+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ca3d12db-3993-498a-9f78-16d63b862c72)
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ca3d12db-3993-498a-9f78-16d63b862c72 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:53:52.049+0800	INFO	rpc/rpc_client.go:337	config-0-ca3d12db-3993-498a-9f78-16d63b862c72 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878432012_192.168.3.3_62029
2025-07-07T16:53:52.050+0800	INFO	rpc/rpc_client.go:486	config-0-ca3d12db-3993-498a-9f78-16d63b862c72 notify connected event to listeners , connectionId=1751878432012_192.168.3.3_62029
2025-07-07T16:53:52.050+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:54:39.201+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:54:39.202+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a7219eea-a68f-4213-a629-05adb2195eb6)
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a7219eea-a68f-4213-a629-05adb2195eb6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:54:39.326+0800	INFO	rpc/rpc_client.go:337	config-0-a7219eea-a68f-4213-a629-05adb2195eb6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878479289_192.168.3.3_62130
2025-07-07T16:54:39.329+0800	INFO	rpc/rpc_client.go:486	config-0-a7219eea-a68f-4213-a629-05adb2195eb6 notify connected event to listeners , connectionId=1751878479289_192.168.3.3_62130
2025-07-07T16:54:39.329+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:55:24.521+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:55:24.521+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d)
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:55:24.647+0800	INFO	rpc/rpc_client.go:337	config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878524610_192.168.3.3_62221
2025-07-07T16:55:24.647+0800	INFO	rpc/rpc_client.go:486	config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d notify connected event to listeners , connectionId=1751878524610_192.168.3.3_62221
2025-07-07T16:55:24.647+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:56:32.397+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:56:32.398+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ba565615-185a-4b7a-8061-04975ca6fc89)
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ba565615-185a-4b7a-8061-04975ca6fc89 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:56:32.522+0800	INFO	rpc/rpc_client.go:337	config-0-ba565615-185a-4b7a-8061-04975ca6fc89 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878592485_192.168.3.3_62342
2025-07-07T16:56:32.523+0800	INFO	rpc/rpc_client.go:486	config-0-ba565615-185a-4b7a-8061-04975ca6fc89 notify connected event to listeners , connectionId=1751878592485_192.168.3.3_62342
2025-07-07T16:56:32.523+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-17T16:45:12.686+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-17T16:45:12.687+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-771d20a5-909c-46f9-8376-db278de855db)
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-771d20a5-909c-46f9-8376-db278de855db try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T16:45:12.803+0800	INFO	rpc/rpc_client.go:337	config-0-771d20a5-909c-46f9-8376-db278de855db success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752741912782_192.168.3.3_52946
2025-07-17T16:45:12.804+0800	INFO	rpc/rpc_client.go:486	config-0-771d20a5-909c-46f9-8376-db278de855db notify connected event to listeners , connectionId=1752741912782_192.168.3.3_52946
2025-07-17T16:45:12.804+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-08-05T14:04:22.952+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-05T14:04:22.953+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-49570744-85e8-422c-8ed5-6dbb153b0a40)
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-05T14:04:23.077+0800	INFO	rpc/rpc_client.go:337	config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1754373863006_192.168.3.5_60930
2025-08-05T14:04:23.077+0800	INFO	rpc/rpc_client.go:486	config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 notify connected event to listeners , connectionId=1754373863006_192.168.3.5_60930
2025-08-05T14:04:23.077+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
