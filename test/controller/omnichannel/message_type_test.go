package omnichannel

import (
	"context"
	"testing"

	v1 "brainHub/api/omnichannel/v1"
	"brainHub/internal/consts"
	"brainHub/internal/controller/omnichannel"
	"brainHub/utility"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMessageTypeProcessing 測試不同 MessageType 的處理邏輯
func TestMessageTypeProcessing(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := &omnichannel.ControllerV1{}

		// 測試 text 類型消息
		textReq := &v1.ChatReq{
			TenantID:    "test_tenant",
			ServiceId:   "test_service",
			UserId:      "test_user",
			Channel:     "test_channel",
			Question:    "Hello, this is a text message",
			MessageType: consts.MesssageTypeText,
		}

		// 驗證請求結構
		t.AssertNE(textReq, nil)
		t.Assert(textReq.MessageType, consts.MesssageTypeText)
		t.Assert(textReq.Question, "Hello, this is a text message")

		// 測試 image 類型消息
		imageReq := &v1.ChatReq{
			TenantID:    "test_tenant",
			ServiceId:   "test_service",
			UserId:      "test_user",
			Channel:     "test_channel",
			Question:    "https://example.com/test.jpg", // 模擬圖片 URL
			MessageType: consts.MessageTypeImage,
		}

		t.AssertNE(imageReq, nil)
		t.Assert(imageReq.MessageType, consts.MessageTypeImage)

		// 測試 voice 類型消息
		voiceReq := &v1.ChatReq{
			TenantID:    "test_tenant",
			ServiceId:   "test_service",
			UserId:      "test_user",
			Channel:     "test_channel",
			Question:    "https://example.com/test.mp3", // 模擬音頻 URL
			MessageType: consts.MessageTypeVoice,
		}

		t.AssertNE(voiceReq, nil)
		t.Assert(voiceReq.MessageType, consts.MessageTypeVoice)
	})
}

// TestDownloadFileFromURL 測試文件下載功能
func TestDownloadFileFromURL(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試參數驗證
		_, _, err := utility.DownloadFileFromURL(ctx, "", consts.MessageTypeImage)
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "download URL cannot be empty")

		_, _, err = utility.DownloadFileFromURL(ctx, "https://example.com/test.jpg", "")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "message type cannot be empty")

		// 測試無效的消息類型
		_, _, err = utility.DownloadFileFromURL(ctx, "https://example.com/test.jpg", "invalid_type")
		t.AssertNE(err, nil)
	})
}

// TestIsValidFileType 測試文件類型驗證
// 注意：這個函數在 utility 包中是私有的，這裡只是測試邏輯
func TestIsValidFileType(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 由於 isValidFileType 是私有函數，我們只能測試公開的行為
		// 這裡主要測試常量的正確性

		// 測試圖片格式常量
		t.Assert(len(consts.PhotoFormat) > 0, true)
		found := false
		for _, format := range consts.PhotoFormat {
			if format == "image/jpeg" {
				found = true
				break
			}
		}
		t.Assert(found, true)

		// 測試音頻格式常量
		t.Assert(len(consts.AudioFormat) > 0, true)
		found = false
		for _, format := range consts.AudioFormat {
			if format == "audio/mp3" {
				found = true
				break
			}
		}
		t.Assert(found, true)
	})
}

// TestProcessedContent 測試處理後內容結構
func TestProcessedContent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 由於 ProcessedContent 是在 controller 包中定義的，我們無法直接訪問
		// 這裡測試相關的常量和邏輯

		// 測試內容類型常量
		t.Assert(consts.ContentTypeText, "text")
		t.Assert(consts.ContentMediaFile, "media")

		// 測試消息類型常量
		t.Assert(consts.MesssageTypeText, "text")
		t.Assert(consts.MessageTypeImage, "image")
		t.Assert(consts.MessageTypeVoice, "voice")
	})
}

// TestMessageTypeConstants 測試消息類型常量
func TestMessageTypeConstants(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 驗證消息類型常量的正確性
		t.Assert(consts.MesssageTypeText, "text")
		t.Assert(consts.MessageTypeImage, "image")
		t.Assert(consts.MessageTypeVoice, "voice")

		// 驗證內容類型常量
		t.Assert(consts.ContentTypeText, "text")
		t.Assert(consts.ContentMediaFile, "media")
	})
}
