package test

import (
	"brainHub/internal/llms/aoai"
	"brainHub/internal/llms/claude"
	claudeModel "brainHub/internal/model/claude"
	"context"
	"sync"
	"testing"
	"time"

	"github.com/gogf/gf/v2/container/garray"
)

// BenchmarkAoAiHistoryOperations 測試 AoAi 歷史記錄操作性能
func BenchmarkAoAiHistoryOperations(b *testing.B) {
	aoaiInstance := aoai.New().(*aoai.AoAi)
	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 模擬添加消息到歷史記錄
			// 注意：這裡我們無法直接訪問 history 欄位，因為它是私有的
			// 但我們可以通過其他方法間接測試性能
			aoaiInstance.Release(ctx)
			aoaiInstance = aoai.New().(*aoai.AoAi)
		}
	})
}

// BenchmarkClaudeHistoryOperations 測試 Claude 歷史記錄操作性能
func BenchmarkClaudeHistoryOperations(b *testing.B) {
	claudeInstance := claude.New().(*claude.Claude)
	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 模擬添加消息到歷史記錄
			claudeInstance.Release(ctx)
			claudeInstance = claude.New().(*claude.Claude)
		}
	})
}

// BenchmarkGArrayOperations 測試 garray.Array 操作性能
func BenchmarkGArrayOperations(b *testing.B) {
	arr := garray.New(true)

	testMessage := claudeModel.ClaudeMessage{
		Role: "user",
		Content: []claudeModel.ContentPart{
			{
				Type: "text",
				Text: "Test message for performance benchmark",
			},
		},
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			arr.Append(testMessage)
			_ = arr.Len()
			_ = arr.Slice()
		}
	})
}

// BenchmarkConcurrentAccess 測試併發訪問性能
func BenchmarkConcurrentAccess(b *testing.B) {
	arr := garray.New(true)

	testMessage := claudeModel.ClaudeMessage{
		Role: "user",
		Content: []claudeModel.ContentPart{
			{
				Type: "text",
				Text: "Concurrent test message",
			},
		},
	}

	// 預填充一些數據
	for i := 0; i < 100; i++ {
		arr.Append(testMessage)
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 混合讀寫操作
			switch b.N % 4 {
			case 0:
				arr.Append(testMessage)
			case 1:
				_ = arr.Len()
			case 2:
				_ = arr.Slice()
			case 3:
				if arr.Len() > 50 {
					arr.Clear()
				}
			}
		}
	})
}

// BenchmarkDeadlockPrevention 測試死鎖預防性能
func BenchmarkDeadlockPrevention(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var wg sync.WaitGroup
		numGoroutines := 10

		for j := 0; j < numGoroutines; j++ {
			wg.Add(1)
			go func() {
				defer wg.Done()

				// 模擬複雜操作
				for k := 0; k < 10; k++ {
					// 測試併發安全操作
					time.Sleep(time.Microsecond)
				}
			}()
		}

		wg.Wait()
	}
}

// TestPerformanceComparison 性能對比測試
func TestPerformanceComparison(t *testing.T) {
	// 測試 garray.Array 的性能特性
	arr := garray.New(true)

	// 測試大量併發寫入
	start := time.Now()
	var wg sync.WaitGroup
	numGoroutines := 100
	operationsPerGoroutine := 1000

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			testMessage := claudeModel.ClaudeMessage{
				Role: "user",
				Content: []claudeModel.ContentPart{
					{
						Type: "text",
						Text: "Performance test message",
					},
				},
			}

			for j := 0; j < operationsPerGoroutine; j++ {
				arr.Append(testMessage)
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	expectedOperations := numGoroutines * operationsPerGoroutine
	actualOperations := arr.Len()

	t.Logf("Performance test completed:")
	t.Logf("- Duration: %v", duration)
	t.Logf("- Expected operations: %d", expectedOperations)
	t.Logf("- Actual operations: %d", actualOperations)
	t.Logf("- Operations per second: %.2f", float64(actualOperations)/duration.Seconds())

	if actualOperations != expectedOperations {
		t.Errorf("Expected %d operations, got %d", expectedOperations, actualOperations)
	}
}

// TestMemoryUsage 記憶體使用測試
func TestMemoryUsage(t *testing.T) {
	// 創建多個 LLM 實例並測試記憶體使用
	instances := make([]interface{}, 0, 100)

	// 創建 AoAi 實例
	for i := 0; i < 50; i++ {
		instances = append(instances, aoai.New())
	}

	// 創建 Claude 實例
	for i := 0; i < 50; i++ {
		instances = append(instances, claude.New())
	}

	// 釋放所有實例
	ctx := context.Background()
	for _, instance := range instances {
		if llm, ok := instance.(interface{ Release(context.Context) }); ok {
			llm.Release(ctx)
		}
	}

	t.Logf("Created and released %d LLM instances successfully", len(instances))
}
