package test

import (
	"context"
	"testing"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestConfigFormat_MCPServerTypes 测试不同类型的 MCP 服务器配置
func TestConfigFormat_MCPServerTypes(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试 STDIO 类型服务器配置
		stdioConfig := mcp.MCPServerConfig{
			Name:        "filesystem",
			Type:        "stdio",
			Description: "文件系統操作工具",
			Command:     "mcp-server-filesystem",
			Args:        []string{"--root", "/workspace", "--readonly"},
			Env:         []string{"PATH=/usr/local/bin:/usr/bin:/bin", "HOME=/home/<USER>"},
			Timeout:     "30s",
			RetryCount:  3,
		}

		// 验证 STDIO 配置
		t.AssertEQ(stdioConfig.Name, "filesystem")
		t.Asser<PERSON>(stdioConfig.Type, "stdio")
		t.AssertEQ(stdioConfig.Command, "mcp-server-filesystem")
		t.AssertEQ(len(stdioConfig.Args), 3)
		t.AssertEQ(len(stdioConfig.Env), 2)

		// 测试 HTTP 类型服务器配置
		httpConfig := mcp.MCPServerConfig{
			Name:        "web-search",
			Type:        "http",
			Description: "網頁搜索服務",
			URL:         "https://api.search.example.com/mcp",
			Headers: map[string]string{
				"Authorization": "Bearer ${MCP_SEARCH_TOKEN}",
				"Content-Type":  "application/json",
				"User-Agent":    "brainHub-MCP/1.0",
			},
			Timeout:    "60s",
			RetryCount: 3,
		}

		// 验证 HTTP 配置
		t.AssertEQ(httpConfig.Name, "web-search")
		t.AssertEQ(httpConfig.Type, "http")
		t.AssertEQ(httpConfig.URL, "https://api.search.example.com/mcp")
		t.AssertEQ(len(httpConfig.Headers), 3)

		// 测试 SSE 类型服务器配置
		sseConfig := mcp.MCPServerConfig{
			Name:        "realtime-monitor",
			Type:        "sse",
			Description: "實時監控數據流",
			URL:         "https://monitor.example.com/events",
			Headers: map[string]string{
				"Authorization": "Bearer ${MONITOR_TOKEN}",
				"Accept":        "text/event-stream",
				"Cache-Control": "no-cache",
			},
			Timeout:    "300s",
			RetryCount: 1,
		}

		// 验证 SSE 配置
		t.AssertEQ(sseConfig.Name, "realtime-monitor")
		t.AssertEQ(sseConfig.Type, "sse")
		t.AssertEQ(sseConfig.URL, "https://monitor.example.com/events")
		t.AssertEQ(sseConfig.Headers["Accept"], "text/event-stream")
	})
}

// TestConfigFormat_GlobalConfig 测试全局配置格式
func TestConfigFormat_GlobalConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		globalConfig := mcp.MCPGlobalConfig{
			Enabled:            true,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 20,
			CacheTTL:           "10m",
			LogLevel:           "info",
		}

		// 验证全局配置
		t.AssertEQ(globalConfig.Enabled, true)
		t.AssertEQ(globalConfig.DefaultTimeout, "30s")
		t.AssertEQ(globalConfig.MaxConcurrentCalls, 20)
		t.AssertEQ(globalConfig.CacheTTL, "10m")
		t.AssertEQ(globalConfig.LogLevel, "info")
	})
}

// TestConfigFormat_CompleteConfig 测试完整的配置结构
func TestConfigFormat_CompleteConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建完整的 MCP 配置，模拟用户提供的配置文件格式
		completeConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "filesystem",
					Type:        "stdio",
					Description: "文件系統操作工具",
					Command:     "mcp-server-filesystem",
					Args:        []string{"--root", "/workspace", "--readonly"},
					Env:         []string{"PATH=/usr/local/bin:/usr/bin:/bin", "HOME=/home/<USER>", "MCP_LOG_LEVEL=info"},
					Timeout:     "30s",
					RetryCount:  3,
				},
				{
					Name:        "calculator",
					Type:        "stdio",
					Description: "數學計算工具",
					Command:     "python3",
					Args:        []string{"-m", "mcp_calculator_server"},
					Env:         []string{"PYTHONPATH=/opt/mcp/lib"},
					Timeout:     "15s",
					RetryCount:  2,
				},
				{
					Name:        "web-search",
					Type:        "http",
					Description: "網頁搜索服務",
					URL:         "https://api.search.example.com/mcp",
					Headers: map[string]string{
						"Authorization": "Bearer ${MCP_SEARCH_TOKEN}",
						"Content-Type":  "application/json",
						"User-Agent":    "brainHub-MCP/1.0",
						"X-API-Version": "v1",
					},
					Timeout:    "60s",
					RetryCount: 3,
				},
				{
					Name:        "realtime-monitor",
					Type:        "sse",
					Description: "實時監控數據流",
					URL:         "https://monitor.example.com/events",
					Headers: map[string]string{
						"Authorization": "Bearer ${MONITOR_TOKEN}",
						"Accept":        "text/event-stream",
						"Cache-Control": "no-cache",
					},
					Timeout:    "300s",
					RetryCount: 1,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 20,
				CacheTTL:           "10m",
				LogLevel:           "info",
			},
		}

		// 验证完整配置
		t.AssertEQ(completeConfig.Enabled, true)
		t.AssertEQ(len(completeConfig.Servers), 4)

		// 验证各种类型的服务器
		stdioServers := 0
		httpServers := 0
		sseServers := 0

		for _, server := range completeConfig.Servers {
			switch server.Type {
			case "stdio":
				stdioServers++
			case "http":
				httpServers++
			case "sse":
				sseServers++
			}
		}

		t.AssertEQ(stdioServers, 2)
		t.AssertEQ(httpServers, 1)
		t.AssertEQ(sseServers, 1)

		// 验证配置验证功能
		err := mcp.ValidateConfig(completeConfig)
		t.AssertNil(err)
	})
}

// TestConfigFormat_EnvironmentVariables 测试环境变量支持
func TestConfigFormat_EnvironmentVariables(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		configManager := mcp.NewConfigManager()

		// 创建包含环境变量的服务器配置
		serverConfig := &mcp.MCPServerConfig{
			Name:        "test-server",
			Type:        "http",
			Description: "Test server with environment variables",
			URL:         "https://api.example.com/mcp",
			Headers: map[string]string{
				"Authorization": "Bearer ${API_TOKEN}",
				"X-Client-ID":   "${CLIENT_ID}",
			},
			Timeout:    "30s",
			RetryCount: 3,
		}

		// 测试环境变量展开功能
		configManager.ExpandEnvironmentVariables(serverConfig)

		// 验证环境变量处理
		t.AssertNE(serverConfig.Headers, nil)
		t.AssertEQ(len(serverConfig.Headers), 2)

		// 注意：在测试环境中，环境变量可能不存在，所以这里主要验证功能不会崩溃
		t.Log("Environment variable expansion test completed")
	})
}

// TestConfigFormat_DefaultValues 测试默认值设置
func TestConfigFormat_DefaultValues(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		configManager := mcp.NewConfigManager()

		// 通过加载配置来测试默认值设置
		// ConfigManager 在加载配置时会自动应用默认值
		ctx := context.Background()
		config, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		t.AssertNE(config, nil)

		// 验证全局配置的默认值
		t.AssertNE(config.Global.DefaultTimeout, "")
		t.AssertGT(config.Global.MaxConcurrentCalls, 0)
		t.AssertNE(config.Global.CacheTTL, "")
		t.AssertNE(config.Global.LogLevel, "")
	})
}
