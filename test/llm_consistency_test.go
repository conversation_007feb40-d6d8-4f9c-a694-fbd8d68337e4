package test

import (
	"brainHub/internal/llms/aoai"
	"brainHub/internal/llms/claude"
	"brainHub/internal/llms/gemini"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestLLMConsistency 測試所有 LLM 實現的一致性
func TestLLMConsistency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試 AoAi 實例創建
		aoaiInstance := aoai.New()
		t.AssertNE(aoaiInstance, nil)

		// 測試 Claude 實例創建
		claudeInstance := claude.New()
		t.AssertNE(claudeInstance, nil)

		// 測試 Gemini 實例創建
		geminiInstance := gemini.New()
		t.AssertNE(geminiInstance, nil)

		// 測試 Release 方法的一致性
		aoaiInstance.Release(ctx)
		claudeInstance.Release(ctx)
		geminiInstance.Release(ctx)

		t.Log("All LLM implementations created and released successfully")
	})
}

// TestConcurrentSafety 測試所有 LLM 實現的併發安全性
func TestConcurrentSafety(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建所有 LLM 實例
		aoaiInstance := aoai.New()
		claudeInstance := claude.New()
		geminiInstance := gemini.New()

		// 併發調用 Release 方法
		done := make(chan bool, 3)

		go func() {
			aoaiInstance.Release(ctx)
			done <- true
		}()

		go func() {
			claudeInstance.Release(ctx)
			done <- true
		}()

		go func() {
			geminiInstance.Release(ctx)
			done <- true
		}()

		// 等待所有 goroutine 完成
		for i := 0; i < 3; i++ {
			<-done
		}

		t.Log("All LLM implementations handled concurrent operations safely")
	})
}

// TestStructureConsistency 測試結構體的一致性
func TestStructureConsistency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 AoAi 結構體
		aoaiInstance := aoai.New().(*aoai.AoAi)
		t.AssertNE(aoaiInstance, nil)

		// 測試 Claude 結構體
		claudeInstance := claude.New().(*claude.Claude)
		t.AssertNE(claudeInstance, nil)

		// 測試 Gemini 結構體
		geminiInstance := gemini.New().(*gemini.GeminiLLM)
		t.AssertNE(geminiInstance, nil)

		t.Log("All LLM structures are consistent and properly initialized")
	})
}
