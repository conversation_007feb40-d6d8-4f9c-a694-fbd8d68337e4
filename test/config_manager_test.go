package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestConfigManager_LoadMCPConfig 测试配置管理器的配置加载功能
func TestConfigManager_LoadMCPConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		configManager := mcp.NewConfigManager()

		// 测试加载配置
		config, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		t.AssertNE(config, nil)

		// 验证配置结构
		t.AssertNE(config.Global, nil)
		t.AssertNE(config.Servers, nil)

		// 测试配置验证
		err = mcp.ValidateConfig(config)
		t.AssertNil(err)
	})
}

// TestConfigManager_Cache 测试配置缓存功能
func TestConfigManager_Cache(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		configManager := mcp.NewConfigManager()

		// 第一次加载配置
		config1, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		t.AssertNE(config1, nil)

		// 检查缓存信息
		cacheInfo := configManager.GetCacheInfo()
		t.AssertNE(cacheInfo, nil)
		t.AssertEQ(cacheInfo["has_cache"], true)

		// 第二次加载配置（应该从缓存获取）
		config2, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		t.AssertNE(config2, nil)

		// 验证是否使用了缓存
		cacheInfo = configManager.GetCacheInfo()
		t.AssertEQ(cacheInfo["has_cache"], true)
		t.AssertEQ(cacheInfo["cache_valid"], true)
	})
}

// TestConfigManager_ClearCache 测试缓存清理功能
func TestConfigManager_ClearCache(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		configManager := mcp.NewConfigManager()

		// 加载配置以创建缓存
		_, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)

		// 验证缓存存在
		cacheInfo := configManager.GetCacheInfo()
		t.AssertEQ(cacheInfo["has_cache"], true)

		// 清理缓存
		configManager.ClearCache()

		// 验证缓存已清理
		cacheInfo = configManager.GetCacheInfo()
		t.AssertEQ(cacheInfo["has_cache"], false)
	})
}

// TestConfigManager_SetCacheTTL 测试缓存TTL设置
func TestConfigManager_SetCacheTTL(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		configManager := mcp.NewConfigManager()

		// 设置新的缓存TTL
		newTTL := 10 * time.Second
		configManager.SetCacheTTL(newTTL)

		// 验证TTL设置
		cacheInfo := configManager.GetCacheInfo()
		t.AssertEQ(cacheInfo["cache_ttl"], newTTL.String())
	})
}

// TestConfigManager_DefaultConfig 测试默认配置
func TestConfigManager_DefaultConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		configManager := mcp.NewConfigManager()

		// 加载配置（在没有配置文件的情况下应该返回默认配置）
		config, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		t.AssertNE(config, nil)

		// 验证默认配置的基本属性
		t.AssertNE(config.Global.DefaultTimeout, "")
		t.AssertGT(config.Global.MaxConcurrentCalls, 0)
		t.AssertNE(config.Global.CacheTTL, "")
		t.AssertNE(config.Global.LogLevel, "")
	})
}

// TestConfigValidation 测试配置验证功能
func TestConfigValidation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试有效配置
		validConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "test-server",
					Type:        "stdio",
					Description: "Test server",
					Command:     "echo",
					Args:        []string{"hello"},
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		err := mcp.ValidateConfig(validConfig)
		t.AssertNil(err)

		// 测试无效配置（空名称）
		invalidConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "", // 空名称应该导致验证失败
					Type:        "stdio",
					Command:     "echo",
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		err = mcp.ValidateConfig(invalidConfig)
		t.AssertNE(err, nil)
	})
}

// TestConfigManager_WatchConfigChanges 测试配置变更监听
func TestConfigManager_WatchConfigChanges(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		configManager := mcp.NewConfigManager()
		callbackCalled := false

		// 设置配置变更回调
		callback := func(config *mcp.MCPConfig) {
			callbackCalled = true
		}

		// 启动配置监听
		err := configManager.WatchConfigChanges(ctx, callback)
		t.AssertNil(err)

		// 等待一段时间让监听器运行
		time.Sleep(100 * time.Millisecond)

		// 注意：在实际环境中，这里应该触发配置文件变更
		// 但在测试环境中，我们只验证监听器能正常启动
		t.AssertEQ(callbackCalled, false) // 没有配置变更，所以回调不应该被调用
	})
}
