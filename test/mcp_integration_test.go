package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPIntegration 測試 MCP 基本集成功能
func TestMCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 MCP 配置
		config := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "test-echo",
					Type:        "stdio",
					Description: "測試 echo 工具",
					Command:     "echo",
					Args:        []string{"Hello MCP!"},
					Timeout:     "10s",
					RetryCount:  2,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 5,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		// 創建工具管理器
		toolManager := mcp.NewMCPToolManager(config)
		t.AssertNE(toolManager, nil)

		// 初始化
		err := toolManager.Initialize(ctx)
		t.AssertNil(err)

		// 延遲關閉
		defer func() {
			err := toolManager.Close()
			t.AssertNil(err)
		}()

		// 獲取工具定義
		tools, err := toolManager.GetToolDefinitions(ctx)
		t.AssertNil(err)
		t.Assert(len(tools) >= 0, true) // 可能沒有工具，但不應該出錯

		// 獲取統計信息
		stats := toolManager.GetStats(ctx)
		t.AssertNE(stats, nil)

		// 健康檢查
		health := toolManager.HealthCheck(ctx)
		t.AssertNE(health, nil)
	})
}

// TestMCPClientManager 測試客戶端管理器
func TestMCPClientManager(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建配置
		config := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "test-client",
					Type:        "stdio",
					Description: "測試客戶端",
					Command:     "echo",
					Args:        []string{"test"},
					Timeout:     "5s",
					RetryCount:  1,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "10s",
				MaxConcurrentCalls: 3,
				CacheTTL:           "2m",
				LogLevel:           "debug",
			},
		}

		// 創建客戶端管理器
		clientManager := mcp.NewMCPClientManager(config)
		t.AssertNE(clientManager, nil)

		// 初始化
		err := clientManager.Initialize(ctx)
		t.AssertNil(err)

		// 延遲關閉
		defer func() {
			err := clientManager.Close()
			t.AssertNil(err)
		}()

		// 獲取所有客戶端
		clients := clientManager.GetAllClients()
		t.AssertNE(clients, nil)

		// 獲取特定客戶端
		client, err := clientManager.GetClient("test-client")
		if err == nil {
			t.AssertNE(client, nil)
		}

		// 獲取統計信息
		stats := clientManager.GetStats()
		t.AssertNE(stats, nil)

		// 健康檢查
		health := clientManager.HealthCheck(ctx)
		t.AssertNE(health, nil)
	})
}

// TestMCPConfiguration 測試配置管理
func TestMCPConfiguration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試有效配置
		validConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "valid-server",
					Type:        "stdio",
					Description: "有效的服務器配置",
					Command:     "echo",
					Args:        []string{"test"},
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		err := mcp.ValidateConfig(validConfig)
		t.AssertNil(err)

		// 測試無效配置
		invalidConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "", // 空名稱
					Type:        "stdio",
					Description: "無效的服務器配置",
					Command:     "",
					Timeout:     "invalid", // 無效超時
					RetryCount:  -1,        // 無效重試次數
				},
			},
		}

		err = mcp.ValidateConfig(invalidConfig)
		t.AssertNE(err, nil)
	})
}

// TestMCPPerformance 簡單的性能測試
func TestMCPPerformance(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建配置
		config := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "perf-test",
					Type:        "stdio",
					Description: "性能測試",
					Command:     "echo",
					Args:        []string{"performance"},
					Timeout:     "5s",
					RetryCount:  1,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "10s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "1m",
				LogLevel:           "warn",
			},
		}

		// 創建工具管理器
		toolManager := mcp.NewMCPToolManager(config)
		defer toolManager.Close()

		// 測試初始化時間
		start := time.Now()
		err := toolManager.Initialize(ctx)
		initTime := time.Since(start)

		t.AssertNil(err)
		t.Assert(initTime < 5*time.Second, true) // 初始化應該在 5 秒內完成

		// 測試工具定義獲取時間
		start = time.Now()
		_, err = toolManager.GetToolDefinitions(ctx)
		getToolsTime := time.Since(start)

		t.AssertNil(err)
		t.Assert(getToolsTime < 2*time.Second, true) // 獲取工具定義應該在 2 秒內完成

		// 測試統計信息獲取時間
		start = time.Now()
		stats := toolManager.GetStats(ctx)
		getStatsTime := time.Since(start)

		t.AssertNE(stats, nil)
		t.Assert(getStatsTime < 1*time.Second, true) // 獲取統計信息應該在 1 秒內完成
	})
}

// TestMCPErrorHandling 測試錯誤處理
func TestMCPErrorHandling(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試無效命令的配置
		config := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "invalid-command",
					Type:        "stdio",
					Description: "無效命令測試",
					Command:     "nonexistent-command-12345",
					Args:        []string{},
					Timeout:     "5s",
					RetryCount:  1,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "10s",
				MaxConcurrentCalls: 5,
				CacheTTL:           "1m",
				LogLevel:           "error",
			},
		}

		// 創建工具管理器
		toolManager := mcp.NewMCPToolManager(config)
		defer toolManager.Close()

		// 初始化應該處理錯誤而不崩潰
		err := toolManager.Initialize(ctx)
		// 可能會有錯誤，但不應該 panic
		if err != nil {
			t.Assert(err.Error() != "", true) // 錯誤信息不應該為空
		}

		// 即使有錯誤，也應該能夠獲取統計信息
		stats := toolManager.GetStats(ctx)
		t.AssertNE(stats, nil)

		// 健康檢查應該反映問題
		health := toolManager.HealthCheck(ctx)
		t.AssertNE(health, nil)
	})
}
