package test

import (
	"brainHub/internal/logic/llmRouter"
	"brainHub/internal/model"
	"context"
	"testing"

	// 導入 logic 包以確保所有服務的 init 函數被執行
	_ "brainHub/internal/logic"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestGetPayloadWithEqualTenantAndService 測試當 tenantID == serviceID 時的行為
func TestGetPayloadWithEqualTenantAndService(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		router := llmRouter.New()

		// 測試 tenantID == serviceID 的情況
		input := &model.AiSelectorInput{
			TenantID:  "test_id",
			ServiceID: "test_id", // 相同的 ID
			UserID:    "user_001",
			Channel:   "web",
		}

		// 這個調用可能會因為 payload 為 nil 而導致問題
		llmObj, err := router.Select(ctx, input)

		// 檢查結果
		if err != nil {
			t.Logf("Expected error occurred: %v", err)
			// 如果返回錯誤，這是正確的行為
			t.AssertNE(err, nil)
		} else {
			// 如果沒有錯誤，檢查 llmObj 是否正常
			t.AssertNE(llmObj, nil)
			t.Logf("LLM object created successfully despite equal tenant and service ID")
		}
	})
}

// TestGetPayloadWithDifferentTenantAndService 測試正常情況
func TestGetPayloadWithDifferentTenantAndService(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		router := llmRouter.New()

		// 測試正常情況
		input := &model.AiSelectorInput{
			TenantID:  "tenant_001",
			ServiceID: "service_001",
			UserID:    "user_001",
			Channel:   "web",
		}

		// 這個調用應該正常工作（雖然可能因為配置問題而失敗）
		llmObj, err := router.Select(ctx, input)

		// 記錄結果
		if err != nil {
			t.Logf("Error occurred (may be due to configuration): %v", err)
		} else {
			t.AssertNE(llmObj, nil)
			t.Logf("LLM object created successfully")
		}
	})
}
