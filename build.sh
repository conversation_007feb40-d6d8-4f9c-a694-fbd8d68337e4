gf build  -v 2.0 -n brainHub -a amd64 -s linux  -p ./bin

#gcloud compute scp "/Users/<USER>/Source/Ai app/brainHub/bin/linux_amd64/brainHub" ai-promotion-demo-development-ap-001:~/brainHub_V1 --zone "asia-east1-c" --project "aile-ai-development"

#gcloud compute scp "/Users/<USER>/Source/Ai app/brainHub/key/key.json" ai-promotion-demo-development-ap-001:~/brainHub_V1 --zone "asia-east1-c" --project "aile-ai-development"
#gcloud compute scp "/Users/<USER>/Source/Ai app/brainHub/manifest/config/config.yaml" ai-promotion-demo-development-ap-001:~/brainHub_V1 --zone "asia-east1-c" --project "aile-ai-development"