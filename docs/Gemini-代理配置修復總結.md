# Gemini 代理配置修復總結

## 問題描述

在需要代理的網絡環境中，Gemini 服務無法連接到 Google Cloud API，出現類似以下錯誤：

```
Post "https://us-central1-aiplatform.googleapis.com/v1beta1/projects/..."
context deadline exceeded (Client.Timeout exceeded while awaiting headers)
```

此外，還存在憑證文件優先級的問題：即使用戶在配置中指定了憑證文件路徑，系統仍可能使用環境變量中的憑證文件，導致配置不符合預期。

## 問題根因分析

### 1. 缺少代理配置
- **Gemini 實現問題**：在 `genai.NewClient` 初始化時沒有配置代理設置
- **與其他服務不一致**：Claude 已經正確實現了代理配置，但 Gemini 缺少相同的功能
- **網絡環境要求**：用戶環境需要通過代理 `http://127.0.0.1:7890` 連接外部 API

### 2. 憑證文件優先級問題
- **配置優先級混亂**：原邏輯優先檢查環境變量，而不是配置參數
- **用戶期望不符**：用戶希望配置參數中的憑證文件優先於環境變量
- **配置可控性差**：無法確保使用指定的憑證文件

### 3. Google Cloud SDK 特殊要求
- **HTTP 客戶端配置**：Google Cloud 的 genai 客戶端需要自定義 HTTP 客戶端來支持代理
- **認證流程**：Google Cloud 認證過程也需要通過代理進行
- **配置一致性**：需要與現有的 Claude 代理配置保持一致

## 解決方案

### 1. 添加代理支持
在 `internal/llms/gemini/genai.go` 中修改 `Initialize` 方法：

```go
// 檢查是否需要設置代理
vProxy, _ := g.Cfg().Get(ctx, "system.proxy")
if vProxy != nil && !vProxy.IsEmpty() {
    proxyURL, parseErr := url.Parse(vProxy.String())
    if parseErr != nil {
        m.logger().Warningf(ctx, "Failed to parse proxy URL: %v", parseErr)
    } else {
        m.logger().Infof(ctx, "Setting up proxy for Gemini client: %s", vProxy.String())
        
        // 創建帶代理的 HTTP 客戶端
        httpClient := &http.Client{
            Transport: &http.Transport{
                Proxy: http.ProxyURL(proxyURL),
            },
            Timeout: 60 * time.Second,
        }
        
        // 設置 HTTP 客戶端到配置中
        clientConfig.HTTPClient = httpClient
    }
}
```

### 2. 憑證文件優先級調整
修改憑證文件處理邏輯，優先使用配置參數：

```go
// 優先使用參數中指定的憑證文件，即使環境變量已設置
if !g.IsEmpty(params.Vertex.CredentialFile) {
    // 檢查參數指定的憑證文件是否存在
    if gfile.IsFile(params.Vertex.CredentialFile) {
        // 設置環境變量為參數指定的文件（覆蓋現有環境變量）
        err = os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", params.Vertex.CredentialFile)
        m.logger().Infof(ctx, "Set GOOGLE_APPLICATION_CREDENTIALS from config: %s", params.Vertex.CredentialFile)
    } else {
        err = gerror.Newf("credential file specified in config does not exist: %s", params.Vertex.CredentialFile)
        return
    }
} else {
    // 如果參數中沒有指定憑證文件，則檢查環境變量
    existingCredentials := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")
    if !g.IsEmpty(existingCredentials) {
        m.logger().Infof(ctx, "Using existing GOOGLE_APPLICATION_CREDENTIALS environment variable: %s", existingCredentials)
        // 驗證環境變量指向的文件是否存在
        if !gfile.IsFile(existingCredentials) {
            err = gerror.Newf("credential file from environment variable does not exist: %s", existingCredentials)
            return
        }
    } else {
        err = gerror.New("no credential file specified in config and GOOGLE_APPLICATION_CREDENTIALS environment variable is not set")
        return
    }
}
```

### 3. 配置結構調整
修改 `genai.ClientConfig` 的創建方式：

```go
// 設置代理配置，環境需要透過 proxy 連接外部 API
clientConfig := &genai.ClientConfig{
    Project:  params.Vertex.ProjectID,
    Location: params.Vertex.Region,
    Backend:  genai.BackendVertexAI,
}

// 根據配置動態添加 HTTP 客戶端
m.client, err = genai.NewClient(ctx, clientConfig)
```

### 4. 導入必要的包
添加 `net/url` 包的導入：

```go
import (
    // ... 其他導入
    "net/url"
    // ... 其他導入
)
```

## 修改的文件

### 1. `internal/llms/gemini/genai.go`
- **添加導入**：新增 `net/url` 包
- **修改 Initialize 方法**：添加代理配置邏輯
- **動態配置**：根據 `system.proxy` 配置動態設置 HTTP 客戶端
- **錯誤處理**：添加代理 URL 解析錯誤處理
- **日誌記錄**：添加代理設置的日誌記錄

### 2. `internal/llms/gemini/genai_test.go`
- **添加導入**：新增 `github.com/gogf/gf/v2/frame/g` 包
- **新增測試**：添加 `TestProxyConfiguration` 測試方法
- **配置測試**：驗證代理配置讀取邏輯

### 3. `ProductFile/api規格.md`
- **更新記錄**：添加 Gemini 代理配置修復的說明
- **功能描述**：記錄新增的代理支持功能

## 技術細節

### 代理配置流程
1. **配置讀取**：從 `system.proxy` 配置項讀取代理地址
2. **URL 解析**：解析代理 URL 並驗證格式
3. **HTTP 客戶端創建**：創建帶代理的 HTTP 客戶端
4. **客戶端配置**：將 HTTP 客戶端設置到 genai.ClientConfig 中
5. **錯誤處理**：處理代理配置錯誤並記錄日誌

### 與 Claude 的一致性
- **配置來源**：都從 `system.proxy` 讀取代理配置
- **錯誤處理**：都有完整的錯誤處理和日誌記錄
- **超時設置**：都設置了合理的超時時間
- **代理格式**：都支持標準的 HTTP 代理格式

### 併發安全
- **無狀態操作**：代理配置在初始化時設置，不涉及併發修改
- **線程安全**：HTTP 客戶端本身是線程安全的
- **配置讀取**：GoFrame 的配置讀取是線程安全的

## 預期效果

### 1. 網絡連接修復
- **API 調用成功**：Gemini 能夠通過代理成功連接 Google Cloud API
- **認證正常**：Google Cloud 認證流程正常工作
- **穩定性提升**：避免網絡連接超時導致的服務失敗

### 2. 配置一致性
- **統一管理**：所有 LLM 服務都使用相同的代理配置
- **維護簡化**：代理配置集中管理，便於維護
- **部署靈活**：支持不同網絡環境的部署需求

### 3. 用戶體驗改善
- **透明代理**：用戶無需額外配置，系統自動處理代理設置
- **錯誤減少**：減少因網絡連接問題導致的服務中斷
- **日誌完善**：提供詳細的代理配置日誌，便於問題排查

## 測試驗證

### 1. 單元測試
- ✅ `TestProxyConfiguration`：驗證代理配置邏輯
- ✅ `TestInitialize`：驗證初始化流程
- ✅ 所有現有測試：確保沒有破壞現有功能

### 2. 集成測試
- ✅ 項目編譯：確保代碼編譯正常
- ✅ 功能測試：驗證 Gemini 服務基本功能

### 3. 配置測試
- ✅ 代理配置讀取：驗證能正確讀取配置
- ✅ URL 解析：驗證代理 URL 解析邏輯
- ✅ 錯誤處理：驗證錯誤處理機制

## 部署建議

### 1. 配置檢查
- 確保 `system.proxy` 配置正確設置
- 驗證代理服務器可用性
- 檢查網絡連接權限

### 2. 監控建議
- 監控 Gemini API 調用成功率
- 關注代理相關的錯誤日誌
- 監控網絡連接超時情況

### 3. 故障排除
- 檢查代理配置格式
- 驗證代理服務器狀態
- 查看詳細的錯誤日誌

這個修復確保了 Gemini 服務在需要代理的網絡環境中能夠正常工作，與 Claude 服務保持了一致的代理配置機制，提升了整體系統的穩定性和可用性。
