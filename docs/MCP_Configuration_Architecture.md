# MCP 配置架構說明 (v2.0)

## 概述

本文檔說明了 brainHub v2.0 中 MCP 配置架構的重大重構，包括配置流程的簡化、性能優化和新的配置格式。

## 架構變更

### v1.x 舊架構
```
配置文件 → router.getMCPConfig() → LLMsConfig.MCPConfig → LLM初始化 → 格式轉換 → MCP工具管理器
```

**問題**:
- 配置傳遞層級過多
- 配置數據重複存儲
- 格式轉換邏輯重複
- 配置管理分散

### v2.0 新架構
```
配置文件 → ConfigManager.LoadMCPConfig() → MCP工具管理器
```

**優勢**:
- 配置流程簡化
- 統一配置管理
- 配置緩存機制
- 支持熱更新

## 配置結構變更

### 新的配置格式

```yaml
# MCP 全局配置
mcp_config:
  enabled: true
  default_timeout: "30s"
  max_concurrent_calls: 20
  cache_ttl: "10m"
  log_level: "info"

# MCP 服務器配置
mcp_servers:
  - name: "filesystem"
    type: "stdio"
    description: "文件系統操作工具"
    command: "mcp-server-filesystem"
    args: ["--root", "/workspace"]
    timeout: "30s"
    retry_count: 3
```

### 配置路徑說明

| 配置路徑 | 用途 | 類型 |
|---------|------|------|
| `mcp_config` | MCP 全局配置 | Object |
| `mcp_servers` | MCP 服務器列表 | Array |

## ConfigManager 功能

### 核心功能

1. **配置加載**: 直接從配置文件讀取 `mcp_config` 和 `mcp_servers`
2. **配置緩存**: 5分鐘TTL的配置緩存，提升性能
3. **配置驗證**: 統一的配置驗證邏輯
4. **熱更新**: 支持配置變更時的自動更新
5. **默認值**: 自動應用合理的默認配置

### API 接口

```go
type ConfigManager struct {
    logger      glog.ILogger
    configCache *MCPConfig
    cacheTime   time.Time
    cacheTTL    time.Duration
    cacheMutex  sync.RWMutex
}

// 主要方法
func (cm *ConfigManager) LoadMCPConfig(ctx context.Context) (*MCPConfig, error)
func (cm *ConfigManager) ClearCache()
func (cm *ConfigManager) SetCacheTTL(ttl time.Duration)
func (cm *ConfigManager) GetCacheInfo() map[string]interface{}
func (cm *ConfigManager) WatchConfigChanges(ctx context.Context, callback func(*MCPConfig)) error
```

## LLM 初始化變更

### v1.x 舊方式
```go
// 需要在 LLMsConfig 中傳遞 MCP 配置
config := &llm.LLMsConfig{
    MCPConfig: &llm.MCPConfig{
        Enabled: true,
        Servers: servers,
        Global:  globalConfig,
    },
}

// 在 LLM 初始化中進行格式轉換
mcpConfig := &mcp.MCPConfig{
    Enabled: params.MCPConfig.Enabled,
    Servers: s.convertMCPServerConfigs(params.MCPConfig.Servers),
    Global:  s.convertMCPGlobalConfig(params.MCPConfig.Global),
}
```

### v2.0 新方式
```go
// 簡化的 LLMsConfig，不再包含 MCPConfig
config := &llm.LLMsConfig{
    // MCP 配置現在直接從配置文件讀取
}

// LLM 初始化時直接使用 ConfigManager
func (s *AoAi) Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) error {
    // 直接使用 ConfigManager 獲取 MCP 配置
    configManager := mcp.NewConfigManager()
    mcpConfig, err := configManager.LoadMCPConfig(ctx)
    if err != nil {
        s.logger().Warningf(ctx, "Failed to load MCP config: %v", err)
        return nil // 不阻止 LLM 初始化
    }
    
    if mcpConfig != nil && mcpConfig.Enabled {
        s.mcpToolManager = mcp.NewMCPToolManager(mcpConfig)
        // ...
    }
}
```

## 性能優化

### 配置緩存
- **緩存TTL**: 5分鐘（可配置）
- **緩存策略**: 讀寫鎖保護的內存緩存
- **緩存清理**: 支持手動清理和自動過期

### 熱更新機制
- **配置監聽**: 每30秒檢查配置變更
- **自動清理**: 配置變更時自動清理緩存
- **回調通知**: 支持配置變更回調

## 環境變量支持

### 語法
```yaml
headers:
  Authorization: "Bearer ${API_TOKEN}"
  X-Client-ID: "${CLIENT_ID}"
```

### 展開邏輯
- 在配置加載時自動展開環境變量
- 支持 `${VAR_NAME}` 格式
- 未找到環境變量時保持原值

## 配置驗證

### 驗證規則
1. **必填字段**: name, type
2. **類型驗證**: stdio 需要 command，http/sse 需要 url
3. **格式驗證**: timeout 格式，retry_count 範圍
4. **唯一性**: 服務器名稱唯一性

### 默認值
```go
const (
    DefaultTimeout            = "30s"
    DefaultMaxConcurrentCalls = 10
    DefaultCacheTTL           = "5m"
    DefaultRetryCount         = 3
)
```

## 遷移指南

### 從 v1.x 遷移到 v2.0

1. **更新配置文件格式**:
   ```yaml
   # 舊格式
   mcp:
     enabled: true
     servers: [...]
     global: {...}
   
   # 新格式
   mcp_config:
     enabled: true
     # 全局配置項
   
   mcp_servers:
     - name: "..."
       # 服務器配置
   ```

2. **移除 LLMsConfig 中的 MCPConfig**:
   ```go
   // 移除這個字段
   // MCPConfig *MCPConfig `json:"mcp_config"`
   ```

3. **更新 LLM 初始化邏輯**:
   - 移除配置轉換方法
   - 使用 ConfigManager 直接獲取配置

4. **測試配置**:
   - 驗證新配置格式正確
   - 測試配置緩存和熱更新功能

## 最佳實踐

### 配置管理
1. **使用環境變量**: 敏感信息使用環境變量
2. **合理設置緩存TTL**: 根據配置變更頻率調整
3. **監控配置變更**: 使用配置變更回調進行監控
4. **配置驗證**: 部署前驗證配置有效性

### 性能優化
1. **利用緩存**: 避免頻繁的配置文件讀取
2. **合理設置並發數**: 根據系統資源調整 max_concurrent_calls
3. **適當的超時設置**: 根據工具特性設置合理的 timeout

### 錯誤處理
1. **優雅降級**: MCP 配置錯誤不應阻止 LLM 初始化
2. **詳細日誌**: 記錄配置加載和驗證過程
3. **監控告警**: 監控 MCP 配置和工具調用狀態

通過這次重構，MCP 配置架構變得更加簡潔、高效和易於維護。
