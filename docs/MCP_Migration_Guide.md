# MCP 配置遷移指南 (v1.x → v2.0)

## 概述

本指南幫助您從 brainHub v1.x 的 MCP 配置遷移到 v2.0 的新架構。v2.0 重構了 MCP 配置管理，提供了更簡潔、高效的配置方式。

## 主要變更

### 1. 配置結構變更

#### v1.x 舊格式
```yaml
mcp:
  enabled: true
  servers:
    - name: "filesystem"
      type: "stdio"
      command: "mcp-server-filesystem"
      args: ["--root", "/workspace"]
      timeout: "30s"
      retry_count: 3
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 10
    cache_ttl: "5m"
    log_level: "info"
```

#### v2.0 新格式
```yaml
# 全局配置分離
mcp_config:
  enabled: true
  default_timeout: "30s"
  max_concurrent_calls: 10
  cache_ttl: "5m"
  log_level: "info"

# 服務器配置分離
mcp_servers:
  - name: "filesystem"
    type: "stdio"
    description: "文件系統操作工具"
    command: "mcp-server-filesystem"
    args: ["--root", "/workspace"]
    timeout: "30s"
    retry_count: 3
```

### 2. 代碼變更

#### 移除的結構體
```go
// 這些結構體已從 internal/model/llm/params.go 中移除
type MCPConfig struct { ... }
type MCPServerConfig struct { ... }
type MCPGlobalConfig struct { ... }

// LLMsConfig 中的 MCPConfig 字段已移除
type LLMsConfig struct {
    // MCPConfig *MCPConfig // 已移除
}
```

#### 新的配置獲取方式
```go
// v1.x 舊方式
config := &llm.LLMsConfig{
    MCPConfig: mcpConfig, // 需要手動傳遞
}

// v2.0 新方式
config := &llm.LLMsConfig{
    // MCP 配置自動從配置文件讀取
}

// 在 LLM 初始化中
configManager := mcp.NewConfigManager()
mcpConfig, err := configManager.LoadMCPConfig(ctx)
```

## 遷移步驟

### 步驟 1: 更新配置文件

1. **備份現有配置**
   ```bash
   cp config.yaml config.yaml.backup
   ```

2. **轉換配置格式**
   ```yaml
   # 將舊的 mcp 配置
   mcp:
     enabled: true
     servers: [...]
     global: {...}
   
   # 轉換為新格式
   mcp_config:
     enabled: true
     # 將 global 中的配置項移到這裡
   
   mcp_servers:
     # 將 servers 數組移到這裡
   ```

3. **添加新的配置項**
   ```yaml
   mcp_servers:
     - name: "filesystem"
       type: "stdio"
       description: "文件系統操作工具"  # 新增描述字段
       command: "mcp-server-filesystem"
       args: ["--root", "/workspace"]
       env:  # 新增環境變量支持
         - "PATH=/usr/local/bin:/usr/bin:/bin"
         - "MCP_LOG_LEVEL=info"
       timeout: "30s"
       retry_count: 3
   ```

### 步驟 2: 更新代碼

1. **移除 MCPConfig 相關代碼**
   ```go
   // 移除這些轉換方法
   func convertMCPServerConfigs(...) { ... }
   func convertMCPGlobalConfig(...) { ... }
   
   // 移除 router 中的 getMCPConfig 方法
   func (s *sAiRouter) getMCPConfig(...) { ... }
   ```

2. **更新 LLM 初始化邏輯**
   ```go
   // 舊方式
   if params.MCPConfig != nil && params.MCPConfig.Enabled {
       mcpConfig := &mcp.MCPConfig{
           Enabled: params.MCPConfig.Enabled,
           Servers: s.convertMCPServerConfigs(params.MCPConfig.Servers),
           Global:  s.convertMCPGlobalConfig(params.MCPConfig.Global),
       }
       // ...
   }
   
   // 新方式
   configManager := mcp.NewConfigManager()
   mcpConfig, err := configManager.LoadMCPConfig(ctx)
   if err != nil {
       s.logger().Warningf(ctx, "Failed to load MCP config: %v", err)
   } else if mcpConfig != nil && mcpConfig.Enabled {
       s.mcpToolManager = mcp.NewMCPToolManager(mcpConfig)
       // ...
   }
   ```

### 步驟 3: 測試遷移

1. **驗證配置格式**
   ```bash
   # 檢查配置文件語法
   go run tools/config-validator.go
   ```

2. **運行單元測試**
   ```bash
   go test ./internal/llms/mcp/... -v
   go test ./test/config_manager_test.go -v
   ```

3. **測試 LLM 初始化**
   ```bash
   go test ./test/llm_initialization_test.go -v
   ```

## 配置映射表

| v1.x 路徑 | v2.0 路徑 | 說明 |
|-----------|-----------|------|
| `mcp.enabled` | `mcp_config.enabled` | 全局啟用開關 |
| `mcp.global.default_timeout` | `mcp_config.default_timeout` | 默認超時時間 |
| `mcp.global.max_concurrent_calls` | `mcp_config.max_concurrent_calls` | 最大並發數 |
| `mcp.global.cache_ttl` | `mcp_config.cache_ttl` | 緩存TTL |
| `mcp.global.log_level` | `mcp_config.log_level` | 日誌級別 |
| `mcp.servers` | `mcp_servers` | 服務器配置數組 |

## 新功能

### 1. 配置緩存
```go
// 獲取緩存信息
configManager := mcp.NewConfigManager()
cacheInfo := configManager.GetCacheInfo()

// 清理緩存
configManager.ClearCache()

// 設置緩存TTL
configManager.SetCacheTTL(10 * time.Minute)
```

### 2. 配置熱更新
```go
// 監聽配置變更
err := configManager.WatchConfigChanges(ctx, func(config *mcp.MCPConfig) {
    log.Info("MCP configuration updated")
    // 處理配置變更
})
```

### 3. 環境變量支持
```yaml
mcp_servers:
  - name: "api-server"
    type: "http"
    url: "https://api.example.com/mcp"
    headers:
      Authorization: "Bearer ${API_TOKEN}"  # 自動展開環境變量
      X-Client-ID: "${CLIENT_ID}"
```

## 常見問題

### Q: 遷移後 MCP 功能不工作？
A: 檢查以下項目：
1. 配置文件格式是否正確
2. `mcp_config.enabled` 是否為 `true`
3. 查看日誌中的配置加載錯誤

### Q: 如何驗證新配置是否生效？
A: 使用以下方法：
```go
configManager := mcp.NewConfigManager()
config, err := configManager.LoadMCPConfig(ctx)
if err != nil {
    log.Error("Config load failed:", err)
} else {
    log.Info("Config loaded successfully:", config)
}
```

### Q: 配置緩存如何工作？
A: 
- 默認緩存TTL為5分鐘
- 配置變更時自動清理緩存
- 可以手動清理緩存強制重新加載

### Q: 如何回滾到舊版本？
A: 
1. 恢復備份的配置文件
2. 回滾代碼到 v1.x 版本
3. 重新部署應用

## 最佳實踐

1. **逐步遷移**: 先在開發環境測試，再部署到生產環境
2. **配置驗證**: 使用配置驗證工具檢查格式正確性
3. **監控日誌**: 遷移後密切監控 MCP 相關日誌
4. **性能測試**: 驗證配置緩存對性能的改善
5. **備份策略**: 保留舊配置文件作為備份

通過遵循本指南，您可以順利完成從 v1.x 到 v2.0 的 MCP 配置遷移。
