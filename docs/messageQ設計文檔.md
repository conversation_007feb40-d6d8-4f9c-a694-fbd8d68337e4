# MessageQ 消息隊列設計文檔

## 📋 文檔概述

本文檔詳細說明了 DataSyncHub 專案中 MessageQ 消息隊列組件的設計架構、實現邏輯和使用方法。MessageQ 是基於 RabbitMQ 的高可用消息隊列服務，負責處理系統內部的異步消息通信，支持 MariaDB 和 Weaviate 的數據同步操作。

### 目標讀者
- 後端開發工程師
- 系統架構師
- 運維工程師
- 需要集成相同消息隊列功能的開發團隊

## 🏗️ 整體架構設計

### 架構模式
MessageQ 採用 **生產者-消費者模式** 結合 **發布-訂閱模式**，實現了高可用、可擴展的消息處理架構。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息生產者     │───▶│   RabbitMQ      │───▶│   MessageQ      │
│  (HTTP API)     │    │   Exchange      │    │   消費者        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────┐         ┌─────────────┐
                       │   Queue     │         │  Handler    │
                       │  Binding    │         │ Dispatcher  │
                       └─────────────┘         └─────────────┘
                                                       │
                              ┌────────────────────────┼────────────────────────┐
                              ▼                        ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
                    │   MariaDB       │    │   Weaviate      │    │   其他服務      │
                    │   Handler       │    │   Handler       │    │   Handler       │
                    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心設計原則
1. **高可用性**：自動重連機制，連接斷開時自動恢復
2. **並發安全**：使用讀寫鎖保護共享資源，支持並發消息處理
3. **錯誤恢復**：完善的 panic 恢復機制，防止單個消息處理失敗影響整體服務
4. **可觀測性**：詳細的日誌記錄和連接狀態監控
5. **可擴展性**：支持動態註冊消息處理器，易於擴展新的業務邏輯

## 📁 文件結構

```
internal/logic/messageQ/
├── messageQ.go          # 主要實現文件
└── messageQ_test.go     # 單元測試文件

internal/service/
└── message_q.go         # 服務接口定義

internal/consts/
├── consts.go           # 基礎常量定義
└── mq.go              # 消息隊列相關常量
```

## 🔧 核心數據結構

### sMessageQ 結構體

```go
type sMessageQ struct {
    // RabbitMQ 連接管理
    conn            *amqp.Connection
    channel         *amqp.Channel
    messageHandlers map[string]consts.OnMessage

    // 連接狀態管理
    isConnected  bool
    isConnecting bool
    isClosed     bool
    url          string

    // 控制通道
    reconnectChan chan struct{}
    closeChan     chan struct{}

    // 同步控制
    mu sync.RWMutex

    // 重連配置
    maxRetries           int
    retryInterval        time.Duration
    maxReconnectInterval time.Duration
    reconnectCount       int
    lastReconnectTime    time.Time

    // 消息處理並發控制
    messageWorkerCount int
    messageWorkerSem   chan struct{}
}
```

### 關鍵字段說明

| 字段名 | 類型 | 說明 |
|--------|------|------|
| `conn` | `*amqp.Connection` | RabbitMQ 連接實例 |
| `channel` | `*amqp.Channel` | RabbitMQ 通道實例 |
| `messageHandlers` | `map[string]consts.OnMessage` | 路由鍵前綴到處理器的映射 |
| `isConnected` | `bool` | 連接狀態標識 |
| `isConnecting` | `bool` | 正在連接標識，防止重複連接 |
| `isClosed` | `bool` | 服務關閉標識 |
| `reconnectChan` | `chan struct{}` | 重連信號通道 |
| `closeChan` | `chan struct{}` | 關閉信號通道 |
| `mu` | `sync.RWMutex` | 讀寫鎖，保護共享資源 |
| `messageWorkerSem` | `chan struct{}` | 信號量，控制並發處理數量 |

## 🔌 服務接口定義

### IMessageQ 接口

```go
type IMessageQ interface {
    RegisterHandler(routeKeyPrefix string, handler consts.OnMessage)
    InitReceive(ctx context.Context) (err error)
    IsHealthy() bool
    Close() error
    GetConnectionStatus() map[string]interface{}
}
```

### 接口方法說明

#### RegisterHandler
```go
func RegisterHandler(routeKeyPrefix string, handler consts.OnMessage)
```
- **功能**：註冊消息處理器
- **參數**：
  - `routeKeyPrefix`：路由鍵前綴（如 "mariadb."、"weaviate."）
  - `handler`：消息處理函數
- **使用場景**：在服務啟動時註冊不同類型的消息處理器

#### InitReceive
```go
func InitReceive(ctx context.Context) (err error)
```
- **功能**：初始化消息接收服務
- **參數**：
  - `ctx`：上下文對象
- **返回值**：錯誤信息
- **功能說明**：
  1. 從配置中讀取 RabbitMQ 連接 URL
  2. 測試連接可用性
  3. 啟動連接維護 goroutine
  4. 啟動消息消費 goroutine

#### IsHealthy
```go
func IsHealthy() bool
```
- **功能**：檢查服務健康狀態
- **返回值**：健康狀態布爾值
- **檢查項目**：
  - 服務未關閉
  - 連接已建立
  - RabbitMQ 連接未關閉
  - 通道可用

#### Close
```go
func Close() error
```
- **功能**：優雅關閉服務
- **返回值**：錯誤信息
- **關閉流程**：
  1. 設置關閉標識
  2. 關閉控制通道
  3. 關閉 RabbitMQ 連接
  4. 等待消息處理完成

#### GetConnectionStatus
```go
func GetConnectionStatus() map[string]interface{}
```
- **功能**：獲取連接狀態信息
- **返回值**：包含詳細狀態信息的 map
- **狀態信息**：連接狀態、重連次數、工作線程數等

## 🚀 核心方法詳解

### 1. 初始化和配置

#### New() 構造函數
```go
func New() service.IMessageQ {
    return &sMessageQ{
        messageHandlers:      make(map[string]consts.OnMessage),
        reconnectChan:        make(chan struct{}, 1),
        closeChan:            make(chan struct{}),
        maxRetries:           5,                    // 最大重試次數
        retryInterval:        time.Second * 5,      // 重試間隔
        maxReconnectInterval: time.Minute * 5,      // 最大重連間隔
        messageWorkerCount:   10,                   // 並發處理數
        messageWorkerSem:     make(chan struct{}, 10),
    }
}
```

**配置參數說明**：
- `maxRetries: 5`：每批次最大重試 5 次
- `retryInterval: 5s`：基礎重試間隔 5 秒
- `maxReconnectInterval: 5m`：最大重連間隔 5 分鐘
- `messageWorkerCount: 10`：最大並發處理 10 個消息

### 2. 連接管理

#### testConnection() 連接測試
```go
func (s *sMessageQ) testConnection(ctx context.Context) error
```
- **功能**：測試 RabbitMQ 連接可用性
- **實現邏輯**：
  1. 嘗試建立連接
  2. 創建通道
  3. 立即關閉測試連接
  4. 記錄測試結果

#### connect() 建立連接
```go
func (s *sMessageQ) connect(ctx context.Context) error
```
- **功能**：建立 RabbitMQ 連接
- **實現邏輯**：
  1. 加鎖防止並發連接
  2. 關閉現有連接
  3. 建立新連接和通道
  4. 更新連接狀態

### 3. 重連機制

#### reconnectWithRetry() 重連邏輯
```go
func (s *sMessageQ) reconnectWithRetry(ctx context.Context)
```

**重連策略**：
1. **頻率限制**：最小重連間隔 10 秒
2. **指數退避**：重試間隔按 2^n 增長
3. **最大限制**：單次重連最多 5 次，總重連最多 100 次
4. **可中斷**：支持上下文取消

**重連流程**：
```
開始重連 → 檢查頻率限制 → 指數退避重試 → 成功/失敗處理
    ↓              ↓              ↓              ↓
頻率檢查 → 等待最小間隔 → 嘗試連接 → 記錄結果並重置計數器
```

### 4. 消息處理

#### dispatchMessage() 消息分發
```go
func (s *sMessageQ) dispatchMessage(ctx context.Context, msg amqp.Delivery)
```

**分發邏輯**：
1. **路由匹配**：遍歷已註冊的處理器，根據路由鍵前綴匹配
2. **並發處理**：使用 `g.Go()` 創建 goroutine 並發處理消息
3. **錯誤恢復**：每個 goroutine 都有獨立的錯誤恢復機制
4. **多處理器支持**：一個消息可以被多個匹配的處理器處理

**代碼示例**：
```go
// 遍歷處理器，找到匹配的前綴
for prefix, handler := range s.messageHandlers {
    if gstr.HasPrefix(routingKey, prefix) {
        handlerFound = true
        // 使用 goroutine 並發處理消息
        g.Go(ctx, func(ctx context.Context) {
            s.processMessageWithRecovery(ctx, msg, handler, prefix)
        }, func(ctx context.Context, exception error) {
            s.logger().Errorf(ctx, "Message processing goroutine exception for prefix %s: %v",
                prefix, gerror.Stack(exception))
        })
    }
}
```

#### processMessageWithRecovery() 消息處理恢復
```go
func (s *sMessageQ) processMessageWithRecovery(ctx context.Context, msg amqp.Delivery,
    handler consts.OnMessage, prefix string)
```

**處理流程**：
1. **並發控制**：獲取信號量，限制並發處理數量
2. **Panic 恢復**：使用 defer + recover 防止 panic 導致服務崩潰
3. **性能監控**：記錄消息處理時間
4. **日誌記錄**：詳細記錄處理過程和結果

### 5. 消息消費

#### consumeMessages() 消息消費核心邏輯
```go
func (s *sMessageQ) consumeMessages(ctx context.Context) error
```

**消費流程**：
1. **Exchange 聲明**：聲明 "dsh" direct exchange
2. **Queue 聲明**：創建臨時隊列（自動刪除）
3. **Queue 綁定**：綁定所有預定義的路由鍵
4. **開始消費**：啟動消息消費循環
5. **消息分發**：將接收到的消息分發給對應處理器

**Exchange 和 Queue 配置**：
```go
// Exchange 聲明
err := channel.ExchangeDeclare(
    consts.ExchangeName,  // "dsh"
    "direct",             // 類型：直接路由
    true,                 // 持久化
    false,                // 不自動刪除
    false,                // 不是內部 exchange
    false,                // 不等待
    nil,                  // 參數
)

// Queue 聲明
queue, err := channel.QueueDeclare(
    "",     // 空名稱，自動生成
    true,   // 持久化
    true,   // 自動刪除
    true,   // 獨占
    false,  // 不等待
    nil,    // 參數
)
```

## 📊 路由鍵和消息類型

### 路由鍵結構

MessageQ 使用分層路由鍵結構：`{service}.{resource}.{action}`

#### 預定義路由鍵
```go
// 服務前綴
RouteKeyMariadbPrefix  = "mariadb."
RouteKeyWeaviatePrefix = "weaviate."

// 資源類型
RouteKeyAMS        = "ams"
RouteKeyTenant     = "tenant"
RouteKeyQuizto     = "quizto"
RouteKeyChannelHub = "channelHub"
RouteKeyBrainHub   = "brainHub"

// 完整路由鍵列表
var RouteKeys = []string{
    "mariadb.ams",
    "mariadb.tenant",
    "mariadb.quizto",
    "mariadb.channelHub",
    "mariadb.brainHub",
    "weaviate.ams",
    "weaviate.tenant",
    "weaviate.quizto",
}
```

### 消息動作類型

#### MariaDB 動作類型
```go
const (
    ActionInsert         = "insert"
    ActionDelete         = "delete"
    ActionUpdate         = "update"
    ActionUpdateOrInsert = "updateOrInsert"
    ActionCreateSchema   = "createSchema"
    ActionCreateTable    = "createTable"
)
```

#### Weaviate 動作類型
```go
const (
    ActionCreateCollection  = "create_collection"
    ActionAddNewProperties  = "add_new_properties"
    ActionCreateData        = "create_data"
    ActionUpdateProperties  = "update_properties"
    ActionCreateTenant      = "create_tenant"
    ActionClearDataByFilter = "clear_data_by_filter"
    ActionEmptyCollection   = "empty_collection"
    ActionDeleteCollection  = "delete_collection"
    ActionDeleteTenants     = "delete_tenants"
    ActionUpdateVector      = "update_vector"
)
```

### 消息格式

#### 標準消息結構
```go
type amqp.Delivery struct {
    RoutingKey string      // 路由鍵
    Type       string      // 動作類型
    Body       []byte      // 消息體（JSON 格式）
    Headers    amqp.Table  // 消息頭
}
```

#### 消息體示例
```json
{
    "schema": "dsh",
    "table": "resource_records",
    "data": {
        "upload_user_id": "user123",
        "upload_files": [...],
        "url_contents": [...],
        "youtube_contents": [...],
        "plain_text_contents": [...]
    }
}
```

## ⚙️ 配置管理

### RabbitMQ 配置

#### 配置文件位置
- **開發環境**：`manifest/config/config.yaml`
- **生產環境**：Nacos 配置中心

#### 配置格式
```yaml
rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"
```

#### 環境變量支持
```bash
export RABBITMQ_URL="amqp://admin:admin@127.0.0.1:5672/"
```

### 連接參數說明

| 參數 | 說明 | 默認值 |
|------|------|--------|
| `protocol` | 協議類型 | `amqp` |
| `username` | 用戶名 | `admin` |
| `password` | 密碼 | `admin` |
| `host` | 主機地址 | `127.0.0.1` |
| `port` | 端口號 | `5672` |
| `vhost` | 虛擬主機 | `/` |

## 🔄 並發處理機制

### 並發控制策略

#### 1. 信號量機制
```go
messageWorkerSem chan struct{}  // 容量為 10 的信號量
```

**工作原理**：
- 每個消息處理前獲取信號量：`s.messageWorkerSem <- struct{}{}`
- 處理完成後釋放信號量：`<-s.messageWorkerSem`
- 最多同時處理 10 個消息

#### 2. 讀寫鎖保護
```go
mu sync.RWMutex  // 保護共享資源
```

**保護資源**：
- 連接狀態變量
- RabbitMQ 連接和通道
- 重連計數器和時間戳

#### 3. Goroutine 管理
使用 GoFrame 的 `g.Go()` 方法：
- 自動 panic 恢復
- 統一錯誤處理
- 上下文傳遞

### 線程安全保證

#### 讀操作（使用讀鎖）
```go
s.mu.RLock()
defer s.mu.RUnlock()
// 讀取共享資源
```

#### 寫操作（使用寫鎖）
```go
s.mu.Lock()
defer s.mu.Unlock()
// 修改共享資源
```

## 🔍 錯誤處理和恢復機制

### 錯誤處理層次

#### 1. 連接層錯誤
**錯誤類型**：
- 網絡連接失敗
- 認證失敗
- 通道創建失敗

**處理策略**：
- 自動重連機制
- 指數退避算法
- 連接狀態監控

#### 2. 消息處理錯誤
**錯誤類型**：
- 消息格式錯誤
- 處理器 panic
- 業務邏輯錯誤

**處理策略**：
- Panic 恢復機制
- 錯誤日誌記錄
- 繼續處理其他消息

#### 3. 系統級錯誤
**錯誤類型**：
- 內存不足
- 上下文取消
- 服務關閉

**處理策略**：
- 優雅關閉
- 資源清理
- 狀態持久化

### Panic 恢復機制

```go
defer func() {
    if r := recover(); r != nil {
        s.logger().Errorf(ctx, "Message handler panic recovered for prefix %s, routing key %s: %v",
            prefix, msg.RoutingKey, r)
    }
}()
```

**恢復範圍**：
- 消息處理器 panic
- JSON 解析錯誤
- 數據庫操作異常
- 業務邏輯錯誤

## 📈 監控和日誌

### 日誌分類

#### 1. 連接日誌
```go
s.logger().Info(ctx, "RabbitMQ connection established successfully")
s.logger().Error(ctx, "Initial connection failed:", err)
```

#### 2. 消息處理日誌
```go
s.logger().Infof(ctx, "receive message route key: %s", msg.RoutingKey)
s.logger().Debugf(ctx, "Message processed successfully for prefix %s, routing key %s, processing time: %v",
    prefix, msg.RoutingKey, processingTime)
```

#### 3. 錯誤日誌
```go
s.logger().Errorf(ctx, "Message processing goroutine exception for prefix %s: %v",
    prefix, gerror.Stack(exception))
```

### 監控指標

#### 連接狀態監控
```go
func (s *sMessageQ) GetConnectionStatus() map[string]interface{} {
    return map[string]interface{}{
        "is_connected":           s.isConnected,
        "is_connecting":          s.isConnecting,
        "is_closed":              s.isClosed,
        "reconnect_count":        s.reconnectCount,
        "last_reconnect_time":    s.lastReconnectTime.Format(time.RFC3339),
        "active_message_workers": len(s.messageWorkerSem),
    }
}
```

#### 性能指標
- 消息處理時間
- 並發處理數量
- 重連次數和頻率
- 錯誤率統計

## 🚀 使用指南

### 基本使用流程

#### 1. 服務初始化
```go
// 在 main 函數或服務啟動時
func main() {
    ctx := context.Background()

    // 註冊消息處理器
    service.MessageQ().RegisterHandler(consts.RouteKeyMariadbPrefix, service.MariaDB().OnMessage)
    service.MessageQ().RegisterHandler(consts.RouteKeyWeaviatePrefix, service.VecWeaviate().OnMessage)

    // 初始化消息接收
    err := service.MessageQ().InitReceive(ctx)
    if err != nil {
        panic(err)
    }
}
```

#### 2. 自定義處理器實現
```go
// 實現 consts.OnMessage 接口
func (s *MyService) OnMessage(ctx context.Context, message any) {
    var msg *amqp.Delivery
    _ = gconv.Struct(message, &msg)

    if msg != nil {
        // 解析消息體
        bodyStr := gjson.New(msg.Body).MustToJsonIndentString()

        // 根據消息類型處理
        switch msg.Type {
        case "insert":
            s.handleInsert(ctx, msg)
        case "update":
            s.handleUpdate(ctx, msg)
        case "delete":
            s.handleDelete(ctx, msg)
        }
    }
}
```

#### 3. 處理器註冊
```go
// 註冊自定義處理器
service.MessageQ().RegisterHandler("myservice.", myService.OnMessage)
```

### 消息發送示例

雖然 MessageQ 主要負責消息消費，但了解消息發送格式有助於理解整體流程：

```go
// 發送消息到 RabbitMQ（通常由其他服務完成）
func sendMessage(routingKey, actionType string, data interface{}) error {
    conn, err := amqp.Dial("amqp://admin:admin@127.0.0.1:5672/")
    if err != nil {
        return err
    }
    defer conn.Close()

    channel, err := conn.Channel()
    if err != nil {
        return err
    }
    defer channel.Close()

    body, _ := json.Marshal(data)

    return channel.Publish(
        "dsh",        // exchange
        routingKey,   // routing key
        false,        // mandatory
        false,        // immediate
        amqp.Publishing{
            ContentType: "application/json",
            Body:        body,
            Type:        actionType,
        },
    )
}
```

### 健康檢查

```go
// 檢查服務健康狀態
if service.MessageQ().IsHealthy() {
    fmt.Println("MessageQ service is healthy")
} else {
    fmt.Println("MessageQ service is unhealthy")
}

// 獲取詳細狀態
status := service.MessageQ().GetConnectionStatus()
fmt.Printf("Connection status: %+v\n", status)
```

## 🔧 配置最佳實踐

### 開發環境配置

#### 1. 本地開發配置
```yaml
# manifest/config/config.yaml
rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"
```

#### 2. Docker Compose 配置
```yaml
version: '3.8'
services:
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
```

### 生產環境配置

#### 1. Nacos 配置
```yaml
# 在 Nacos 配置中心設置
rabbitMQ:
  url: "amqp://prod_user:prod_pass@rabbitmq-cluster:5672/"
```

#### 2. 環境變量配置
```bash
export RABBITMQ_URL="amqp://prod_user:prod_pass@rabbitmq-cluster:5672/"
```

### 性能調優建議

#### 1. 並發處理數調整
```go
// 根據服務器性能調整並發數
messageWorkerCount: 20  // 高性能服務器可增加到 20-50
```

#### 2. 重連參數調整
```go
maxRetries:           10,                   // 增加重試次數
retryInterval:        time.Second * 3,      // 減少重試間隔
maxReconnectInterval: time.Minute * 10,     // 增加最大重連間隔
```

#### 3. 連接池配置
```go
// RabbitMQ 連接池配置（如果需要）
maxConnections: 10
maxChannels:    100
```

## 🛠️ 與 DataSyncHub 系統集成

### 系統集成架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP API      │    │   MessageQ      │    │   Data Layer    │
│   Controllers   │───▶│   Service       │───▶│   MariaDB       │
└─────────────────┘    └─────────────────┘    │   Weaviate      │
                              │                └─────────────────┘
                              ▼
                       ┌─────────────────┐
                       │   RabbitMQ      │
                       │   Exchange      │
                       └─────────────────┘
```

### 服務註冊和發現

#### 1. 服務註冊
```go
// internal/cmd/cmd.go
func init() {
    // 註冊 MessageQ 服務
    service.RegisterMessageQ(messageQ.New())
}
```

#### 2. 處理器註冊
```go
// 在服務啟動時註冊處理器
service.MessageQ().RegisterHandler(consts.RouteKeyMariadbPrefix, service.MariaDB().OnMessage)
service.MessageQ().RegisterHandler(consts.RouteKeyWeaviatePrefix, service.VecWeaviate().OnMessage)
```

### 與其他組件的交互

#### 1. MariaDB 服務集成
```go
// MariaDB 處理器實現
func (s *sMariaDB) OnMessage(ctx context.Context, message any) {
    var msg *amqp.Delivery
    _ = gconv.Struct(message, &msg)

    switch msg.Type {
    case consts.ActionInsert:
        s.actionInsertData(ctx, msg)
    case consts.ActionUpdate:
        s.actionUpdate(ctx, msg)
    case consts.ActionDelete:
        s.actionDelete(ctx, msg)
    case consts.ActionUpdateOrInsert:
        s.actionInsertOrUpdate(ctx, msg)
    }
}
```

#### 2. Weaviate 服務集成
```go
// Weaviate 處理器實現
func (s *sVecWeaviate) OnMessage(ctx context.Context, message any) {
    var msg *amqp.Delivery
    _ = gconv.Struct(message, &msg)

    switch msg.Type {
    case consts.ActionCreateCollection:
        s.createCollection(ctx, msg)
    case consts.ActionCreateData:
        s.createData(ctx, msg)
    case consts.ActionUpdateVector:
        s.updateVector(ctx, msg)
    }
}
```

## ❓ 常見問題和故障排除

### 常見問題

#### 1. 連接失敗
**問題現象**：
```
RabbitMQ initial connection test failed: dial tcp 127.0.0.1:5672: connect: connection refused
```

**解決方案**：
1. 檢查 RabbitMQ 服務是否啟動
2. 確認連接 URL 配置正確
3. 檢查網絡連通性
4. 驗證用戶名密碼

**檢查命令**：
```bash
# 檢查 RabbitMQ 服務狀態
docker ps | grep rabbitmq

# 測試連接
telnet 127.0.0.1 5672

# 檢查 RabbitMQ 管理界面
curl http://127.0.0.1:15672
```

#### 2. 消息處理失敗
**問題現象**：
```
Message processing goroutine exception for prefix mariadb.: runtime error: invalid memory address
```

**解決方案**：
1. 檢查消息格式是否正確
2. 驗證處理器邏輯
3. 查看詳細錯誤堆棧
4. 檢查數據庫連接

#### 3. 重連頻繁
**問題現象**：
```
Reconnection too frequent, waiting 10s before retry
```

**解決方案**：
1. 檢查網絡穩定性
2. 調整重連參數
3. 檢查 RabbitMQ 服務穩定性
4. 監控系統資源使用

#### 4. 消息積壓
**問題現象**：
- 消息處理緩慢
- 內存使用增長
- 響應時間延長

**解決方案**：
1. 增加並發處理數
2. 優化處理器邏輯
3. 檢查數據庫性能
4. 監控系統資源

### 故障排除步驟

#### 1. 檢查服務狀態
```go
// 獲取服務狀態
status := service.MessageQ().GetConnectionStatus()
fmt.Printf("Service status: %+v\n", status)

// 檢查健康狀態
if !service.MessageQ().IsHealthy() {
    fmt.Println("Service is unhealthy")
}
```

#### 2. 查看日誌
```bash
# 查看 MessageQ 相關日誌
tail -f logs/MQ_*.log

# 查看錯誤日誌
grep "ERROR" logs/MQ_*.log

# 查看重連日誌
grep "reconnect" logs/MQ_*.log
```

#### 3. 監控指標
```go
// 監控關鍵指標
status := service.MessageQ().GetConnectionStatus()
fmt.Printf("Active workers: %d\n", status["active_message_workers"])
fmt.Printf("Reconnect count: %d\n", status["reconnect_count"])
fmt.Printf("Last reconnect: %s\n", status["last_reconnect_time"])
```

### 性能調優指南

#### 1. 並發優化
```go
// 根據 CPU 核心數調整並發數
runtime.NumCPU() * 2  // 建議並發數

// 監控並發使用情況
activeWorkers := len(s.messageWorkerSem)
if activeWorkers > s.messageWorkerCount * 0.8 {
    // 考慮增加並發數
}
```

#### 2. 內存優化
```go
// 定期清理資源
runtime.GC()

// 監控內存使用
var m runtime.MemStats
runtime.ReadMemStats(&m)
fmt.Printf("Memory usage: %d KB\n", m.Alloc/1024)
```

#### 3. 網絡優化
```go
// 調整連接參數
conn.Config.Heartbeat = 10 * time.Second
conn.Config.WriteTimeout = 30 * time.Second
conn.Config.ReadTimeout = 30 * time.Second
```

## 📚 參考資料

### 相關文檔
- [GoFrame 官方文檔](https://goframe.org/)
- [RabbitMQ 官方文檔](https://www.rabbitmq.com/documentation.html)
- [AMQP 0-9-1 協議規範](https://www.rabbitmq.com/amqp-0-9-1-reference.html)

### 代碼示例
- [MessageQ 測試文件](../internal/logic/messageQ/messageQ_test.go)
- [MariaDB 處理器實現](../internal/logic/mariadb/mariadb.go)
- [Weaviate 處理器實現](../internal/logic/vector/vec_weaviate.go)

### 最佳實踐
1. **錯誤處理**：始終實現完善的錯誤恢復機制
2. **日誌記錄**：記錄關鍵操作和錯誤信息
3. **監控告警**：設置連接狀態和性能監控
4. **資源管理**：及時釋放連接和通道資源
5. **測試覆蓋**：編寫完整的單元測試和集成測試

## 🔄 版本更新記錄

### v1.0.0 (當前版本)
- ✅ 基礎消息隊列功能
- ✅ 自動重連機制
- ✅ 並發處理支持
- ✅ 錯誤恢復機制
- ✅ 健康檢查功能

### 未來規劃
- 🔄 消息持久化支持
- 🔄 消息優先級處理
- 🔄 死信隊列支持
- 🔄 消息追蹤和審計
- 🔄 集群模式支持

---

**文檔維護**：本文檔由 DataSyncHub 開發團隊維護，如有問題請聯繫相關負責人。

**最後更新**：2025-01-16
