# MCP 知識轉移文檔

## 概述

本文檔為 brainHub 項目中 MCP (Model Context Protocol) 功能的知識轉移材料，旨在幫助團隊成員快速理解和掌握 MCP 的實現、使用和維護。

## 項目背景

### 業務需求
- **問題**: AI 模型需要與外部工具和服務進行交互，但缺乏標準化的協議
- **解決方案**: 集成 MCP 協議，提供統一的工具調用接口
- **價值**: 提高 AI 模型的實用性和擴展性

### 技術選型
- **協議**: Model Context Protocol (MCP)
- **實現語言**: Go 1.19+
- **依賴庫**: mark3labs/mcp-go
- **支持的客戶端類型**: STDIO, HTTP, SSE

## 架構設計

### 整體架構
```
┌─────────────────┐
│   LLM Router    │ ← 路由和配置管理
└─────────┬───────┘
          │
┌─────────▼───────┐
│ MCP Tool Manager│ ← 工具管理和調用
└─────────┬───────┘
          │
┌─────────▼───────┐
│MCP Client Manager│ ← 客戶端管理
└─────────┬───────┘
          │
┌─────────▼───────┐
│   MCP Clients   │ ← 具體的客戶端實現
│ - STDIO Client  │
│ - HTTP Client   │
│ - SSE Client    │
└─────────────────┘
```

### 核心組件

#### 1. MCP Tool Manager (`internal/llms/mcp/tool_manager.go`)
**職責**: 
- 管理工具定義和調用
- 提供統一的工具調用接口
- 處理工具調用結果

**關鍵方法**:
```go
type MCPToolManager interface {
    Initialize(ctx context.Context) error
    GetToolDefinitions(ctx context.Context) ([]ToolDefinition, error)
    CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*ToolResult, error)
    Close() error
}
```

#### 2. MCP Client Manager (`internal/llms/mcp/client_manager.go`)
**職責**:
- 管理多個 MCP 客戶端
- 處理客戶端生命週期
- 提供客戶端路由功能

**關鍵方法**:
```go
type MCPClientManager interface {
    Initialize(ctx context.Context) error
    GetClient(name string) MCPClient
    CallTool(ctx context.Context, clientName, toolName string, args map[string]interface{}) (*mcp.CallToolResult, error)
    Close() error
}
```

#### 3. MCP Clients (`internal/llms/mcp/clients/`)
**職責**:
- 實現具體的 MCP 協議客戶端
- 處理不同類型的連接（STDIO, HTTP, SSE）
- 管理連接狀態和錯誤處理

## 實現細節

### 1. LLM 集成

#### Azure OpenAI 集成 (`internal/llms/aoai/aoai.go`)
```go
// 在 Initialize 方法中添加 MCP 初始化
if params.MCPConfig != nil && params.MCPConfig.Enabled {
    mcpConfig := &mcp.MCPConfig{
        Enabled: params.MCPConfig.Enabled,
        Servers: s.convertMCPServerConfigs(params.MCPConfig.Servers),
        Global:  s.convertMCPGlobalConfig(params.MCPConfig.Global),
    }
    
    s.mcpToolManager = mcp.NewMCPToolManager(mcpConfig)
    if err := s.mcpToolManager.Initialize(ctx); err != nil {
        s.logger().Errorf(ctx, "Failed to initialize MCP tool manager: %v", err)
    }
}

// 在 Chat 方法中支持工具調用
aiMessage, err := s.getResponseWithTools(ctx, gconv.String(message.Content))
```

#### Claude 集成 (`internal/llms/claude/claude.go`)
```go
// 類似的集成模式，支持 Claude 的工具調用格式
func (c *Claude) processClaudeWithTools(ctx context.Context, userMessage claude.ClaudeMessage, toolDefinitions []common.ToolDefinition) (*claude.ClaudeResponse, error) {
    // 構建包含工具定義的請求
    request := c.buildClaudeRequest(toolDefinitions)
    
    // 調用 Claude API
    claudeResponse, err := c.sendClaudeRequestWithRetry(ctx, request)
    
    // 處理工具調用響應
    return c.processClaudeResponse(ctx, claudeResponse, toolDefinitions)
}
```

#### Gemini 集成 (`internal/llms/gemini/genai.go`)
```go
// Gemini 的工具調用實現
func (m *GeminiLLM) sendMessageWithTools(ctx context.Context, content *genai.Content) (*genai.GenerateContentResponse, error) {
    // 獲取工具定義並轉換為 Gemini 格式
    if len(toolDefinitions) > 0 {
        tools := m.convertToGeminiTools(toolDefinitions)
        m.config.Tools = tools
    }
    
    // 發送消息並處理工具調用
    return m.processGeminiWithTools(ctx, parts, toolDefinitions)
}
```

### 2. 配置管理

#### 配置結構 (`internal/model/llm/params.go`)
```go
type MCPConfig struct {
    Enabled bool                `json:"enabled"`
    Servers []MCPServerConfig   `json:"servers"`
    Global  MCPGlobalConfig     `json:"global"`
}

type MCPServerConfig struct {
    Name        string            `json:"name"`
    Type        string            `json:"type"`        // stdio, http, sse
    Description string            `json:"description"`
    Command     string            `json:"command,omitempty"`
    Args        []string          `json:"args,omitempty"`
    URL         string            `json:"url,omitempty"`
    Headers     map[string]string `json:"headers,omitempty"`
    Env         []string          `json:"env,omitempty"`
    Timeout     string            `json:"timeout"`
    RetryCount  int               `json:"retry_count"`
}
```

#### 路由器集成 (`internal/logic/llmRouter/router.go`)
```go
func (s *sAiRouter) getLLMConfig(ctx context.Context, llmParams *model.LLMParams, tenantParams g.Map) (cfg *llm.LLMsConfig) {
    // 獲取 MCP 配置
    mcpConfig := s.getMCPConfig(ctx, tenantParams)
    
    // 為所有配置添加 MCP 配置
    if cfg != nil {
        cfg.MCPConfig = mcpConfig
    }
    
    return
}
```

### 3. 錯誤處理

#### 自定義錯誤類型 (`internal/llms/mcp/errors.go`)
```go
type MCPError struct {
    Code    string
    Message string
    Cause   error
}

func NewMCPError(code, message string, cause error) *MCPError {
    return &MCPError{
        Code:    code,
        Message: message,
        Cause:   cause,
    }
}
```

#### 錯誤處理策略
1. **連接錯誤**: 自動重試，記錄日誌
2. **工具調用錯誤**: 返回錯誤信息，不中斷流程
3. **配置錯誤**: 使用默認配置，記錄警告

## 關鍵設計決策

### 1. 為什麼選擇 MCP 協議？
- **標準化**: 提供統一的工具調用接口
- **擴展性**: 支持多種客戶端類型
- **社區支持**: 活躍的開源社區

### 2. 為什麼使用工具管理器模式？
- **解耦**: 將工具管理與 LLM 實現分離
- **復用**: 多個 LLM 可以共享同一套工具
- **維護性**: 集中管理工具定義和調用邏輯

### 3. 為什麼支持多種客戶端類型？
- **靈活性**: 適應不同的部署場景
- **性能**: STDIO 適合本地工具，HTTP 適合遠程服務
- **實時性**: SSE 支持實時數據流

## 配置示例

### 開發環境配置
```yaml
mcp:
  enabled: true
  servers:
    - name: "dev-filesystem"
      type: "stdio"
      command: "mcp-server-filesystem"
      args: ["--root", "./workspace"]
      timeout: "30s"
      retry_count: 3
  
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 10
    cache_ttl: "5m"
    log_level: "debug"
```

### 生產環境配置
```yaml
mcp:
  enabled: true
  servers:
    - name: "prod-filesystem"
      type: "stdio"
      command: "/opt/mcp/bin/filesystem-server"
      args: ["--root", "/data", "--readonly"]
      timeout: "60s"
      retry_count: 5
    
    - name: "external-api"
      type: "http"
      url: "https://api.example.com/mcp"
      headers:
        Authorization: "Bearer ${MCP_API_TOKEN}"
      timeout: "120s"
      retry_count: 3
  
  global:
    enabled: true
    default_timeout: "60s"
    max_concurrent_calls: 50
    cache_ttl: "15m"
    log_level: "info"
```

## 測試策略

### 1. 單元測試
- **覆蓋範圍**: 所有核心組件
- **測試文件**: `*_test.go`
- **運行命令**: `go test ./internal/llms/mcp/...`

### 2. 集成測試
- **測試範圍**: LLM 與 MCP 的集成
- **測試文件**: `test/llm_mcp_integration_test.go`
- **運行命令**: `go test ./test/...`

### 3. 性能測試
- **測試範圍**: 工具調用性能和並發處理
- **測試文件**: `test/mcp_performance_test.go`
- **運行命令**: `go test -bench=. ./test/...`

## 監控和維護

### 1. 關鍵指標
- **工具調用成功率**: > 95%
- **平均響應時間**: < 500ms
- **並發處理能力**: > 1000 ops/sec
- **內存使用**: < 100MB

### 2. 日誌監控
```go
// 結構化日誌示例
logger.Info("tool_call_completed", 
    "tool_name", toolName,
    "duration", duration,
    "success", success,
    "client_name", clientName)
```

### 3. 健康檢查
```bash
# API 健康檢查
curl http://localhost:8000/health/mcp

# 工具可用性檢查
curl http://localhost:8000/api/v1/mcp/tools
```

## 故障排除指南

### 常見問題

#### 1. MCP 服務器連接失敗
**症狀**: 工具調用返回連接錯誤
**排查步驟**:
1. 檢查服務器配置是否正確
2. 驗證命令路徑和參數
3. 檢查網絡連接和防火牆設置
4. 查看服務器日誌

**解決方案**:
```bash
# 檢查命令是否存在
which mcp-server-filesystem

# 手動測試命令
mcp-server-filesystem --help

# 檢查網絡連接
curl -I https://api.example.com/mcp
```

#### 2. 工具調用超時
**症狀**: 工具調用經常超時
**排查步驟**:
1. 檢查超時配置是否合理
2. 監控服務器性能
3. 分析工具執行時間

**解決方案**:
```yaml
# 增加超時時間
mcp:
  servers:
    - name: "slow-tool"
      timeout: "120s"  # 增加到 2 分鐘
```

#### 3. 內存使用過高
**症狀**: 應用內存使用持續增長
**排查步驟**:
1. 使用 pprof 分析內存使用
2. 檢查是否有內存洩漏
3. 監控 goroutine 數量

**解決方案**:
```bash
# 內存分析
go tool pprof http://localhost:8000/debug/pprof/heap

# 調整 GC 參數
export GOGC=50
```

## 最佳實踐

### 1. 配置管理
- 使用環境變量管理敏感信息
- 分環境管理配置文件
- 定期備份配置

### 2. 性能優化
- 啟用工具調用緩存
- 設置合理的並發限制
- 監控關鍵性能指標

### 3. 安全考慮
- 限制工具訪問權限
- 驗證工具輸入參數
- 使用 HTTPS 加密通信

### 4. 維護建議
- 定期更新依賴庫
- 監控工具可用性
- 實施自動化測試

## 後續改進計劃

### 短期目標 (1-3 個月)
- [ ] 添加更多工具類型支持
- [ ] 優化工具調用性能
- [ ] 完善監控和告警

### 中期目標 (3-6 個月)
- [ ] 實現工具組合和鏈式調用
- [ ] 添加工具調用分析功能
- [ ] 支持動態工具註冊

### 長期目標 (6-12 個月)
- [ ] 實現分佈式工具調用
- [ ] 添加 AI 輔助的工具推薦
- [ ] 支持自定義協議擴展

## 聯繫信息

### 技術負責人
- **姓名**: [技術負責人姓名]
- **郵箱**: [郵箱地址]
- **微信**: [微信號]

### 團隊成員
- **開發**: [開發人員列表]
- **測試**: [測試人員列表]
- **運維**: [運維人員列表]

### 相關資源
- **代碼倉庫**: https://git.qbiai.com/Nanjing/NanjingRD/ai/chatgpt/brainhub
- **文檔中心**: [文檔鏈接]
- **監控面板**: [監控鏈接]
- **問題追蹤**: [問題追蹤鏈接]

通過本知識轉移文檔，團隊成員可以快速了解 MCP 功能的實現細節和維護要點。

---

# MCP 常見問題 FAQ

## 基礎概念

### Q1: 什麼是 MCP？
**A**: MCP (Model Context Protocol) 是一個標準化協議，允許 AI 模型與外部工具和服務進行交互。它提供了統一的接口來調用各種工具，如文件系統操作、網頁搜索、數據庫查詢等。

### Q2: MCP 與傳統 API 調用有什麼區別？
**A**:
- **標準化**: MCP 提供統一的協議格式，而傳統 API 各有不同
- **類型豐富**: 支持 STDIO、HTTP、SSE 等多種連接方式
- **AI 友好**: 專為 AI 模型設計，包含工具描述和參數定義
- **動態發現**: 支持動態發現和註冊工具

### Q3: brainHub 中的 MCP 支持哪些 LLM？
**A**: 目前支持：
- Azure OpenAI (GPT-3.5, GPT-4)
- Claude (通過 Google Vertex AI)
- Gemini (Google Vertex AI)

## 配置和部署

### Q4: 如何啟用 MCP 功能？
**A**: 在配置文件中設置：
```yaml
mcp:
  enabled: true
  servers:
    - name: "your-server"
      type: "stdio"  # 或 http, sse
      # 其他配置...
```

### Q5: 支援哪些類型的 MCP 服務器？
**A**:
- **STDIO**: 本地命令行工具和腳本
- **HTTP**: Web 服務和 REST API
- **SSE**: 實時數據流和事件驅動服務

### Q6: 如何配置多個 MCP 服務器？
**A**: 在 `servers` 數組中添加多個配置：
```yaml
mcp:
  servers:
    - name: "filesystem"
      type: "stdio"
      command: "mcp-server-filesystem"
    - name: "web-api"
      type: "http"
      url: "https://api.example.com/mcp"
```

## 開發和使用

### Q7: 如何創建自定義 MCP 工具？
**A**:
1. 實現 MCP 協議的服務器
2. 定義工具的輸入輸出格式
3. 在配置中註冊服務器
4. 測試工具調用

示例 Python 工具：
```python
from mcp import Server, Tool

server = Server("my-tool")

@server.tool("calculate")
def calculate(expression: str) -> str:
    return str(eval(expression))
```

### Q8: 工具調用失敗時如何處理？
**A**:
- 檢查工具參數是否正確
- 驗證服務器連接狀態
- 查看錯誤日誌
- 使用健康檢查 API 診斷問題

### Q9: 如何調試 MCP 工具調用？
**A**:
1. 啟用調試日誌：`log_level: "debug"`
2. 使用健康檢查：`GET /health/mcp`
3. 查看工具列表：`GET /api/v1/mcp/tools`
4. 手動測試工具：`POST /api/v1/mcp/tools/call`

## 性能和優化

### Q10: MCP 工具調用的性能如何？
**A**:
- 本地 STDIO 工具：通常 < 100ms
- HTTP 工具：取決於網絡延遲，通常 < 500ms
- 支持緩存和並發調用優化

### Q11: 如何優化 MCP 性能？
**A**:
- 啟用工具調用緩存
- 設置合理的並發限制
- 使用連接池
- 監控和調優超時設置

### Q12: MCP 支持多少並發調用？
**A**: 默認支持 10 個並發調用，可通過 `max_concurrent_calls` 配置調整。建議根據服務器性能設置合理值。

## 安全和權限

### Q13: MCP 有哪些安全考慮？
**A**:
- 限制工具訪問權限
- 驗證工具輸入參數
- 使用 HTTPS 加密通信
- 定期更新依賴庫

### Q14: 如何管理 MCP 工具的權限？
**A**:
- 在服務器級別限制文件訪問
- 使用環境變量管理敏感信息
- 實施 API 認證和授權
- 定期審計工具使用情況

## 故障排除

### Q15: MCP 服務器無法連接怎麼辦？
**A**:
1. 檢查服務器配置是否正確
2. 驗證命令路徑和參數
3. 檢查網絡連接
4. 查看服務器日誌
5. 使用 `TestConnection` 方法測試

### Q16: 工具調用經常超時怎麼辦？
**A**:
1. 增加超時時間配置
2. 檢查服務器性能
3. 優化工具實現
4. 使用異步調用

### Q17: 內存使用過高怎麼辦？
**A**:
1. 使用 pprof 分析內存使用
2. 檢查是否有內存洩漏
3. 調整 GC 參數
4. 使用對象池優化

## 監控和維護

### Q18: 如何監控 MCP 的運行狀態？
**A**:
- 使用健康檢查 API
- 監控工具調用統計
- 設置性能告警
- 查看結構化日誌

### Q19: 如何備份和恢復 MCP 配置？
**A**:
- 定期備份配置文件
- 使用版本控制管理配置
- 實施配置變更審計
- 測試配置恢復流程

### Q20: 如何升級 MCP 功能？
**A**:
1. 備份當前配置
2. 更新代碼和依賴
3. 測試新功能
4. 逐步部署到生產環境
5. 監控升級後的運行狀態
