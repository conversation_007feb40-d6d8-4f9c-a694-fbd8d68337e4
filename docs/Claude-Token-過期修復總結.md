# Claude Token 過期問題修復總結

## 問題描述

在長時間運行的 Claude 服務中，會出現 token 過期導致的 API 調用失敗問題。這是因為 Google Cloud OAuth2 access token 的有效期通常為 1 小時，當 token 過期後，API 會返回 401 或 403 錯誤，但原有的重試機制沒有處理這種認證失敗的情況。

## 問題根因分析

### 1. Token 管理機制不完善
- Claude 使用 Google Cloud OAuth2 token，有效期為 1 小時
- 雖然設置了 50 分鐘的緩存過期時間，但沒有處理實際 token 過期的情況
- 缺乏認證錯誤檢測和自動刷新機制

### 2. 重試邏輯不完整
- 當 API 返回 401/403 錯誤時，重試邏輯只是重複相同的請求
- 沒有檢查是否是因為 token 過期導致的認證失敗
- 沒有在重試前嘗試刷新 token

### 3. 與其他 LLM 實現的差異
- **Gemini**: 使用官方 SDK，內部自動處理 token 刷新
- **AoAi**: 使用 langchain 的 OpenAI 實現，有內建的認證處理
- **Claude**: 使用自定義 HTTP 客戶端，需要手動管理 token 生命週期

## 解決方案

### 1. 添加認證錯誤檢測
在 `sendClaudeRequest` 方法中添加對 401/403 錯誤的特殊處理：

```go
// 檢查是否為認證相關錯誤（401 Unauthorized 或 403 Forbidden）
if statusCode == 401 || statusCode == 403 {
    c.logger().Warningf(ctx, "Authentication error detected (status: %d), token may be expired", statusCode)
    return nil, gerror.NewCodef(gcode.CodeNotAuthorized, "Authentication failed with status code %d: %s", statusCode, responseBody)
}
```

### 2. 實現 Token 刷新機制
添加 `refreshAccessToken` 方法來強制刷新 access token：

```go
func (c *Claude) refreshAccessToken(ctx context.Context, credentialFile string) error {
    // 清除緩存中的 token
    cacheKey := fmt.Sprintf("claude_token_%s_%s", c.projectID, c.region)
    _, err := gcache.Remove(ctx, cacheKey)
    
    // 重新獲取 token
    err = c.initializeAccessToken(ctx, credentialFile)
    return err
}
```

### 3. 改進重試邏輯
在重試循環中集成 token 刷新邏輯：

```go
// 檢查是否為認證錯誤，如果是則嘗試刷新 token
if gerror.HasCode(lastErr, gcode.CodeNotAuthorized) && attempt < consts.ClaudeMaxRetryAttempts {
    if c.llmsConfig != nil && !g.IsEmpty(c.llmsConfig.Vertex.CredentialFile) {
        refreshErr := c.refreshAccessToken(ctx, c.llmsConfig.Vertex.CredentialFile)
        if refreshErr == nil {
            // 刷新成功，繼續重試（不需要額外等待）
            continue
        }
    }
}
```

### 4. 結構體改進
添加 `llmsConfig` 字段來存儲配置信息，以便在 token 刷新時使用：

```go
type Claude struct {
    // ... 其他字段
    llmsConfig      *llm.LLMsConfig // LLM 配置，用於 token 刷新
}
```

## 修改的文件

### 1. `internal/llms/claude/claude.go`
- 添加 `llmsConfig` 字段到 Claude 結構體
- 實現 `refreshAccessToken` 方法
- 改進 `sendClaudeRequest` 中的錯誤檢測
- 在 `getResponse` 和 `callClaudeAPI` 中集成 token 刷新邏輯
- 在 `Release` 方法中清理新增的字段

### 2. `internal/llms/claude/claude_token_refresh_test.go`
- 新增測試文件，包含以下測試：
  - `TestTokenRefreshMechanism`: 測試 token 刷新機制
  - `TestAuthenticationErrorDetection`: 測試認證錯誤檢測
  - `TestTokenCacheKeyGeneration`: 測試緩存鍵生成
  - `TestRetryLogicWithTokenRefresh`: 測試重試邏輯
  - `TestPayloadValidation`: 測試配置驗證

### 3. `ProductFile/api規格.md`
- 更新 API 規格文檔，記錄 token 管理優化

## 技術細節

### Token 刷新流程
1. **錯誤檢測**: API 返回 401/403 時，識別為認證錯誤
2. **緩存清理**: 清除過期的 token 緩存
3. **重新獲取**: 使用原有的 `initializeAccessToken` 方法獲取新 token
4. **立即重試**: 刷新成功後立即重試請求，不需要額外等待

### 併發安全
- 使用 GoFrame 的 `gcache` 進行線程安全的緩存操作
- Token 刷新過程中的併發請求會等待刷新完成
- 保持與現有併發安全架構的一致性

### 錯誤處理
- 使用 `gcode.CodeNotAuthorized` 標識認證錯誤
- 提供詳細的日誌記錄以便調試
- 在 token 刷新失敗時提供降級策略

## 預期效果

### 1. 自動恢復
- 當 token 過期時，系統能夠自動檢測並刷新 token
- 用戶無需重新初始化服務即可繼續使用

### 2. 提升穩定性
- 減少因 token 過期導致的服務中斷
- 提供更好的用戶體驗

### 3. 統一性
- 與其他 LLM 實現保持一致的錯誤恢復能力
- 遵循 GoFrame 框架的最佳實踐

## 測試驗證

所有相關測試均已通過：
- 新增的 token 刷新測試
- 現有的 Claude 功能測試
- 併發安全測試
- 項目整體編譯測試

## 部署建議

1. **監控**: 建議添加 token 刷新的監控指標
2. **日誌**: 關注認證錯誤和 token 刷新的日誌
3. **測試**: 在生產環境部署前進行長時間運行測試

這個修復確保了 Claude 服務在長時間運行時的穩定性，避免了因 token 過期導致的服務中斷問題。
