# MessageQ 架構圖說明

## 系統整體架構

```
                    DataSyncHub MessageQ 架構圖
                    
┌─────────────────────────────────────────────────────────────────────────────┐
│                           HTTP API Layer                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ ExecuteAPI  │  │  QueryAPI   │  │  VectorAPI  │  │   Others    │        │
│  │ Controller  │  │ Controller  │  │ Controller  │  │ Controllers │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                         Message Producer                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                    (HTTP API 產生消息發送到 RabbitMQ)                        │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                           RabbitMQ Broker                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                    Exchange: "dsh" (direct)                         │    │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │    │
│  │  │ mariadb.ams │  │mariadb.tenant│ │weaviate.ams │  │   others    │ │    │
│  │  │   routing   │  │   routing    │  │   routing   │  │   routing   │ │    │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                    │                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                      Auto-Generated Queue                          │    │
│  │              (Exclusive, Auto-Delete, Durable)                     │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                         MessageQ Consumer                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                    Connection Manager                               │    │
│  │  • Auto Reconnection with Exponential Backoff                      │    │
│  │  • Connection Health Monitoring                                     │    │
│  │  • Graceful Shutdown Support                                        │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                    │                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                   Message Dispatcher                                │    │
│  │  • Route Key Prefix Matching                                        │    │
│  │  • Concurrent Message Processing (10 workers)                       │    │
│  │  • Panic Recovery Mechanism                                         │    │
│  │  • Processing Time Monitoring                                       │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    ▼               ▼               ▼
┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────┐
│   MariaDB Handler   │  │  Weaviate Handler   │  │   Custom Handlers   │
├─────────────────────┤  ├─────────────────────┤  ├─────────────────────┤
│ • Route: mariadb.*  │  │ • Route: weaviate.* │  │ • Route: custom.*   │
│ • Actions:          │  │ • Actions:          │  │ • Actions:          │
│   - insert          │  │   - create_collection│  │   - custom_action   │
│   - update          │  │   - create_data     │  │   - other_action    │
│   - delete          │  │   - update_vector   │  │                     │
│   - updateOrInsert  │  │   - delete_collection│  │                     │
│   - createSchema    │  │   - create_tenant   │  │                     │
│   - createTable     │  │   - clear_data      │  │                     │
└─────────────────────┘  └─────────────────────┘  └─────────────────────┘
                    │               │               │
                    ▼               ▼               ▼
┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────┐
│      MariaDB        │  │      Weaviate       │  │   Other Services    │
│   Database Layer    │  │   Vector Database   │  │                     │
└─────────────────────┘  └─────────────────────┘  └─────────────────────┘
```

## 消息流程圖

```
消息處理流程

1. 消息接收
   ┌─────────────┐
   │ RabbitMQ    │
   │ Message     │ ──┐
   └─────────────┘   │
                     ▼
2. 路由匹配         ┌─────────────────┐
   ┌─────────────┐  │ dispatchMessage │
   │ Route Key   │─▶│ (路由鍵匹配)     │
   │ Matching    │  └─────────────────┘
   └─────────────┘           │
                             ▼
3. 並發處理         ┌─────────────────┐
   ┌─────────────┐  │ processMessage  │
   │ Goroutine   │◀─│ WithRecovery    │
   │ Pool (10)   │  │ (並發+恢復)      │
   └─────────────┘  └─────────────────┘
                             │
                             ▼
4. 業務處理         ┌─────────────────┐
   ┌─────────────┐  │ Handler.        │
   │ Business    │◀─│ OnMessage       │
   │ Logic       │  │ (業務邏輯)       │
   └─────────────┘  └─────────────────┘
                             │
                             ▼
5. 數據持久化       ┌─────────────────┐
   ┌─────────────┐  │ Database        │
   │ MariaDB/    │◀─│ Operations      │
   │ Weaviate    │  │ (數據操作)       │
   └─────────────┘  └─────────────────┘
```

## 重連機制流程圖

```
連接管理和重連機制

┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 初始連接     │───▶│ 連接測試     │───▶│ 啟動服務     │
└─────────────┘    └─────────────┘    └─────────────┘
                                             │
                                             ▼
                                    ┌─────────────┐
                                    │ 消息消費     │
                                    │ 循環        │
                                    └─────────────┘
                                             │
                                             ▼
                                    ┌─────────────┐
                                    │ 連接監控     │ ──┐
                                    └─────────────┘   │
                                             │        │
                                    連接斷開  ▼        │ 連接正常
                                    ┌─────────────┐   │
                                    │ 觸發重連     │   │
                                    └─────────────┘   │
                                             │        │
                                             ▼        │
                                    ┌─────────────┐   │
                                    │ 重連邏輯     │   │
                                    │ (指數退避)   │   │
                                    └─────────────┘   │
                                             │        │
                                    ┌────────┼────────┘
                                    │        │
                                重連成功 ▼   重連失敗
                            ┌─────────────┐    │
                            │ 恢復服務     │    │
                            └─────────────┘    ▼
                                    ▲    ┌─────────────┐
                                    │    │ 等待重試     │
                                    └────│ (退避算法)   │
                                         └─────────────┘
```

## 並發處理架構

```
並發處理和錯誤恢復

                    ┌─────────────────┐
                    │   消息接收       │
                    └─────────────────┘
                             │
                             ▼
                    ┌─────────────────┐
                    │   路由分發       │
                    └─────────────────┘
                             │
                ┌────────────┼────────────┐
                ▼            ▼            ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │   Worker 1      │ │   Worker 2      │ │   Worker N      │
    │ ┌─────────────┐ │ │ ┌─────────────┐ │ │ ┌─────────────┐ │
    │ │ Semaphore   │ │ │ │ Semaphore   │ │ │ │ Semaphore   │ │
    │ │ Control     │ │ │ │ Control     │ │ │ │ Control     │ │
    │ └─────────────┘ │ │ └─────────────┘ │ │ └─────────────┘ │
    │ ┌─────────────┐ │ │ ┌─────────────┐ │ │ ┌─────────────┐ │
    │ │ Panic       │ │ │ │ Panic       │ │ │ │ Panic       │ │
    │ │ Recovery    │ │ │ │ Recovery    │ │ │ │ Recovery    │ │
    │ └─────────────┘ │ │ └─────────────┘ │ │ └─────────────┘ │
    │ ┌─────────────┐ │ │ ┌─────────────┐ │ │ ┌─────────────┐ │
    │ │ Message     │ │ │ │ Message     │ │ │ │ Message     │ │
    │ │ Processing  │ │ │ │ Processing  │ │ │ │ Processing  │ │
    │ └─────────────┘ │ │ └─────────────┘ │ │ └─────────────┘ │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
                             │
                             ▼
                    ┌─────────────────┐
                    │   處理結果       │
                    │   日誌記錄       │
                    └─────────────────┘
```

這些架構圖展示了 MessageQ 的完整設計思路和實現邏輯，有助於開發團隊理解系統架構和進行相似功能的實現。
