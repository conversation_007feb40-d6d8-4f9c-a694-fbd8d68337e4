# MCP 部署指南

## 概述

本文檔提供了在 brainHub 項目中部署 MCP (Model Context Protocol) 功能的完整指南，包括環境準備、依賴安裝、配置管理和部署步驟。

## 系統要求

### 硬件要求

#### 最低配置
- CPU: 2 核心
- 內存: 4GB RAM
- 存儲: 20GB 可用空間
- 網絡: 穩定的互聯網連接

#### 推薦配置
- CPU: 4+ 核心
- 內存: 8GB+ RAM
- 存儲: 50GB+ SSD
- 網絡: 高速互聯網連接

### 軟件要求

#### 基礎環境
- 操作系統: Linux (Ubuntu 20.04+), macOS (10.15+), Windows 10+
- Go: 1.19 或更高版本
- Git: 2.20 或更高版本

#### 可選依賴
- Docker: 20.10+ (容器化部署)
- Kubernetes: 1.20+ (集群部署)
- Redis: 6.0+ (緩存)
- PostgreSQL: 12+ (數據存儲)

## 環境準備

### 1. Go 環境安裝

#### Linux/macOS
```bash
# 下載並安裝 Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz

# 設置環境變量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
echo 'export GOBIN=$GOPATH/bin' >> ~/.bashrc
source ~/.bashrc

# 驗證安裝
go version
```

#### Windows
```powershell
# 下載並安裝 Go MSI 包
# 從 https://go.dev/dl/ 下載 Windows 安裝包

# 設置環境變量
$env:GOPATH = "C:\Users\<USER>\go"
$env:GOBIN = "$env:GOPATH\bin"

# 驗證安裝
go version
```

### 2. 項目克隆和依賴安裝

```bash
# 克隆項目
git clone https://git.qbiai.com/Nanjing/NanjingRD/ai/chatgpt/brainhub.git
cd brainhub

# 安裝 Go 依賴
go mod download
go mod tidy

# 驗證依賴
go mod verify
```

### 3. MCP 服務器安裝

#### Python MCP 服務器
```bash
# 安裝 Python 和 pip
sudo apt-get update
sudo apt-get install python3 python3-pip

# 安裝 MCP 服務器
pip3 install mcp-server-filesystem
pip3 install mcp-server-web-search
pip3 install mcp-server-database
```

#### Node.js MCP 服務器
```bash
# 安裝 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安裝 MCP 服務器
npm install -g @mcp/server-web-api
npm install -g @mcp/server-tools
```

## 配置管理

### 1. 基礎配置

#### 開發環境配置 (`config/development.yaml`)
```yaml
# 數據庫配置
database:
  default:
    host: "localhost"
    port: 5432
    user: "brainhub_dev"
    pass: "dev_password"
    name: "brainhub_dev"

# MCP 配置
mcp:
  enabled: true
  servers:
    - name: "dev-filesystem"
      type: "stdio"
      description: "開發環境文件系統"
      command: "mcp-server-filesystem"
      args: ["--root", "./workspace"]
      timeout: "30s"
      retry_count: 3
    
    - name: "dev-web-search"
      type: "http"
      description: "開發環境網頁搜索"
      url: "http://localhost:8080/mcp"
      headers:
        Authorization: "Bearer dev-token"
      timeout: "60s"
      retry_count: 2
  
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 10
    cache_ttl: "5m"
    log_level: "debug"

# 日誌配置
logger:
  level: "debug"
  stdout: true
  file: "./logs/brainhub-dev.log"
```

#### 生產環境配置 (`config/production.yaml`)
```yaml
# 數據庫配置
database:
  default:
    host: "${DB_HOST}"
    port: "${DB_PORT}"
    user: "${DB_USER}"
    pass: "${DB_PASS}"
    name: "${DB_NAME}"

# MCP 配置
mcp:
  enabled: true
  servers:
    - name: "prod-filesystem"
      type: "stdio"
      description: "生產環境文件系統"
      command: "/opt/mcp/bin/filesystem-server"
      args: ["--root", "/data", "--readonly"]
      env:
        - "MCP_LOG_LEVEL=warn"
        - "MCP_SECURITY_MODE=strict"
      timeout: "60s"
      retry_count: 5
    
    - name: "prod-web-api"
      type: "http"
      description: "生產環境 Web API"
      url: "${MCP_WEB_API_URL}"
      headers:
        Authorization: "Bearer ${MCP_API_TOKEN}"
        User-Agent: "brainHub/1.0"
      timeout: "120s"
      retry_count: 3
  
  global:
    enabled: true
    default_timeout: "60s"
    max_concurrent_calls: 50
    cache_ttl: "15m"
    log_level: "info"

# 日誌配置
logger:
  level: "info"
  stdout: false
  file: "/var/log/brainhub/brainhub.log"
  rotate: true
  maxSize: "100MB"
  maxBackups: 10
  maxAge: "30d"
```

### 2. 環境變量配置

#### 開發環境 (`.env.development`)
```bash
# 數據庫配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=brainhub_dev
DB_PASS=dev_password
DB_NAME=brainhub_dev

# MCP 配置
MCP_API_TOKEN=dev-api-token
MCP_WEB_API_URL=http://localhost:8080/mcp
MCP_LOG_LEVEL=debug

# 應用配置
APP_ENV=development
APP_PORT=8000
APP_DEBUG=true
```

#### 生產環境 (`.env.production`)
```bash
# 數據庫配置
DB_HOST=prod-db.example.com
DB_PORT=5432
DB_USER=brainhub_prod
DB_PASS=secure_prod_password
DB_NAME=brainhub_prod

# MCP 配置
MCP_API_TOKEN=secure-prod-api-token
MCP_WEB_API_URL=https://api.example.com/mcp
MCP_LOG_LEVEL=info

# 應用配置
APP_ENV=production
APP_PORT=8000
APP_DEBUG=false

# 安全配置
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
```

### 3. Nacos 配置管理

#### Nacos 配置示例
```yaml
# Data ID: brainhub-mcp-config
# Group: DEFAULT_GROUP
# 配置格式: YAML

mcp:
  enabled: true
  servers:
    - name: "nacos-filesystem"
      type: "stdio"
      description: "Nacos 管理的文件系統服務"
      command: "/opt/mcp/bin/filesystem-server"
      args: ["--root", "/data"]
      timeout: "30s"
      retry_count: 3
    
    - name: "nacos-web-api"
      type: "http"
      description: "Nacos 管理的 Web API 服務"
      url: "https://api.example.com/mcp"
      headers:
        Authorization: "Bearer ${MCP_TOKEN}"
      timeout: "60s"
      retry_count: 2
  
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 20
    cache_ttl: "10m"
    log_level: "info"
```

## 部署步驟

### 1. 本地開發部署

```bash
# 1. 準備環境
cd brainhub
cp .env.development.example .env.development
# 編輯 .env.development 文件，設置正確的配置

# 2. 安裝依賴
go mod download

# 3. 運行數據庫遷移
go run cmd/migrate/main.go

# 4. 啟動應用
go run main.go

# 5. 驗證部署
curl http://localhost:8000/health
curl http://localhost:8000/health/mcp
```

### 2. Docker 容器部署

#### Dockerfile
```dockerfile
# 多階段構建
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o brainhub .

# 運行階段
FROM alpine:latest

RUN apk --no-cache add ca-certificates python3 py3-pip nodejs npm
WORKDIR /root/

# 安裝 MCP 服務器
RUN pip3 install mcp-server-filesystem mcp-server-web-search
RUN npm install -g @mcp/server-web-api

# 複製應用
COPY --from=builder /app/brainhub .
COPY --from=builder /app/config ./config

# 創建必要的目錄
RUN mkdir -p /var/log/brainhub /data/workspace

EXPOSE 8000

CMD ["./brainhub"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  brainhub:
    build: .
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - DB_HOST=postgres
      - DB_USER=brainhub
      - DB_PASS=password
      - DB_NAME=brainhub
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/var/log/brainhub
      - ./data:/data
    networks:
      - brainhub-network

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=brainhub
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=brainhub
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - brainhub-network

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - brainhub-network

volumes:
  postgres_data:
  redis_data:

networks:
  brainhub-network:
    driver: bridge
```

#### 部署命令
```bash
# 構建和啟動
docker-compose up -d

# 查看日誌
docker-compose logs -f brainhub

# 停止服務
docker-compose down

# 更新部署
docker-compose pull
docker-compose up -d --force-recreate
```

### 3. Kubernetes 部署

#### 部署配置 (`k8s/deployment.yaml`)
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: brainhub
  labels:
    app: brainhub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: brainhub
  template:
    metadata:
      labels:
        app: brainhub
    spec:
      containers:
      - name: brainhub
        image: brainhub:latest
        ports:
        - containerPort: 8000
        env:
        - name: APP_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: brainhub-secrets
              key: db-host
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: brainhub-secrets
              key: db-user
        - name: DB_PASS
          valueFrom:
            secretKeyRef:
              name: brainhub-secrets
              key: db-pass
        - name: MCP_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: brainhub-secrets
              key: mcp-api-token
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/mcp
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: data-volume
          mountPath: /data
      volumes:
      - name: config-volume
        configMap:
          name: brainhub-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: brainhub-data-pvc
```

#### 服務配置 (`k8s/service.yaml`)
```yaml
apiVersion: v1
kind: Service
metadata:
  name: brainhub-service
spec:
  selector:
    app: brainhub
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: LoadBalancer
```

#### 部署命令
```bash
# 創建命名空間
kubectl create namespace brainhub

# 應用配置
kubectl apply -f k8s/ -n brainhub

# 查看部署狀態
kubectl get pods -n brainhub
kubectl get services -n brainhub

# 查看日誌
kubectl logs -f deployment/brainhub -n brainhub

# 擴展副本
kubectl scale deployment brainhub --replicas=5 -n brainhub
```

## 監控和維護

### 1. 健康檢查

```bash
# 應用健康檢查
curl http://localhost:8000/health

# MCP 健康檢查
curl http://localhost:8000/health/mcp

# 詳細狀態檢查
curl http://localhost:8000/status
```

### 2. 日誌管理

```bash
# 查看應用日誌
tail -f /var/log/brainhub/brainhub.log

# 查看 MCP 日誌
tail -f /var/log/brainhub/mcp.log

# 日誌輪轉配置
sudo logrotate -f /etc/logrotate.d/brainhub
```

### 3. 性能監控

```bash
# 查看系統資源使用
htop
iostat -x 1
netstat -tuln

# 查看應用指標
curl http://localhost:8000/metrics

# 數據庫連接檢查
psql -h localhost -U brainhub -d brainhub -c "SELECT 1;"
```

## 故障排除

### 常見問題

1. **MCP 服務器連接失敗**
   ```bash
   # 檢查 MCP 服務器狀態
   ps aux | grep mcp
   netstat -tuln | grep 8080
   
   # 重啟 MCP 服務器
   sudo systemctl restart mcp-server
   ```

2. **數據庫連接問題**
   ```bash
   # 檢查數據庫連接
   pg_isready -h localhost -p 5432
   
   # 檢查數據庫日誌
   sudo tail -f /var/log/postgresql/postgresql-14-main.log
   ```

3. **內存不足**
   ```bash
   # 檢查內存使用
   free -h
   
   # 調整 Go GC 參數
   export GOGC=50
   ```

### 調試技巧

```bash
# 啟用調試模式
export APP_DEBUG=true
export MCP_LOG_LEVEL=debug

# 使用 pprof 分析性能
go tool pprof http://localhost:8000/debug/pprof/profile

# 檢查 goroutine 洩漏
go tool pprof http://localhost:8000/debug/pprof/goroutine
```

## 安全考慮

### 1. 網絡安全
- 使用 HTTPS 加密通信
- 配置防火牆規則
- 限制 API 訪問權限

### 2. 數據安全
- 加密敏感配置
- 定期備份數據
- 實施訪問控制

### 3. 應用安全
- 定期更新依賴
- 使用安全掃描工具
- 實施安全審計

通過遵循本部署指南，可以安全、穩定地部署 brainHub 項目的 MCP 功能。
