# 通用准则
- 請使用中文回覆我
- 請用審視的目光，來看待我的輸入。 如果有問題請指出，並跳出我的思考框架給出我建議
- 如果我的方向有錯，請明確指出，讓我瞬間清醒

# Junie 项目指南

## 1. 项目概述

Junie 是一个基于 Go 语言开发的 AI 聊天服务平台，它能够根据租户和用户信息路由请求到不同的 AI 模型，并提供多渠道（omnichannel）通信能力。该项目主要集成了 Google 的生成式 AI 服务（Gemini），并支持文件存储、网页爬取等功能。

## 2. 技术栈

### 2.1 核心技术

- **编程语言**：Go 1.23.0
- **Web 框架**：GoFrame v2.9.0
- **AI 服务**：Google Generative AI (Gemini)
- **存储服务**：Google Cloud Storage
- **缓存**：Redis
- **Web 自动化**：Playwright
- **容器化**：Docker
- **编排**：Kubernetes

### 2.2 主要依赖

- `cloud.google.com/go/storage`：Google Cloud Storage 客户端
- `google.golang.org/genai`：Google Generative AI 客户端
- `github.com/gogf/gf/v2`：GoFrame 框架
- `github.com/playwright-community/playwright-go`：Web 自动化工具
- `github.com/JohannesKaufmann/html-to-markdown`：HTML 转 Markdown 工具
- `github.com/kkdai/youtube/v2`：YouTube 集成

## 3. 项目结构

```
brainHub/
├── api/                    # API 定义
│   ├── brain/              # 大脑 API
│   ├── hello/              # Hello API
│   ├── omnichannel/        # 多渠道 API
│   └── storage/            # 存储 API
├── docs/                   # 文档
├── hack/                   # 构建和部署脚本
├── internal/               # 内部实现
│   ├── cmd/                # 命令行入口
│   ├── consts/             # 常量定义
│   ├── controller/         # 控制器
│   ├── llms/               # LLM 服务接口和实现
│   ├── logic/              # 业务逻辑
│   ├── model/              # 数据模型
│   ├── packed/             # 打包资源
│   └── service/            # 服务接口
├── manifest/               # 部署清单
│   ├── config/             # 配置文件
│   ├── deploy/             # 部署文件
│   └── docker/             # Docker 相关文件
└── utility/                # 工具函数
```

## 4. 核心组件

### 4.1 控制器（Controller）

控制器负责处理 HTTP 请求，并调用相应的服务。主要控制器包括：

- **Brain**：处理 AI 聊天请求
- **Omnichannel**：处理多渠道通信请求
- **Storage**：处理文件存储请求

### 4.2 服务路由（Service Router）

`AiRouter` 是一个核心组件，负责根据租户和用户信息选择合适的 AI 模型。它维护了一个键值映射，用于缓存租户配置。

### 4.3 LLM 接口

`ILLMs` 接口定义了与 AI 模型交互的标准方法：

- **Initialize**：初始化 AI 模型
- **Chat**：发送消息并获取响应
- **Release**：释放资源

目前主要实现是 `GeminiLLM`，它与 Google 的 Gemini 模型集成。

### 4.4 存储服务

提供文件上传、删除和 URL 更新等功能，与 Google Cloud Storage 集成。

## 5. 编译与构建

项目使用 GoFrame CLI 工具进行构建和代码生成。主要的构建命令包括：

- `make build`：构建二进制文件
- `make image`：构建 Docker 镜像
- `make deploy`：部署到 Kubernetes

构建过程由 `hack/hack.mk` 和 `hack/hack-cli.mk` 中的 Makefile 规则定义。

## 6. 部署方式

### 6.1 Docker 容器化

项目使用 `loads/alpine:3.8` 作为基础镜像，将编译好的二进制文件和资源文件打包到容器中。

### 6.2 Kubernetes 部署

使用 Kustomize 管理 Kubernetes 配置，支持不同环境的配置覆盖。基本部署单元包括：

- Deployment：定义应用的副本数和容器配置
- Service：暴露应用的端口
- ConfigMap：管理应用配置

## 7. 开发指南

### 7.1 添加新的 API

1. 在 `api/` 目录下定义新的 API 接口
2. 使用 `gf gen ctrl` 生成控制器代码
3. 实现控制器逻辑
4. 在 `cmd.go` 中注册新的控制器

### 7.2 添加新的 AI 模型

1. 在 `internal/llms/` 目录下创建新的包
2. 实现 `ILLMs` 接口
3. 在 `AiRouter` 中添加对新模型的支持

### 7.3 配置管理

应用配置存储在 `manifest/config/config.yaml` 中，可以通过环境变量覆盖。

## 8. 未来发展方向

1. 支持更多的 AI 模型和服务提供商
2. 增强多租户隔离和配置管理
3. 改进网页爬取和内容处理能力
4. 优化性能和资源使用
5. 增加更多的集成测试和监控

## 9. 常见问题与解决方案

### 9.1 配置问题

确保 `key/key.json` 文件存在并包含正确的 Google Cloud 凭证。

### 9.2 构建问题

如果遇到构建错误，尝试先运行 `make cli.install` 安装最新的 GoFrame CLI 工具。

### 9.3 部署问题

检查 Kubernetes 配置和命名空间设置，确保镜像正确推送到仓库。