// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/model/omnichannel"
	"context"
)

type (
	IOmniChannel interface {
		CreateAnswer(ctx context.Context, in interface{}) (answer []*omnichannel.Root, err error)
	}
)

var (
	localOmniChannel IOmniChannel
)

func OmniChannel() IOmniChannel {
	if localOmniChannel == nil {
		panic("implement not found for interface IOmniChannel, forgot register?")
	}
	return localOmniChannel
}

func RegisterOmniChannel(i IOmniChannel) {
	localOmniChannel = i
}
