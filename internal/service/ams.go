// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/model"
	"context"
)

type (
	IAMS interface {
		GetResources(ctx context.Context, tenantID string, serviceID string) (asset *model.Asset, err error)
	}
)

var (
	localAMS IAMS
)

func AMS() IAMS {
	if localAMS == nil {
		panic("implement not found for interface IAMS, forgot register?")
	}
	return localAMS
}

func RegisterAMS(i IAMS) {
	localAMS = i
}
