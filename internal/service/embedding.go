// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/embeddings"
	"brainHub/internal/model/embedding"
	"context"
)

type (
	IEmbedding interface {
		// GenerateEmbeddings 生成文本向量（使用默認配置）
		GenerateEmbeddings(ctx context.Context, req *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error)
		// GenerateEmbeddingsWithContext 生成文本向量（帶租戶和服務上下文）
		GenerateEmbeddingsWithContext(ctx context.Context, tenantID string, serviceID string, req *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error)
		// GenerateSingleEmbedding 生成單個文本向量
		GenerateSingleEmbedding(ctx context.Context, tenantID string, serviceID string, text string) ([]float32, error)
		// GetEmbeddingProvider 獲取 embedding 提供商實例
		GetEmbeddingProvider(ctx context.Context, tenantID string, serviceID string) (embeddings.IEmbeddings, error)
		// GetSupportedProviders 獲取支持的提供商列表
		GetSupportedProviders() []string
		// ValidateEmbeddingConfig 驗證 embedding 配置
		ValidateEmbeddingConfig(config *embedding.EmbeddingConfig) error
		// GetEmbeddingMetrics 獲取 embedding 性能指標
		GetEmbeddingMetrics(ctx context.Context, tenantID string, serviceID string) (*embedding.EmbeddingMetrics, error)
		// CheckEmbeddingHealth 檢查 embedding 服務健康狀態
		CheckEmbeddingHealth(ctx context.Context, tenantID string, serviceID string) (bool, error)
		// ReleaseEmbeddingProvider 釋放 embedding 提供商資源
		ReleaseEmbeddingProvider(ctx context.Context, tenantID string, serviceID string) error
	}
)

var (
	localEmbedding IEmbedding
)

func Embedding() IEmbedding {
	if localEmbedding == nil {
		panic("implement not found for interface IEmbedding, forgot register?")
	}
	return localEmbedding
}

func RegisterEmbedding(i IEmbedding) {
	localEmbedding = i
}
