// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/llms"
	"brainHub/internal/model"
	"context"
)

type (
	IAiRouter interface {
		// RemoveKeyParams removes the specified key and its associated parameters from the key-to-parameters mapping.
		RemoveKeyParams(ctx context.Context, key string)
		Select(ctx context.Context, in *model.AiSelectorInput) (llmObj llms.ILLMs, err error)
	}
)

var (
	localAiRouter IAiRouter
)

func AiRouter() IAiRouter {
	if localAiRouter == nil {
		panic("implement not found for interface IAiRouter, forgot register?")
	}
	return localAiRouter
}

func RegisterAiRouter(i IAiRouter) {
	localAiRouter = i
}
