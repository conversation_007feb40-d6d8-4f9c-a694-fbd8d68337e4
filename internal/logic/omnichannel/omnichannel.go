package omnichannel

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/omnichannel"
	"brainHub/internal/service"
	"context"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
)

func init() {
	service.RegisterOmniChannel(New())
}

type sOmniChannel struct {
}

func New() service.IOmniChannel {
	return &sOmniChannel{}
}
func (s *sOmniChannel) logger() glog.ILogger {

	return g.Log().Cat(consts.CatalogOmniChannel)
}

func (s *sOmniChannel) CreateAnswer(ctx context.Context, in interface{}) (answer []*omnichannel.Root, err error) {
	s.logger().Infof(ctx, "create answer by : %v", gjson.New(in).MustToJsonIndentString())
	if in == nil {
		err = gerror.New("input answer is nil")
		s.logger().Error(ctx, err)
		return
	}

	answer = make([]*omnichannel.Root, 0)

	// 根據輸入類型智能處理
	switch input := in.(type) {
	case g.MapStrStr:
		// 現有邏輯：創建 Text 類型 + QuickReply
		return s.createTextAnswer(ctx, input)

	case *omnichannel.CarouselInput:
		// 新邏輯：創建 Carousel 類型
		return s.createCarouselAnswer(ctx, input)

	case []*omnichannel.Root:
		// 直接返回已構建的回答
		return input, nil

	default:
		err = gerror.Newf("unsupported input type: %T", in)
		s.logger().Error(ctx, err)
		return
	}
}

// createTextAnswer 創建 Text 類型回答（原有邏輯）
func (s *sOmniChannel) createTextAnswer(ctx context.Context, in g.MapStrStr) (answer []*omnichannel.Root, err error) {
	answer = make([]*omnichannel.Root, 0)

	mapAnswers := gmap.NewStrStrMapFrom(in)
	answerText := gstr.Join(mapAnswers.Keys(), consts.AnswerSep)
	// 每一個 value ： <ele ^^ ele>
	quickReply := gstr.SplitAndTrim(gstr.Join(mapAnswers.Values(), consts.QRSep), consts.QRSep)

	var ans = &omnichannel.Root{
		Text: answerText,
		Type: "Text",
	}

	fnCreateItem := func(qrStr string) *omnichannel.Item {
		return &omnichannel.Item{
			Action: &omnichannel.Action{
				Data:        qrStr,
				DisplayText: qrStr,
				Title:       qrStr,
				Type:        "Postback",
			},
			Type: "Action",
		}
	}

	if len(quickReply) > 0 {
		var items = make([]*omnichannel.Item, 0)
		for _, qrStr := range quickReply {
			items = append(items, fnCreateItem(qrStr))
		}

		ans.QuickReply = &omnichannel.QuickReply{
			Items: items,
		}
	}
	answer = append(answer, ans)

	return
}

// createCarouselAnswer 創建 Carousel 類型回答
func (s *sOmniChannel) createCarouselAnswer(ctx context.Context, in *omnichannel.CarouselInput) (answer []*omnichannel.Root, err error) {
	if in == nil {
		err = gerror.New("carousel input is nil")
		s.logger().Error(ctx, err)
		return
	}

	elements := make([]*omnichannel.Element, 0, len(in.Elements))

	for _, elem := range in.Elements {
		if elem == nil {
			continue
		}

		actions := make([]*omnichannel.Action, 0, len(elem.Actions))

		for _, action := range elem.Actions {
			if action == nil {
				continue
			}

			actions = append(actions, &omnichannel.Action{
				Type:        action.Type,
				Title:       action.Title,
				Data:        action.Data,
				DisplayText: action.DisplayText,
				Url:         action.Url,
			})
		}

		elements = append(elements, &omnichannel.Element{
			Title:    elem.Title,
			Subtitle: elem.Subtitle,
			ImageUrl: elem.ImageUrl,
			Actions:  actions,
		})
	}

	answer = []*omnichannel.Root{
		{
			Type:     "Carousel",
			Text:     in.Text,
			Elements: elements,
		},
	}

	s.logger().Infof(ctx, "created carousel answer with %d elements", len(elements))
	return
}
