package messageQ

import (
	"brainHub/boot"
	"brainHub/internal/consts"
	"brainHub/internal/service"
	"context"
	"sync"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	amqp "github.com/rabbitmq/amqp091-go"
)

func init() {
	service.RegisterMessageQ(New())
}

type sMessageQ struct {
	// RabbitMQ 連接管理
	conn    *amqp.Connection
	channel *amqp.Channel
	url     string

	// 消息處理器映射
	messageHandlers map[string]consts.OnMessage

	// 連接狀態管理
	isConnected  bool
	isConnecting bool
	isClosed     bool

	// 控制通道
	reconnectChan chan struct{}
	closeChan     chan struct{}

	// 同步控制
	mu sync.RWMutex

	// 重連配置
	maxRetries           int
	retryInterval        time.Duration
	maxReconnectInterval time.Duration
	reconnectCount       int
	lastReconnectTime    time.Time

	// 消息處理並發控制
	messageWorkerCount int
	messageWorkerSem   chan struct{}
}

func FailOnError(err error) {
	if err != nil {
		panic(err)
	}
}

func New() service.IMessageQ {
	boot.WaitReady()
	g.Log().Notice(context.TODO(), "ready to create MQ")

	ctx := gctx.GetInitCtx()
	vUrl, err := g.Cfg().Get(ctx, consts.ConfigRabbitMQURL, consts.StringEmpty)
	FailOnError(err)
	if vUrl == nil || vUrl.IsEmpty() {
		panic("rabbitMQ.url is nil")
	}

	s := &sMessageQ{
		url:                  vUrl.String(),
		messageHandlers:      make(map[string]consts.OnMessage),
		reconnectChan:        make(chan struct{}, consts.NetworkChannelBufferSize),
		closeChan:            make(chan struct{}),
		maxRetries:           consts.NetworkRetryHigh,                // 最大重試次數
		retryInterval:        consts.NetworkRetryIntervalFiveSeconds, // 重試間隔
		maxReconnectInterval: consts.NetworkReconnectIntervalMax,     // 最大重連間隔
		messageWorkerCount:   consts.NetworkMessageWorkerCount,       // 並發處理數
		messageWorkerSem:     make(chan struct{}, consts.NetworkMessageWorkerCount),
	}

	// 初始化連接
	err = s.connect(ctx)
	FailOnError(err)

	return s
}

func (s *sMessageQ) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogMQ)
}

// connect 建立 RabbitMQ 連接
func (s *sMessageQ) connect(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isConnecting {
		return nil
	}
	s.isConnecting = true
	defer func() { s.isConnecting = false }()

	var err error
	s.conn, err = amqp.Dial(s.url)
	if err != nil {
		s.logger().Errorf(ctx, "Failed to connect to RabbitMQ: %v", err)
		return err
	}

	s.channel, err = s.conn.Channel()
	if err != nil {
		s.logger().Errorf(ctx, "Failed to open channel: %v", err)
		s.conn.Close()
		return err
	}

	err = s.channel.ExchangeDeclare(
		consts.ExchangeName,
		consts.NetworkExchangeTypeDirect,
		true,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		s.logger().Errorf(ctx, "Failed to declare exchange: %v", err)
		s.channel.Close()
		s.conn.Close()
		return err
	}

	s.isConnected = true
	s.reconnectCount = consts.DBResultEmpty
	s.logger().Infof(ctx, "Successfully connected to RabbitMQ")
	return nil
}

func (s *sMessageQ) Send(ctx context.Context, routeKey, action string, data []byte) (err error) {
	s.logger().Debugf(ctx, "Send: route key %q, action %q  ", routeKey, action)

	s.mu.RLock()
	channel := s.channel
	s.mu.RUnlock()

	if channel == nil {
		return gerror.New("RabbitMQ channel is not available")
	}

	err = channel.PublishWithContext(
		ctx,
		consts.ExchangeName,
		routeKey,
		true,
		false,
		amqp.Publishing{
			ContentType: "application/json",
			Body:        data,
			Type:        action,
		},
	)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	return
}

// RegisterHandler 註冊消息處理器
func (s *sMessageQ) RegisterHandler(routeKeyPrefix string, handler consts.OnMessage) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.messageHandlers[routeKeyPrefix] = handler
	s.logger().Infof(context.Background(), "Registered message handler for route key prefix: %s", routeKeyPrefix)
}

// InitReceive 初始化消息接收服務
func (s *sMessageQ) InitReceive(ctx context.Context) error {
	s.logger().Info(ctx, "Initializing message receive service")

	// 測試連接可用性
	if !s.IsHealthy() {
		return gerror.New("RabbitMQ connection is not healthy")
	}

	// 啟動連接維護 goroutine
	g.Go(ctx, func(ctx context.Context) {
		s.maintainConnection(ctx)
	}, func(ctx context.Context, exception error) {
		s.logger().Errorf(ctx, "Connection maintenance goroutine exception: %v", gerror.Stack(exception))
	})

	// 啟動消息消費 goroutine
	g.Go(ctx, func(ctx context.Context) {
		s.consumeMessages(ctx)
	}, func(ctx context.Context, exception error) {
		s.logger().Errorf(ctx, "Message consumption goroutine exception: %v", gerror.Stack(exception))
	})

	s.logger().Info(ctx, "Message receive service initialized successfully")
	return nil
}

// IsHealthy 檢查服務健康狀態
func (s *sMessageQ) IsHealthy() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.isConnected && s.conn != nil && !s.conn.IsClosed() && s.channel != nil
}

// Close 關閉服務
func (s *sMessageQ) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isClosed {
		return nil
	}

	s.isClosed = true
	close(s.closeChan)

	if s.channel != nil {
		s.channel.Close()
	}
	if s.conn != nil {
		s.conn.Close()
	}

	s.logger().Info(context.Background(), "MessageQ service closed")
	return nil
}

// maintainConnection 維護連接狀態
func (s *sMessageQ) maintainConnection(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.closeChan:
			return
		case <-s.reconnectChan:
			s.handleReconnect(ctx)
		}
	}
}

// handleReconnect 處理重連邏輯
func (s *sMessageQ) handleReconnect(ctx context.Context) {
	s.mu.Lock()
	s.isConnected = false
	s.mu.Unlock()

	for attempt := consts.CacheExistsTrue; attempt <= s.maxRetries; attempt++ {
		if s.isClosed {
			return
		}

		s.logger().Infof(ctx, "Attempting to reconnect to RabbitMQ (attempt %d/%d)", attempt, s.maxRetries)

		if err := s.connect(ctx); err == nil {
			s.logger().Info(ctx, "Successfully reconnected to RabbitMQ")
			return
		}

		// 計算重連間隔
		backoffDuration := time.Duration(attempt) * s.retryInterval
		if backoffDuration > s.maxReconnectInterval {
			backoffDuration = s.maxReconnectInterval
		}

		s.logger().Infof(ctx, "Reconnection failed, retrying in %v", backoffDuration)
		time.Sleep(backoffDuration)
	}

	s.logger().Error(ctx, "Failed to reconnect to RabbitMQ after maximum attempts")
}

// consumeMessages 消費消息的核心邏輯
func (s *sMessageQ) consumeMessages(ctx context.Context) {
	for {
		if s.isClosed {
			return
		}

		if !s.IsHealthy() {
			s.logger().Warning(ctx, "Connection not healthy, triggering reconnect")
			select {
			case s.reconnectChan <- struct{}{}:
			default:
			}
			time.Sleep(consts.NetworkRetryIntervalFiveSeconds)
			continue
		}

		if err := s.startConsuming(ctx); err != nil {
			s.logger().Errorf(ctx, "Error in message consumption: %v", err)
			select {
			case s.reconnectChan <- struct{}{}:
			default:
			}
			time.Sleep(consts.NetworkRetryIntervalFiveSeconds)
		}
	}
}

// startConsuming 開始消費消息
func (s *sMessageQ) startConsuming(ctx context.Context) error {
	s.mu.RLock()
	channel := s.channel
	s.mu.RUnlock()

	if channel == nil {
		return gerror.New("Channel is not available")
	}

	// 聲明隊列
	queue, err := channel.QueueDeclare(
		"",    // 空名稱，自動生成
		true,  // 持久化
		true,  // 自動刪除
		true,  // 獨占
		false, // 不等待
		nil,   // 參數
	)
	if err != nil {
		return gerror.Wrapf(err, "Failed to declare queue")
	}

	// 綁定所有註冊的路由鍵
	s.mu.RLock()
	routingKeys := s.getRoutingKeysForBinding()
	s.mu.RUnlock()

	for _, routingKey := range routingKeys {
		err = channel.QueueBind(
			queue.Name,
			routingKey,
			consts.ExchangeName,
			false,
			nil,
		)
		if err != nil {
			return gerror.Wrapf(err, "Failed to bind queue for routing key %s", routingKey)
		}
		s.logger().Debugf(ctx, "Bound queue to routing key: %s", routingKey)
	}

	// 開始消費消息
	msgs, err := channel.Consume(
		queue.Name,
		"",    // 消費者標籤
		false, // 手動確認
		false, // 非獨占
		false, // 不等待
		false, // 參數
		nil,
	)
	if err != nil {
		return gerror.Wrapf(err, "Failed to register consumer")
	}

	s.logger().Infof(ctx, "Started consuming messages from queue: %s", queue.Name)

	// 處理消息
	for msg := range msgs {
		if s.isClosed {
			break
		}

		// 使用信號量控制並發
		s.messageWorkerSem <- struct{}{}

		g.Go(ctx, func(ctx context.Context) {
			defer func() { <-s.messageWorkerSem }()
			s.dispatchMessage(ctx, msg)
		}, func(ctx context.Context, exception error) {
			s.logger().Errorf(ctx, "Message processing goroutine exception: %v", gerror.Stack(exception))
			<-s.messageWorkerSem
		})
	}

	return nil
}

// dispatchMessage 分發消息到對應的處理器
func (s *sMessageQ) dispatchMessage(ctx context.Context, msg amqp.Delivery) {
	routingKey := msg.RoutingKey
	s.logger().Debugf(ctx, "Received message with routing key: %s, type: %s", routingKey, msg.Type)

	handlerFound := false

	s.mu.RLock()
	handlers := make(map[string]consts.OnMessage)
	for prefix, handler := range s.messageHandlers {
		handlers[prefix] = handler
	}
	s.mu.RUnlock()

	// 遍歷處理器，找到匹配的路由鍵
	for registeredKey, handler := range handlers {
		if s.isRoutingKeyMatch(routingKey, registeredKey) {
			handlerFound = true
			s.processMessageWithRecovery(ctx, msg, handler, registeredKey)
		}
	}

	if !handlerFound {
		s.logger().Warningf(ctx, "No handler found for routing key: %s", routingKey)
	}

	// 確認消息處理完成
	if err := msg.Ack(false); err != nil {
		s.logger().Errorf(ctx, "Failed to acknowledge message: %v", err)
	}
}

// processMessageWithRecovery 帶恢復機制的消息處理
func (s *sMessageQ) processMessageWithRecovery(ctx context.Context, msg amqp.Delivery, handler consts.OnMessage, routingKey string) {
	defer func() {
		if r := recover(); r != nil {
			s.logger().Errorf(ctx, "Panic in message handler for routing key %s: %v", routingKey, r)
		}
	}()

	start := time.Now()
	handler(ctx, msg)
	duration := time.Since(start)

	s.logger().Debugf(ctx, "Message processed successfully for routing key %s in %v", routingKey, duration)
}

// getRoutingKeysForBinding 獲取需要綁定的路由鍵列表
func (s *sMessageQ) getRoutingKeysForBinding() []string {
	var routingKeys []string

	for routingKey := range s.messageHandlers {
		// 在這個項目中，主要處理 payloadChanged 路由鍵
		// 直接使用註冊的路由鍵進行綁定
		routingKeys = append(routingKeys, routingKey)
	}

	return routingKeys
}

// isRoutingKeyMatch 檢查路由鍵是否匹配處理器
func (s *sMessageQ) isRoutingKeyMatch(routingKey, registeredKey string) bool {
	// 在這個項目中，主要處理 payloadChanged 路由鍵
	// 使用精確匹配
	return routingKey == registeredKey
}
