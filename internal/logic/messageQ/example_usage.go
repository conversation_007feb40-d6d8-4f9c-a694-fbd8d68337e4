package messageQ

import (
	"brainHub/internal/consts"
	"brainHub/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	amqp "github.com/rabbitmq/amqp091-go"
)

// ExampleCustomHandler 示例自定義消息處理器
// 展示如何創建自定義的消息處理器
type ExampleCustomHandler struct{}

// OnMessage 實現自定義消息處理邏輯
func (h *ExampleCustomHandler) OnMessage(ctx context.Context, message any) {
	var msg *amqp.Delivery
	err := gconv.Struct(message, &msg)
	if err != nil {
		g.Log().Cat(consts.CatalogMQ).Errorf(ctx, "Failed to convert message: %v", err)
		return
	}

	if msg == nil {
		return
	}

	g.Log().Cat(consts.CatalogMQ).Infof(ctx,
		"Processing custom message: routing_key=%s, type=%s", msg.Routing<PERSON>ey, msg.Type)

	// 根據消息類型處理
	switch msg.Type {
	case "custom_action":
		h.handleCustomAction(ctx, msg)
	case "notification":
		h.handleNotification(ctx, msg)
	default:
		g.Log().Cat(consts.CatalogMQ).Warningf(ctx,
			"Unknown custom action type: %s", msg.Type)
	}
}

func (h *ExampleCustomHandler) handleCustomAction(ctx context.Context, msg *amqp.Delivery) {
	g.Log().Cat(consts.CatalogMQ).Debugf(ctx, "Handling custom action: %s", string(msg.Body))
	// 實現自定義動作邏輯
}

func (h *ExampleCustomHandler) handleNotification(ctx context.Context, msg *amqp.Delivery) {
	g.Log().Cat(consts.CatalogMQ).Debugf(ctx, "Handling notification: %s", string(msg.Body))
	// 實現通知處理邏輯
}

// RegisterExampleHandlers 註冊示例處理器
// 這個函數展示了如何註冊自定義的消息處理器
// 注意：在實際項目中，主要使用的是 payloadChanged 處理器
func RegisterExampleHandlers() {
	// 註冊自定義處理器（僅作為示例）
	customHandler := &ExampleCustomHandler{}
	service.MessageQ().RegisterHandler("custom.example", customHandler.OnMessage)

	g.Log().Cat(consts.CatalogMQ).Info(context.Background(),
		"Example message handlers registered")
}

// 使用示例：
// 在應用啟動時調用以下代碼來設置完整的 MessageQ 服務：
//
// func main() {
//     ctx := context.Background()
//
//     // 設置 MessageQ 服務（會自動註冊 payloadChanged 處理器）
//     err := messageQ.SetupMessageQ(ctx)
//     if err != nil {
//         panic(err)
//     }
//
//     // 可選：註冊其他自定義處理器
//     // messageQ.RegisterExampleHandlers()
//
//     // 應用的其他初始化邏輯...
// }
