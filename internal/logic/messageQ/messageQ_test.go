package messageQ

import (
	"brainHub/internal/consts"
	"context"
	"github.com/gogf/gf/v2/test/gtest"
	amqp "github.com/rabbitmq/amqp091-go"
	"testing"
	"time"
)

// TestMessageQStructure 測試 sMessageQ 結構體的基本功能
func TestMessageQStructure(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 由於需要 RabbitMQ 連接，這裡只測試結構體初始化
		s := &sMessageQ{
			messageHandlers:      make(map[string]consts.OnMessage),
			reconnectChan:        make(chan struct{}, 1),
			closeChan:            make(chan struct{}),
			maxRetries:           5,
			retryInterval:        time.Second * 5,
			maxReconnectInterval: time.Minute * 5,
			messageWorkerCount:   10,
			messageWorkerSem:     make(chan struct{}, 10),
		}

		t.AssertNE(s.messageHandlers, nil)
		t.AssertNE(s.reconnect<PERSON>han, nil)
		t.AssertNE(s.close<PERSON>han, nil)
		t.Assert(s.maxRetries, 5)
		t.Asser<PERSON>(s.messageWorkerCount, 10)
	})
}

// TestRegisterHandler 測試消息處理器註冊功能
func TestRegisterHandler(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		s := &sMessageQ{
			messageHandlers: make(map[string]consts.OnMessage),
		}

		// 測試處理器註冊
		testHandler := func(ctx context.Context, message any) {
			// 測試處理器
		}

		s.RegisterHandler("test.prefix", testHandler)

		t.Assert(len(s.messageHandlers), 1)
		t.AssertNE(s.messageHandlers["test.prefix"], nil)
	})
}

// TestIsRoutingKeyMatch 測試路由鍵匹配邏輯
func TestIsRoutingKeyMatch(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		s := &sMessageQ{}

		// 測試 payloadChanged 精確匹配
		t.Assert(s.isRoutingKeyMatch(consts.RouteKeyPayloadChanged, consts.RouteKeyPayloadChanged), true)
		t.Assert(s.isRoutingKeyMatch("other", consts.RouteKeyPayloadChanged), false)

		// 測試精確匹配
		t.Assert(s.isRoutingKeyMatch("exact.match", "exact.match"), true)
		t.Assert(s.isRoutingKeyMatch("exact.match", "other.match"), false)

		// 測試其他路由鍵
		t.Assert(s.isRoutingKeyMatch("custom.route", "custom.route"), true)
		t.Assert(s.isRoutingKeyMatch("custom.route", "different.route"), false)
	})
}

// TestGetRoutingKeysForBinding 測試路由鍵綁定邏輯
func TestGetRoutingKeysForBinding(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		s := &sMessageQ{
			messageHandlers: make(map[string]consts.OnMessage),
		}

		// 添加不同類型的處理器
		s.messageHandlers[consts.RouteKeyPayloadChanged] = func(ctx context.Context, message any) {}
		s.messageHandlers["custom.route"] = func(ctx context.Context, message any) {}

		routingKeys := s.getRoutingKeysForBinding()

		// 檢查是否包含預期的路由鍵
		t.Assert(len(routingKeys), 2)

		// 檢查是否包含 payloadChanged
		foundPayload := false
		foundCustom := false
		for _, key := range routingKeys {
			if key == consts.RouteKeyPayloadChanged {
				foundPayload = true
			}
			if key == "custom.route" {
				foundCustom = true
			}
		}
		t.Assert(foundPayload, true)
		t.Assert(foundCustom, true)
	})
}

// MockMessage 創建模擬的 AMQP 消息
func MockMessage(routingKey, messageType string, body []byte) amqp.Delivery {
	return amqp.Delivery{
		RoutingKey: routingKey,
		Type:       messageType,
		Body:       body,
	}
}

// TestPayloadChangedHandler 測試 payloadChanged 處理器
func TestPayloadChangedHandler(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		handler := NewPayloadChangedHandler()
		t.AssertNE(handler, nil)

		// 測試提取租戶ID
		testBody := []byte(`{"tenant_id": "test-tenant", "data": {"key": "value"}}`)
		tenantID, err := handler.extractTenantID(context.Background(), testBody)
		t.AssertNil(err)
		t.Assert(tenantID, "test-tenant")

		// 測試從 schema 字段提取
		testBody2 := []byte(`{"schema": "schema-tenant", "table": "test_table"}`)
		tenantID2, err2 := handler.extractTenantID(context.Background(), testBody2)
		t.AssertNil(err2)
		t.Assert(tenantID2, "schema-tenant")

		// 測試空消息體
		tenantID3, err3 := handler.extractTenantID(context.Background(), []byte{})
		t.AssertNil(err3)
		t.Assert(tenantID3, "")
	})
}

// TestMessageDispatch 測試消息分發邏輯
func TestMessageDispatch(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		s := &sMessageQ{
			messageHandlers: make(map[string]consts.OnMessage),
		}

		testHandler := func(ctx context.Context, message any) {
			// 測試處理器
		}

		s.RegisterHandler(consts.RouteKeyPayloadChanged, testHandler)

		// 創建模擬消息
		mockMsg := MockMessage(consts.RouteKeyPayloadChanged, "test", []byte(`{"test": "data"}`))

		// 檢查路由鍵匹配
		matched := s.isRoutingKeyMatch(mockMsg.RoutingKey, consts.RouteKeyPayloadChanged)
		t.Assert(matched, true)
	})
}
