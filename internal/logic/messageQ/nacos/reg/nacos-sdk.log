2025-07-17T10:08:32.342+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-17T10:08:32.343+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54984
2025-07-17T10:08:32.343+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ace5afd5-34d4-4ce1-a81c-b2606d042a15)
2025-07-17T10:08:32.343+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T10:08:32.343+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T10:08:32.343+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T10:08:32.343+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ace5afd5-34d4-4ce1-a81c-b2606d042a15 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T10:08:32.343+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T10:08:32.343+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T10:08:32.343+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T10:08:32.345+0800	INFO	util/common.go:96	Local IP:************
2025-07-17T10:08:32.459+0800	INFO	rpc/rpc_client.go:337	ace5afd5-34d4-4ce1-a81c-b2606d042a15 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752718112490_192.168.3.3_54651
2025-07-17T10:34:45.565+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-17T10:34:45.566+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55617
2025-07-17T10:34:45.566+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=7ce2a43d-6636-46c8-91d9-5927e698747c)
2025-07-17T10:34:45.566+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T10:34:45.566+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T10:34:45.566+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T10:34:45.566+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 7ce2a43d-6636-46c8-91d9-5927e698747c try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T10:34:45.566+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T10:34:45.566+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T10:34:45.566+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T10:34:45.567+0800	INFO	util/common.go:96	Local IP:************
2025-07-17T10:34:45.681+0800	INFO	rpc/rpc_client.go:337	7ce2a43d-6636-46c8-91d9-5927e698747c success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752719685726_192.168.3.3_58131
2025-08-15T15:58:55.946+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-15T15:58:55.947+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55533
2025-08-15T15:58:55.947+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=0cc3bd7d-3ff2-4044-9a14-63ceea934b09)
2025-08-15T15:58:55.947+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-15T15:58:55.947+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-15T15:58:55.947+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-15T15:58:55.947+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 0cc3bd7d-3ff2-4044-9a14-63ceea934b09 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-15T15:58:55.947+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-15T15:58:55.947+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-15T15:58:55.947+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-15T15:58:55.948+0800	INFO	util/common.go:96	Local IP:************
2025-08-15T15:58:56.062+0800	INFO	rpc/rpc_client.go:337	0cc3bd7d-3ff2-4044-9a14-63ceea934b09 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755244736014_192.168.3.3_49670
