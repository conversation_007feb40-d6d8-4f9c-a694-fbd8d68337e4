package messageQ

import (
	"brainHub/internal/consts"
	"brainHub/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
)

// InitMessageHandlers 初始化所有消息處理器
// 這個函數應該在應用啟動時調用，用於註冊所有必要的消息處理器
func InitMessageHandlers(ctx context.Context) error {
	g.Log().Cat(consts.CatalogMQ).Info(ctx, "Initializing message handlers")

	// 註冊 payloadChanged 消息處理器
	RegisterPayloadChangedHandler(service.MessageQ())

	// 這裡可以註冊其他消息處理器
	// 例如：
	// RegisterMariaDBHandler(service.MessageQ())
	// RegisterWeaviateHandler(service.MessageQ())

	g.Log().Cat(consts.CatalogMQ).Info(ctx, "Message handlers initialized successfully")
	return nil
}

// StartMessageConsumer 啟動消息消費服務
// 這個函數應該在所有處理器註冊完成後調用
func StartMessageConsumer(ctx context.Context) error {
	g.Log().Cat(consts.CatalogMQ).Info(ctx, "Starting message consumer")

	// 初始化消息接收服務
	err := service.MessageQ().InitReceive(ctx)
	if err != nil {
		g.Log().Cat(consts.CatalogMQ).Errorf(ctx, "Failed to initialize message receive: %v", err)
		return err
	}

	g.Log().Cat(consts.CatalogMQ).Info(ctx, "Message consumer started successfully")
	return nil
}

// SetupMessageQ 完整設置 MessageQ 服務
// 包括初始化處理器和啟動消費者
func SetupMessageQ(ctx context.Context) error {
	// 初始化消息處理器
	if err := InitMessageHandlers(ctx); err != nil {
		return err
	}

	// 啟動消息消費服務
	if err := StartMessageConsumer(ctx); err != nil {
		return err
	}

	return nil
}
