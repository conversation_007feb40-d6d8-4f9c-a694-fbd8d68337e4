package messageQ

import (
	"brainHub/internal/consts"
	"brainHub/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	amqp "github.com/rabbitmq/amqp091-go"
)

// PayloadChangedHandler 處理 payloadChanged 消息的處理器
type PayloadChangedHandler struct{}

// NewPayloadChangedHandler 創建新的 PayloadChangedHandler 實例
func NewPayloadChangedHandler() *PayloadChangedHandler {
	return &PayloadChangedHandler{}
}

// OnMessage 實現 consts.OnMessage 接口
// 當接收到 payloadChanged 路由鍵的消息時，觸發 LLM 實例重新初始化
func (h *PayloadChangedHandler) OnMessage(ctx context.Context, message any) {
	var msg *amqp.Delivery
	err := gconv.Struct(message, &msg)
	if err != nil {
		g.Log().Cat(consts.CatalogMQ).Errorf(ctx, "Failed to convert message to amqp.Delivery: %v", err)
		return
	}

	if msg == nil {
		g.Log().Cat(consts.CatalogMQ).Warning(ctx, "Received nil message")
		return
	}

	g.Log().Cat(consts.CatalogMQ).Infof(ctx, "Processing payloadChanged message: routing_key=%s, type=%s",
		msg.RoutingKey, msg.Type)

	// 解析消息體以獲取租戶信息
	tenantID, err := h.extractTenantID(ctx, msg.Body)
	if err != nil {
		g.Log().Cat(consts.CatalogMQ).Errorf(ctx, "Failed to extract tenant ID from message: %v", err)
		return
	}

	if tenantID == "" {
		g.Log().Cat(consts.CatalogMQ).Warning(ctx, "Empty tenant ID in payloadChanged message")
		return
	}

	// 觸發 LLM 實例重新初始化
	h.triggerLLMReinitialization(ctx, tenantID)
}

// extractTenantID 從消息體中提取租戶ID
func (h *PayloadChangedHandler) extractTenantID(ctx context.Context, body []byte) (string, error) {
	if len(body) == 0 {
		return "", nil
	}

	// 嘗試解析 JSON 消息體
	jsonData := g.NewVar(body)
	if jsonData.IsNil() {
		return "", nil
	}

	// 嘗試從不同的字段中提取租戶ID
	tenantID := jsonData.Map()["tenant_id"]
	if tenantID != nil {
		return gconv.String(tenantID), nil
	}

	// 嘗試從 schema 字段提取（某些消息格式中 schema 就是 tenant_id）
	schema := jsonData.Map()["schema"]
	if schema != nil {
		return gconv.String(schema), nil
	}

	// 嘗試從 data 字段中提取
	data := jsonData.Map()["data"]
	if data != nil {
		dataMap := gconv.Map(data)
		if dataMap != nil {
			if tid, exists := dataMap["tenant_id"]; exists {
				return gconv.String(tid), nil
			}
		}
	}

	g.Log().Cat(consts.CatalogMQ).Debugf(ctx, "No tenant_id found in message body: %s", string(body))
	return "", nil
}

// triggerLLMReinitialization 觸發指定租戶的 LLM 實例重新初始化
func (h *PayloadChangedHandler) triggerLLMReinitialization(ctx context.Context, tenantID string) {
	g.Log().Cat(consts.CatalogMQ).Infof(ctx, "Triggering LLM reinitialization for tenant: %s", tenantID)

	// 通過 AiRouter 服務移除租戶的緩存參數
	// 這將強制下次調用時重新初始化 LLM 實例
	func() {
		defer func() {
			if r := recover(); r != nil {
				g.Log().Cat(consts.CatalogMQ).Errorf(ctx,
					"Panic while accessing AiRouter service for tenant %s: %v", tenantID, r)
			}
		}()

		// 調用 AiRouter 的 RemoveKeyParams 方法清除租戶緩存
		service.AiRouter().RemoveKeyParams(ctx, tenantID)

		g.Log().Cat(consts.CatalogMQ).Infof(ctx,
			"Successfully triggered LLM reinitialization for tenant: %s", tenantID)
	}()
}

// RegisterPayloadChangedHandler 註冊 payloadChanged 消息處理器
// 這個函數應該在服務啟動時調用
func RegisterPayloadChangedHandler(messageQ service.IMessageQ) {
	handler := NewPayloadChangedHandler()
	messageQ.RegisterHandler(consts.RouteKeyPayloadChanged, handler.OnMessage)

	g.Log().Cat(consts.CatalogMQ).Info(context.Background(),
		"Registered payloadChanged message handler")
}
