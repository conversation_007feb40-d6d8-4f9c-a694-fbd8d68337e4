# MessageQ 服務擴展實現

## 概述

本實現擴展了現有的 MessageQ 服務，在保持原有 `Send` 方法完全兼容的基礎上，添加了完整的消息消費功能。

## 主要功能

### 1. 消息發布（原有功能）
- `Send(ctx, routeKey, action, data)` - 發送消息到 RabbitMQ

### 2. 消息消費（新增功能）
- `RegisterHandler(routeKeyPrefix, handler)` - 註冊消息處理器
- `InitReceive(ctx)` - 初始化消息接收服務
- `IsHealthy()` - 檢查服務健康狀態
- `Close()` - 關閉服務

### 3. 特殊功能
- **payloadChanged 處理器**：當接收到 `payloadChanged` 路由鍵的消息時，自動觸發 LLM 實例重新初始化

## 架構設計

### 核心結構體
```go
type sMessageQ struct {
    // RabbitMQ 連接管理
    conn    *amqp.Connection
    channel *amqp.Channel
    url     string
    
    // 消息處理器映射
    messageHandlers map[string]consts.OnMessage
    
    // 連接狀態管理
    isConnected  bool
    isConnecting bool
    isClosed     bool
    
    // 控制通道
    reconnectChan chan struct{}
    closeChan     chan struct{}
    
    // 同步控制
    mu sync.RWMutex
    
    // 重連配置
    maxRetries           int
    retryInterval        time.Duration
    maxReconnectInterval time.Duration
    
    // 消息處理並發控制
    messageWorkerCount int
    messageWorkerSem   chan struct{}
}
```

### 消息處理流程
1. **連接管理**：自動重連機制，連接斷開時自動恢復
2. **消息接收**：從 RabbitMQ 隊列接收消息
3. **消息分發**：根據路由鍵前綴分發到對應處理器
4. **並發處理**：使用信號量控制並發處理數量
5. **錯誤恢復**：完善的 panic 恢復機制

## 使用方法

### 1. 基本設置
```go
import "brainHub/internal/logic/messageQ"

func main() {
    ctx := context.Background()
    
    // 設置 MessageQ 服務（包括 payloadChanged 處理器）
    err := messageQ.SetupMessageQ(ctx)
    if err != nil {
        panic(err)
    }
}
```

### 2. 註冊自定義處理器
```go
// 創建自定義處理器
func myHandler(ctx context.Context, message any) {
    var msg *amqp.Delivery
    _ = gconv.Struct(message, &msg)
    
    if msg != nil {
        // 處理消息邏輯
        switch msg.Type {
        case "insert":
            handleInsert(ctx, msg)
        case "update":
            handleUpdate(ctx, msg)
        }
    }
}

// 註冊處理器
service.MessageQ().RegisterHandler("myservice.", myHandler)
```

### 3. 發送消息（原有功能）
```go
data := []byte(`{"key": "value"}`)
err := service.MessageQ().Send(ctx, "myservice.action", "insert", data)
```

## 路由鍵設計

### 支持的路由鍵類型

1. **主要路由鍵**：
   - `payloadChanged` - 觸發 LLM 重新初始化（項目的核心功能）

2. **自定義路由鍵**：
   - 支持註冊任何自定義路由鍵的處理器
   - 使用精確匹配機制

## payloadChanged 特殊處理

當系統接收到 `payloadChanged` 路由鍵的消息時：

1. **解析消息體**：提取租戶ID（從 `tenant_id`、`schema` 或 `data.tenant_id` 字段）
2. **觸發重新初始化**：調用 `service.AiRouter().RemoveKeyParams(ctx, tenantID)` 清除租戶緩存
3. **強制重新初始化**：下次調用 `AiModelConfig.LLM()` 時，第二個返回值（需要初始化標誌）將為 `true`

### 消息格式示例
```json
{
    "tenant_id": "example-tenant",
    "action": "config_updated",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

## 配置參數

### RabbitMQ 配置
```yaml
rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"
```

### 內部配置
- **最大重試次數**：5
- **重試間隔**：5秒
- **最大重連間隔**：5分鐘
- **並發處理數**：10

## 錯誤處理

### 連接錯誤
- 自動重連機制
- 指數退避策略
- 最大重試限制

### 消息處理錯誤
- Panic 恢復機制
- 錯誤日誌記錄
- 消息確認處理

### 健康檢查
```go
if service.MessageQ().IsHealthy() {
    // 服務正常
} else {
    // 服務異常，需要處理
}
```

## 測試

運行測試：
```bash
go test ./internal/logic/messageQ/
```

測試覆蓋：
- 結構體初始化
- 處理器註冊
- 路由鍵匹配
- 消息分發邏輯
- payloadChanged 處理器

## 注意事項

1. **向後兼容**：完全保持與現有 `Send` 方法的兼容性
2. **線程安全**：所有操作都是線程安全的
3. **資源管理**：正確的連接和通道管理
4. **優雅關閉**：支持優雅關閉服務

## 擴展指南

要添加新的消息處理器：

1. 實現 `consts.OnMessage` 接口
2. 調用 `RegisterHandler` 註冊處理器
3. 在 `getRoutingKeysForBinding` 中添加相應的路由鍵綁定邏輯

示例請參考 `example_usage.go` 文件。
