package embedding

import (
	"brainHub/internal/model/embedding"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestNew 測試創建服務實例
func TestNew(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		t.AssertNE(service, nil)

		// 驗證類型
		embeddingService, ok := service.(*sEmbedding)
		t.Assert(ok, true)
		t.AssertNE(embeddingService.providerCache, nil)
	})
}

// TestGenerateEmbeddings 測試生成向量
func TestGenerateEmbeddings(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		ctx := context.Background()

		// 測試 nil 請求
		_, err := service.GenerateEmbeddings(ctx, nil)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be nil", err.Error())

		// 測試空文本列表
		req := &embedding.EmbeddingRequest{
			Texts: []string{},
		}
		_, err = service.GenerateEmbeddings(ctx, req)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		// 測試有效請求（會失敗，因為沒有真實的 API 配置）
		req = &embedding.EmbeddingRequest{
			Texts: []string{"test text"},
		}
		_, err = service.GenerateEmbeddings(ctx, req)
		t.AssertNE(err, nil) // 預期失敗，因為沒有真實的 API 配置
	})
}

// TestGenerateSingleEmbedding 測試生成單個向量
func TestGenerateSingleEmbedding(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		ctx := context.Background()

		// 測試空文本
		_, err := service.GenerateSingleEmbedding(ctx, "tenant1", "service1", "")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		// 測試有效請求（會失敗，因為沒有真實的 API 配置）
		_, err = service.GenerateSingleEmbedding(ctx, "tenant1", "service1", "test text")
		t.AssertNE(err, nil) // 預期失敗，因為沒有真實的 API 配置
	})
}

// TestGetEmbeddingProvider 測試獲取提供商
func TestGetEmbeddingProvider(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		ctx := context.Background()

		// 測試空參數
		_, err := service.GetEmbeddingProvider(ctx, "", "")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		_, err = service.GetEmbeddingProvider(ctx, "tenant1", "")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		_, err = service.GetEmbeddingProvider(ctx, "", "service1")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		// 測試有效參數（會失敗，因為沒有 DSH 服務）
		_, err = service.GetEmbeddingProvider(ctx, "tenant1", "service1")
		t.AssertNE(err, nil) // 預期失敗，因為沒有 DSH 服務
		t.AssertIN("failed to get LLM params", err.Error())
	})
}

// TestGetSupportedProviders 測試獲取支持的提供商
func TestGetSupportedProviders(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()

		providers := service.GetSupportedProviders()
		t.AssertGT(len(providers), 0)
		t.AssertIN("aoai", providers)
	})
}

// TestValidateEmbeddingConfig 測試配置驗證
func TestValidateEmbeddingConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()

		// 測試 nil 配置
		err := service.ValidateEmbeddingConfig(nil)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be nil", err.Error())

		// 測試無效配置
		config := &embedding.EmbeddingConfig{
			Provider: "invalid",
		}
		err = service.ValidateEmbeddingConfig(config)
		t.AssertNE(err, nil)

		// 測試有效配置
		config = &embedding.EmbeddingConfig{
			Provider:   "aoai",
			BaseURL:    "https://test.openai.azure.com",
			APIKey:     "test-key",
			APIVersion: "2023-05-15",
			Model:      "text-embedding-ada-002",
		}
		err = service.ValidateEmbeddingConfig(config)
		t.AssertEQ(err, nil)
	})
}

// TestGenerateCacheKey 測試緩存鍵生成
func TestGenerateCacheKey(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New().(*sEmbedding)

		key1 := service.generateCacheKey("tenant1", "service1")
		key2 := service.generateCacheKey("tenant1", "service1")
		key3 := service.generateCacheKey("tenant2", "service1")

		// 相同參數應該生成相同的鍵
		t.Assert(key1, key2)
		// 不同參數應該生成不同的鍵
		t.AssertNE(key1, key3)
		// 鍵應該包含前綴
		t.AssertIN("embedding_provider:", key1)
	})
}

// TestReleaseEmbeddingProvider 測試釋放提供商
func TestReleaseEmbeddingProvider(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		ctx := context.Background()

		// 測試釋放不存在的提供商
		err := service.ReleaseEmbeddingProvider(ctx, "tenant1", "service1")
		t.AssertEQ(err, nil) // 應該成功，即使提供商不存在
	})
}

// TestGenerateEmbeddingsWithContext 測試帶上下文的向量生成
func TestGenerateEmbeddingsWithContext(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New()
		ctx := context.Background()

		// 測試 nil 請求
		_, err := service.GenerateEmbeddingsWithContext(ctx, "tenant1", "service1", nil)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be nil", err.Error())

		// 測試空文本列表
		req := &embedding.EmbeddingRequest{
			Texts: []string{},
		}
		_, err = service.GenerateEmbeddingsWithContext(ctx, "tenant1", "service1", req)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		// 測試空租戶ID
		req = &embedding.EmbeddingRequest{
			Texts: []string{"test text"},
		}
		_, err = service.GenerateEmbeddingsWithContext(ctx, "", "service1", req)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		// 測試空服務ID
		_, err = service.GenerateEmbeddingsWithContext(ctx, "tenant1", "", req)
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())
	})
}

// TestGetLLMParams 測試獲取 LLM 參數
func TestGetLLMParams(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		service := New().(*sEmbedding)
		ctx := context.Background()

		// 測試空參數
		_, err := service.getLLMParams(ctx, "", "")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		_, err = service.getLLMParams(ctx, "tenant1", "")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		_, err = service.getLLMParams(ctx, "", "service1")
		t.AssertNE(err, nil)
		t.AssertIN("cannot be empty", err.Error())

		// 測試有效參數（會失敗，因為沒有 DSH 服務）
		_, err = service.getLLMParams(ctx, "tenant1", "service1")
		t.AssertNE(err, nil) // 預期失敗，因為沒有 DSH 服務
	})
}

// BenchmarkGenerateEmbeddings 性能測試
func BenchmarkGenerateEmbeddings(b *testing.B) {
	service := New()
	ctx := context.Background()

	req := &embedding.EmbeddingRequest{
		Texts: []string{"benchmark test text"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.GenerateEmbeddings(ctx, req)
	}
}

// BenchmarkGetSupportedProviders 性能測試
func BenchmarkGetSupportedProviders(b *testing.B) {
	service := New()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.GetSupportedProviders()
	}
}

// BenchmarkValidateEmbeddingConfig 性能測試
func BenchmarkValidateEmbeddingConfig(b *testing.B) {
	service := New()

	config := &embedding.EmbeddingConfig{
		Provider:   "aoai",
		BaseURL:    "https://test.openai.azure.com",
		APIKey:     "test-key",
		APIVersion: "2023-05-15",
		Model:      "text-embedding-ada-002",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.ValidateEmbeddingConfig(config)
	}
}

// BenchmarkGenerateCacheKey 性能測試
func BenchmarkGenerateCacheKey(b *testing.B) {
	service := New().(*sEmbedding)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.generateCacheKey("tenant1", "service1")
	}
}
