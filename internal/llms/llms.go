package llms

import (
	"brainHub/internal/model/llm"
	"context"
)

type ILLMs interface {
	Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) (err error)
	Chat(ctx context.Context, message *llm.Message) (response *llm.ResponseData, err error)
	GenerateContent(ctx context.Context, request *llm.GenerateContentRequest) (*llm.GenerateContentResponse, error) // 新增統一接口
	Release(ctx context.Context)
}
