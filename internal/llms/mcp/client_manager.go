package mcp

import (
	"brainHub/internal/llms/common"
	"brainHub/internal/llms/mcp/clients"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// MCPClientManager MCP 客戶端管理器
type MCPClientManager struct {
	clients       map[string]*ClientInfo
	config        *MCPConfig
	configManager *ConfigManager
	mutex         sync.RWMutex
	logger        glog.ILogger
	closed        bool
}

// NewMCPClientManager 創建 MCP 客戶端管理器
func NewMCPClientManager(config *MCPConfig) *MCPClientManager {
	return &MCPClientManager{
		clients:       make(map[string]*ClientInfo),
		config:        config,
		configManager: NewConfigManager(),
		logger:        g.Log().Cat("MCPClientManager"),
		closed:        false,
	}
}

// Initialize 初始化客戶端管理器
func (cm *MCPClientManager) Initialize(ctx context.Context) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.closed {
		return NewMCPError(ErrCodeManagerClosed, "client manager is closed", ErrMCPManagerClosed)
	}

	cm.logger.Info(ctx, "Initializing MCP client manager")

	if cm.config == nil || !cm.config.Enabled {
		cm.logger.Info(ctx, "MCP is disabled, skipping client initialization")
		return nil
	}

	// 初始化所有配置的客戶端
	for _, serverConfig := range cm.config.Servers {
		if err := cm.initializeClient(ctx, serverConfig); err != nil {
			cm.logger.Errorf(ctx, "Failed to initialize client %s: %v", serverConfig.Name, err)
			// 繼續初始化其他客戶端，不因為一個失敗而停止
			continue
		}
	}

	cm.logger.Infof(ctx, "Successfully initialized %d MCP clients", len(cm.clients))
	return nil
}

// initializeClient 初始化單個客戶端
func (cm *MCPClientManager) initializeClient(ctx context.Context, config MCPServerConfig) error {
	cm.logger.Infof(ctx, "Initializing %s client: %s", config.Type, config.Name)

	// 展開環境變量
	cm.configManager.ExpandEnvironmentVariables(&config)

	// 創建客戶端
	mcpClient, err := cm.createClient(config)
	if err != nil {
		return NewConnectionFailedError(config.Name, err)
	}

	// 初始化客戶端連接
	if err := mcpClient.Connect(ctx); err != nil {
		return NewConnectionFailedError(config.Name, err)
	}

	// 存儲客戶端信息
	clientInfo := &ClientInfo{
		Name:        config.Name,
		Type:        config.Type,
		Client:      mcpClient,
		Config:      config,
		LastUsed:    time.Now(),
		IsConnected: true,
	}

	cm.clients[config.Name] = clientInfo
	cm.logger.Infof(ctx, "Successfully initialized client: %s", config.Name)

	return nil
}

// createClient 根據配置創建客戶端
func (cm *MCPClientManager) createClient(config MCPServerConfig) (MCPClient, error) {
	switch config.Type {
	case ClientTypeStdio:
		return cm.createStdioClient(config)
	case ClientTypeHTTP:
		return cm.createHTTPClient(config)
	case ClientTypeSSE:
		return cm.createSSEClient(config)
	default:
		return nil, NewMCPError(ErrCodeClientTypeMismatch,
			fmt.Sprintf("unsupported client type: %s", config.Type),
			ErrMCPClientTypeMismatch)
	}
}

// createStdioClient 創建 STDIO 客戶端
func (cm *MCPClientManager) createStdioClient(config MCPServerConfig) (MCPClient, error) {
	cm.logger.Debugf(context.Background(), "Creating STDIO client with command: %s, args: %v",
		config.Command, config.Args)

	return clients.NewStdioMCPClient(
		config.Command,
		config.Env,
		config.Args...,
	), nil
}

// createHTTPClient 創建 HTTP 客戶端
func (cm *MCPClientManager) createHTTPClient(config MCPServerConfig) (MCPClient, error) {
	cm.logger.Debugf(context.Background(), "Creating HTTP client with URL: %s", config.URL)

	return clients.NewHTTPMCPClient(config.URL, config.Headers), nil
}

// createSSEClient 創建 SSE 客戶端
func (cm *MCPClientManager) createSSEClient(config MCPServerConfig) (MCPClient, error) {
	cm.logger.Debugf(context.Background(), "Creating SSE client with URL: %s", config.URL)

	return clients.NewSSEMCPClient(config.URL, config.Headers), nil
}

// GetClient 獲取指定名稱的客戶端
func (cm *MCPClientManager) GetClient(name string) (MCPClient, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.closed {
		return nil, NewMCPError(ErrCodeManagerClosed, "client manager is closed", ErrMCPManagerClosed)
	}

	clientInfo, exists := cm.clients[name]
	if !exists {
		return nil, NewClientNotFoundError(name)
	}

	if !clientInfo.IsConnected {
		return nil, NewMCPError(ErrCodeClientNotConnected,
			fmt.Sprintf("client %s is not connected", name),
			ErrMCPClientNotConnected)
	}

	// 更新最後使用時間
	clientInfo.LastUsed = time.Now()

	return clientInfo.Client, nil
}

// GetAllClients 獲取所有客戶端
func (cm *MCPClientManager) GetAllClients() map[string]MCPClient {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.closed {
		return make(map[string]MCPClient)
	}

	clients := make(map[string]MCPClient)
	for name, clientInfo := range cm.clients {
		if clientInfo.IsConnected {
			clients[name] = clientInfo.Client
		}
	}

	return clients
}

// AddClient 添加新客戶端
func (cm *MCPClientManager) AddClient(name string, config MCPServerConfig) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.closed {
		return NewMCPError(ErrCodeManagerClosed, "client manager is closed", ErrMCPManagerClosed)
	}

	// 檢查是否已存在
	if _, exists := cm.clients[name]; exists {
		return fmt.Errorf("client %s already exists", name)
	}

	// 初始化新客戶端
	ctx := context.Background()
	if err := cm.initializeClient(ctx, config); err != nil {
		return err
	}

	cm.logger.Infof(ctx, "Successfully added new client: %s", name)
	return nil
}

// RemoveClient 移除客戶端
func (cm *MCPClientManager) RemoveClient(name string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.closed {
		return NewMCPError(ErrCodeManagerClosed, "client manager is closed", ErrMCPManagerClosed)
	}

	clientInfo, exists := cm.clients[name]
	if !exists {
		return NewClientNotFoundError(name)
	}

	// 關閉客戶端連接
	if clientInfo.Client != nil {
		if err := clientInfo.Client.Close(); err != nil {
			cm.logger.Errorf(context.Background(), "Failed to close client %s: %v", name, err)
		}
	}

	// 從映射中移除
	delete(cm.clients, name)

	cm.logger.Infof(context.Background(), "Successfully removed client: %s", name)
	return nil
}

// ListTools 列出指定客戶端的所有工具
func (cm *MCPClientManager) ListTools(ctx context.Context, clientName string) ([]common.ToolDefinition, error) {
	client, err := cm.GetClient(clientName)
	if err != nil {
		return nil, err
	}

	tools, err := client.ListTools(ctx)
	if err != nil {
		return nil, NewToolCallFailedError(fmt.Sprintf("%s.list_tools", clientName), err)
	}

	return tools, nil
}

// CallTool 調用指定客戶端的工具
func (cm *MCPClientManager) CallTool(ctx context.Context, clientName, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	client, err := cm.GetClient(clientName)
	if err != nil {
		return nil, err
	}

	result, err := client.CallTool(ctx, toolName, args)
	if err != nil {
		return nil, NewToolCallFailedError(fmt.Sprintf("%s.%s", clientName, toolName), err)
	}

	return result, nil
}

// GetClientInfo 獲取客戶端信息
func (cm *MCPClientManager) GetClientInfo(name string) (*ClientInfo, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.closed {
		return nil, NewMCPError(ErrCodeManagerClosed, "client manager is closed", ErrMCPManagerClosed)
	}

	clientInfo, exists := cm.clients[name]
	if !exists {
		return nil, NewClientNotFoundError(name)
	}

	// 返回副本以避免併發修改
	info := *clientInfo
	return &info, nil
}

// ListClientNames 列出所有客戶端名稱
func (cm *MCPClientManager) ListClientNames() []string {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	names := make([]string, 0, len(cm.clients))
	for name := range cm.clients {
		names = append(names, name)
	}

	return names
}

// GetClientsByType 根據類型獲取客戶端
func (cm *MCPClientManager) GetClientsByType(clientType string) map[string]MCPClient {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	clients := make(map[string]MCPClient)
	for name, clientInfo := range cm.clients {
		if clientInfo.Type == clientType && clientInfo.IsConnected {
			clients[name] = clientInfo.Client
		}
	}

	return clients
}

// HealthCheck 檢查所有客戶端的健康狀態
func (cm *MCPClientManager) HealthCheck(ctx context.Context) map[string]bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	health := make(map[string]bool)

	for name, clientInfo := range cm.clients {
		if !clientInfo.IsConnected {
			health[name] = false
			continue
		}

		// 嘗試列出工具來檢查連接狀態
		_, err := clientInfo.Client.ListTools(ctx)
		health[name] = err == nil

		// 更新連接狀態
		if err != nil {
			clientInfo.IsConnected = false
			cm.logger.Errorf(ctx, "Client %s health check failed: %v", name, err)
		}
	}

	return health
}

// Close 關閉所有客戶端
func (cm *MCPClientManager) Close() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.closed {
		return nil
	}

	cm.logger.Info(context.Background(), "Closing MCP client manager")

	var errors []error
	for name, clientInfo := range cm.clients {
		if clientInfo.Client != nil {
			if err := clientInfo.Client.Close(); err != nil {
				errors = append(errors, fmt.Errorf("failed to close client %s: %v", name, err))
			}
		}
	}

	cm.clients = make(map[string]*ClientInfo)
	cm.closed = true

	if len(errors) > 0 {
		return fmt.Errorf("errors occurred while closing clients: %v", errors)
	}

	cm.logger.Info(context.Background(), "Successfully closed MCP client manager")
	return nil
}

// GetStats 獲取客戶端統計信息
func (cm *MCPClientManager) GetStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_clients":     len(cm.clients),
		"connected_clients": 0,
		"clients_by_type":   make(map[string]int),
	}

	typeCounts := make(map[string]int)
	connectedCount := 0

	for _, clientInfo := range cm.clients {
		typeCounts[clientInfo.Type]++
		if clientInfo.IsConnected {
			connectedCount++
		}
	}

	stats["connected_clients"] = connectedCount
	stats["clients_by_type"] = typeCounts

	return stats
}
