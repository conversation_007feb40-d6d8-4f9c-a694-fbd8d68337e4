package mcp

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPClientManager_Initialize 測試客戶端管理器初始化
func TestMCPClientManager_Initialize(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建測試配置
		config := &MCPConfig{
			Enabled: true,
			Servers: []MCPServerConfig{
				{
					Name:        "test-stdio",
					Type:        "stdio",
					Description: "Test STDIO server",
					Command:     "echo",
					Args:        []string{"hello"},
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		// 創建客戶端管理器
		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試初始化
		err := manager.Initialize(ctx)
		// 注意：這可能會失敗，因為 echo 命令不是真正的 MCP 服務器
		if err != nil {
			t.Logf("Initialize failed as expected (echo is not MCP server): %v", err)
		}

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_GetAllClients 測試獲取所有客戶端
func TestMCPClientManager_GetAllClients(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建禁用的配置
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試獲取客戶端（應該為空）
		clients := manager.GetAllClients()
		t.AssertNE(clients, nil)
		t.AssertEQ(len(clients), 0)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_GetClient 測試獲取特定客戶端
func TestMCPClientManager_GetClient(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試獲取不存在的客戶端
		client, err := manager.GetClient("nonexistent")
		t.AssertNil(client)
		t.AssertNE(err, nil)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_CallTool 測試工具調用
func TestMCPClientManager_CallTool(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試調用不存在客戶端的工具
		_, err := manager.CallTool(ctx, "nonexistent", "tool", map[string]interface{}{})
		t.AssertNE(err, nil) // 應該返回錯誤

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_GetStats 測試獲取統計信息
func TestMCPClientManager_GetStats(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試獲取統計信息
		stats := manager.GetStats()
		t.AssertNE(stats, nil)
		t.AssertEQ(stats["total_clients"], 0)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_HealthCheck 測試健康檢查
func TestMCPClientManager_HealthCheck(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試健康檢查
		health := manager.HealthCheck(ctx)
		t.AssertNE(health, nil)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_Close 測試關閉管理器
func TestMCPClientManager_Close(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 測試關閉
		err := manager.Close()
		t.AssertNil(err)

		// 測試重複關閉
		err = manager.Close()
		t.AssertNil(err)
	})
}

// TestMCPClientManager_ConfigValidation 測試配置驗證
func TestMCPClientManager_ConfigValidation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試無效的客戶端類型
		config := &MCPConfig{
			Enabled: true,
			Servers: []MCPServerConfig{
				{
					Name:        "invalid-type",
					Type:        "invalid",
					Description: "Invalid type server",
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 初始化應該處理無效配置
		err := manager.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed as expected for invalid config: %v", err)
		}

		// 清理
		_ = manager.Close()
	})
}

// TestMCPClientManager_ConcurrentAccess 測試並發訪問
func TestMCPClientManager_ConcurrentAccess(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPClientManager(config)
		t.AssertNE(manager, nil)

		// 並發測試
		done := make(chan bool, 10)
		for i := 0; i < 10; i++ {
			go func() {
				defer func() { done <- true }()

				// 並發獲取客戶端
				clients := manager.GetAllClients()
				t.AssertNE(clients, nil)

				// 並發獲取統計信息
				stats := manager.GetStats()
				t.AssertNE(stats, nil)
			}()
		}

		// 等待所有 goroutine 完成
		for i := 0; i < 10; i++ {
			<-done
		}

		// 清理
		_ = manager.Close()
	})
}

// BenchmarkMCPClientManager_GetAllClients 性能測試
func BenchmarkMCPClientManager_GetAllClients(b *testing.B) {
	config := &MCPConfig{
		Enabled: false,
		Servers: []MCPServerConfig{},
		Global: MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:           "5m",
			LogLevel:           "info",
		},
	}

	manager := NewMCPClientManager(config)
	defer manager.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = manager.GetAllClients()
	}
}
