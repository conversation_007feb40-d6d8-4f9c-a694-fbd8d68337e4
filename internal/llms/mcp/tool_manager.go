package mcp

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"

	"brainHub/internal/llms/common"
)

// MCPToolManager MCP 工具管理器
type MCPToolManager struct {
	clientManager   *MCPClientManager
	configManager   *ConfigManager
	toolDefinitions []common.ToolDefinition
	toolsLastUpdate time.Time
	mutex           sync.RWMutex
	logger          glog.ILogger
	config          *MCPConfig
	closed          bool
}

// NewMCPToolManager 創建 MCP 工具管理器
func NewMCPToolManager(config *MCPConfig) *MCPToolManager {
	return &MCPToolManager{
		clientManager: NewMCPClientManager(config),
		configManager: NewConfigManager(),
		config:        config,
		logger:        g.Log().Cat("MCPToolManager"),
		closed:        false,
	}
}

// Initialize 初始化工具管理器
func (m *MCPToolManager) Initialize(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.closed {
		return NewMCPError(ErrCodeManagerClosed, "tool manager is closed", ErrMCPManagerClosed)
	}

	m.logger.Info(ctx, "Initializing MCP tool manager")

	if m.config == nil || !m.config.Enabled {
		m.logger.Info(ctx, "MCP is disabled, skipping tool manager initialization")
		return nil
	}

	// 初始化客戶端管理器
	if err := m.clientManager.Initialize(ctx); err != nil {
		return NewMCPError(ErrCodeManagerNotInit,
			fmt.Sprintf("failed to initialize client manager: %v", err), err)
	}

	// 初始加載工具定義
	if _, err := m.loadToolDefinitions(ctx); err != nil {
		m.logger.Errorf(ctx, "Failed to load initial tool definitions: %v", err)
		// 不阻止初始化，只記錄錯誤
	}

	m.logger.Info(ctx, "Successfully initialized MCP tool manager")
	return nil
}

// GetToolDefinitions 獲取所有工具定義
func (m *MCPToolManager) GetToolDefinitions(ctx context.Context) ([]common.ToolDefinition, error) {
	m.mutex.RLock()
	// 檢查緩存是否有效（5分鐘內）
	if time.Since(m.toolsLastUpdate) < 5*time.Minute && len(m.toolDefinitions) > 0 {
		defer m.mutex.RUnlock()
		return m.toolDefinitions, nil
	}
	m.mutex.RUnlock()

	return m.loadToolDefinitions(ctx)
}

// loadToolDefinitions 加載工具定義
func (m *MCPToolManager) loadToolDefinitions(ctx context.Context) ([]common.ToolDefinition, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.closed {
		return nil, NewMCPError(ErrCodeManagerClosed, "tool manager is closed", ErrMCPManagerClosed)
	}

	// 雙重檢查
	if time.Since(m.toolsLastUpdate) < 5*time.Minute && len(m.toolDefinitions) > 0 {
		return m.toolDefinitions, nil
	}

	m.logger.Debug(ctx, "Loading tool definitions from all MCP clients")

	var allTools []common.ToolDefinition
	clients := m.clientManager.GetAllClients()

	// 如果沒有客戶端，直接返回空列表
	if len(clients) == 0 {
		m.logger.Info(ctx, "No MCP clients available, returning empty tool definitions")
		m.toolDefinitions = allTools
		m.toolsLastUpdate = time.Now()
		return allTools, nil
	}

	m.logger.Debugf(ctx, "Found %d MCP clients, loading tools...", len(clients))

	for clientName, client := range clients {
		m.logger.Debugf(ctx, "Loading tools from client: %s", clientName)

		// 為每個客戶端調用添加超時控制
		clientCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
		tools, err := client.ListTools(clientCtx)
		cancel()

		if err != nil {
			m.logger.Errorf(ctx, "Failed to list tools from client %s: %v", clientName, err)
			continue
		}

		m.logger.Debugf(ctx, "Client %s returned %d tools", clientName, len(tools))

		for _, tool := range tools {
			toolDef := common.ToolDefinition{
				Name:        fmt.Sprintf("%s.%s", clientName, tool.Name),
				Description: tool.Description,
				Parameters:  tool.Parameters,
				ClientName:  clientName,
			}
			allTools = append(allTools, toolDef)
		}

		m.logger.Debugf(ctx, "Successfully loaded %d tools from client %s", len(tools), clientName)
	}

	m.toolDefinitions = allTools
	m.toolsLastUpdate = time.Now()

	m.logger.Infof(ctx, "Successfully loaded %d tool definitions from %d clients",
		len(allTools), len(clients))

	return allTools, nil
}

// CallTool 調用指定工具
func (m *MCPToolManager) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	if m.closed {
		return nil, NewMCPError(ErrCodeManagerClosed, "tool manager is closed", ErrMCPManagerClosed)
	}

	m.logger.Infof(ctx, "Calling tool: %s with args: %v", toolName, args)

	// 解析工具名稱（格式：clientName.toolName）
	clientName, actualToolName, err := m.parseToolName(toolName)
	if err != nil {
		return &common.ToolResult{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 驗證工具參數
	if err := m.validateToolArguments(ctx, toolName, args); err != nil {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("invalid arguments: %v", err),
		}, nil
	}

	// 調用客戶端管理器
	startTime := time.Now()
	result, err := m.clientManager.CallTool(ctx, clientName, actualToolName, args)
	duration := time.Since(startTime)

	if err != nil {
		m.logger.Errorf(ctx, "Tool call failed for %s after %v: %v", toolName, duration, err)
		return &common.ToolResult{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 轉換結果格式
	toolResult := m.convertMCPResult(result)

	m.logger.Infof(ctx, "Tool call succeeded for %s in %v", toolName, duration)
	return toolResult, nil
}

// parseToolName 解析工具名稱
func (m *MCPToolManager) parseToolName(fullToolName string) (clientName, toolName string, err error) {
	parts := strings.SplitN(fullToolName, ".", 2)
	if len(parts) != 2 {
		return "", "", NewToolNotFoundError(fullToolName).
			WithContext("reason", "invalid tool name format, expected: clientName.toolName")
	}

	return parts[0], parts[1], nil
}

// validateToolArguments 驗證工具參數
func (m *MCPToolManager) validateToolArguments(ctx context.Context, toolName string, args map[string]interface{}) error {
	// 獲取工具定義
	toolDefs, err := m.GetToolDefinitions(ctx)
	if err != nil {
		return fmt.Errorf("failed to get tool definitions: %v", err)
	}

	// 查找對應的工具定義
	var toolDef *common.ToolDefinition
	for _, def := range toolDefs {
		if def.Name == toolName {
			toolDef = &def
			break
		}
	}

	if toolDef == nil {
		return NewToolNotFoundError(toolName)
	}

	// 使用通用工具函數驗證參數
	helper := common.NewToolCallHelper()
	return helper.ValidateToolArguments(ctx, *toolDef, args)
}

// convertMCPResult 轉換 MCP 結果為通用格式（已簡化，直接返回結果）
func (m *MCPToolManager) convertMCPResult(result *common.ToolResult) *common.ToolResult {
	if result == nil {
		return &common.ToolResult{
			Success: false,
			Error:   "nil result",
		}
	}
	return result
}

// ListAvailableTools 列出所有可用工具
func (m *MCPToolManager) ListAvailableTools(ctx context.Context) ([]common.ToolInfo, error) {
	if m.closed {
		return nil, NewMCPError(ErrCodeManagerClosed, "tool manager is closed", ErrMCPManagerClosed)
	}

	toolDefs, err := m.GetToolDefinitions(ctx)
	if err != nil {
		return nil, err
	}

	var toolInfos []common.ToolInfo
	for _, toolDef := range toolDefs {
		toolInfo := common.ToolInfo{
			Name:        toolDef.Name,
			Description: toolDef.Description,
			ClientName:  toolDef.ClientName,
			Parameters:  make(map[string]common.ParameterInfo),
		}

		// 轉換參數信息
		if toolDef.Parameters != nil {
			if properties, ok := toolDef.Parameters["properties"].(map[string]interface{}); ok {
				for paramName, paramInfo := range properties {
					if paramMap, ok := paramInfo.(map[string]interface{}); ok {
						toolInfo.Parameters[paramName] = common.ParameterInfo{
							Type:        fmt.Sprintf("%v", paramMap["type"]),
							Description: fmt.Sprintf("%v", paramMap["description"]),
							Required:    m.isRequiredParameter(toolDef.Parameters, paramName),
						}
					}
				}
			}
		}

		toolInfos = append(toolInfos, toolInfo)
	}

	return toolInfos, nil
}

// isRequiredParameter 檢查參數是否為必需
func (m *MCPToolManager) isRequiredParameter(schema map[string]interface{}, paramName string) bool {
	if required, ok := schema["required"].([]interface{}); ok {
		for _, req := range required {
			if reqStr, ok := req.(string); ok && reqStr == paramName {
				return true
			}
		}
	}
	return false
}

// GetToolsByClient 根據客戶端獲取工具
func (m *MCPToolManager) GetToolsByClient(ctx context.Context, clientName string) ([]common.ToolDefinition, error) {
	allTools, err := m.GetToolDefinitions(ctx)
	if err != nil {
		return nil, err
	}

	var clientTools []common.ToolDefinition
	for _, tool := range allTools {
		if tool.ClientName == clientName {
			clientTools = append(clientTools, tool)
		}
	}

	return clientTools, nil
}

// RefreshToolDefinitions 刷新工具定義
func (m *MCPToolManager) RefreshToolDefinitions(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.closed {
		return NewMCPError(ErrCodeManagerClosed, "tool manager is closed", ErrMCPManagerClosed)
	}

	m.logger.Info(ctx, "Refreshing tool definitions")

	// 重置緩存時間以強制重新加載
	m.toolsLastUpdate = time.Time{}

	_, err := m.loadToolDefinitions(ctx)
	return err
}

// GetClientManager 獲取客戶端管理器
func (m *MCPToolManager) GetClientManager() *MCPClientManager {
	return m.clientManager
}

// GetStats 獲取統計信息
func (m *MCPToolManager) GetStats(ctx context.Context) map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_tools":       len(m.toolDefinitions),
		"tools_last_update": m.toolsLastUpdate,
		"cache_age_seconds": time.Since(m.toolsLastUpdate).Seconds(),
		"tools_by_client":   make(map[string]int),
	}

	// 按客戶端統計工具數量
	clientCounts := make(map[string]int)
	for _, tool := range m.toolDefinitions {
		clientCounts[tool.ClientName]++
	}
	stats["tools_by_client"] = clientCounts

	// 添加客戶端管理器統計
	clientStats := m.clientManager.GetStats()
	stats["client_manager"] = clientStats

	return stats
}

// HealthCheck 健康檢查
func (m *MCPToolManager) HealthCheck(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"tool_manager_status": "healthy",
		"clients_health":      make(map[string]bool),
		"total_tools":         len(m.toolDefinitions),
		"last_update":         m.toolsLastUpdate,
	}

	if m.closed {
		health["tool_manager_status"] = "closed"
		return health
	}

	// 檢查客戶端健康狀態
	clientsHealth := m.clientManager.HealthCheck(ctx)
	health["clients_health"] = clientsHealth

	// 統計健康的客戶端數量
	healthyCount := 0
	for _, isHealthy := range clientsHealth {
		if isHealthy {
			healthyCount++
		}
	}

	health["healthy_clients"] = healthyCount
	health["total_clients"] = len(clientsHealth)

	return health
}

// Close 關閉工具管理器
func (m *MCPToolManager) Close() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.closed {
		return nil
	}

	m.logger.Info(context.Background(), "Closing MCP tool manager")

	// 關閉客戶端管理器
	var err error
	if m.clientManager != nil {
		err = m.clientManager.Close()
	}

	// 清理資源
	m.toolDefinitions = nil
	m.closed = true

	if err != nil {
		m.logger.Errorf(context.Background(), "Error closing tool manager: %v", err)
		return fmt.Errorf("failed to close tool manager: %v", err)
	}

	m.logger.Info(context.Background(), "Successfully closed MCP tool manager")
	return nil
}
