package mcp

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPToolManager_Initialize 測試工具管理器初始化
func TestMCPToolManager_Initialize(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建測試配置
		config := &MCPConfig{
			Enabled: true,
			Servers: []MCPServerConfig{
				{
					Name:        "test-server",
					Type:        "stdio",
					Description: "Test server",
					Command:     "echo",
					Args:        []string{"hello"},
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		// 創建工具管理器
		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		// 測試初始化
		err := manager.Initialize(ctx)
		// 注意：這可能會失敗，因為 echo 命令不是真正的 MCP 服務器
		// 但我們主要測試初始化邏輯
		if err != nil {
			t.Logf("Initialize failed as expected (echo is not MCP server): %v", err)
		}

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_GetToolDefinitions 測試獲取工具定義
func TestMCPToolManager_GetToolDefinitions(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建禁用的配置（避免實際連接）
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試獲取工具定義（應該返回空列表）
		tools, err := manager.GetToolDefinitions(ctx)
		t.AssertNil(err)
		t.AssertEQ(len(tools), 0)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_CallTool 測試工具調用
func TestMCPToolManager_CallTool(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建禁用的配置
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試調用不存在的工具
		result, err := manager.CallTool(ctx, "nonexistent.tool", map[string]interface{}{})
		t.AssertNil(err) // 不應該返回錯誤，而是在結果中標記失敗
		t.AssertNE(result, nil)
		t.AssertEQ(result.Success, false)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_ListAvailableTools 測試列出可用工具
func TestMCPToolManager_ListAvailableTools(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試列出工具
		toolInfos, err := manager.ListAvailableTools(ctx)
		t.AssertNil(err)
		t.AssertEQ(len(toolInfos), 0)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_GetStats 測試獲取統計信息
func TestMCPToolManager_GetStats(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試獲取統計信息
		stats := manager.GetStats(ctx)
		t.AssertNE(stats, nil)
		t.AssertEQ(stats["total_tools"], 0)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_HealthCheck 測試健康檢查
func TestMCPToolManager_HealthCheck(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試健康檢查
		health := manager.HealthCheck(ctx)
		t.AssertNE(health, nil)
		t.AssertEQ(health["tool_manager_status"], "healthy")

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_RefreshToolDefinitions 測試刷新工具定義
func TestMCPToolManager_RefreshToolDefinitions(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試刷新工具定義
		err = manager.RefreshToolDefinitions(ctx)
		t.AssertNil(err)

		// 清理
		_ = manager.Close()
	})
}

// TestMCPToolManager_Close 測試關閉管理器
func TestMCPToolManager_Close(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global: MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:           "5m",
				LogLevel:           "info",
			},
		}

		manager := NewMCPToolManager(config)
		t.AssertNE(manager, nil)

		// 測試關閉
		err := manager.Close()
		t.AssertNil(err)

		// 測試重複關閉
		err = manager.Close()
		t.AssertNil(err)
	})
}

// BenchmarkMCPToolManager_GetToolDefinitions 性能測試
func BenchmarkMCPToolManager_GetToolDefinitions(b *testing.B) {
	ctx := context.Background()

	config := &MCPConfig{
		Enabled: false,
		Servers: []MCPServerConfig{},
		Global: MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:           "5m",
			LogLevel:           "info",
		},
	}

	manager := NewMCPToolManager(config)
	_ = manager.Initialize(ctx)
	defer manager.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = manager.GetToolDefinitions(ctx)
	}
}
