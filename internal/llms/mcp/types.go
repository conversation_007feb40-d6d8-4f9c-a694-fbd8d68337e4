package mcp

import (
	"context"
	"time"

	"brainHub/internal/llms/common"
)

// MCPClient MCP 客戶端接口
type MCPClient interface {
	// Connect 連接到 MCP 服務器
	Connect(ctx context.Context) error

	// ListTools 列出可用工具
	ListTools(ctx context.Context) ([]common.ToolDefinition, error)

	// CallTool 調用工具
	CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error)

	// Close 關閉客戶端
	Close() error
}

// MCPConfig MCP 總配置結構
type MCPConfig struct {
	Enabled bool              `json:"enabled"`
	Servers []MCPServerConfig `json:"servers"`
	Global  MCPGlobalConfig   `json:"global"`
}

// MCPServerConfig MCP 服務器配置
type MCPServerConfig struct {
	Name        string            `json:"name"`
	Type        string            `json:"type"` // stdio, http, sse
	Description string            `json:"description"`
	Command     string            `json:"command,omitempty"` // for stdio
	Args        []string          `json:"args,omitempty"`    // for stdio
	URL         string            `json:"url,omitempty"`     // for http/sse
	Headers     map[string]string `json:"headers,omitempty"` // for http/sse
	Env         []string          `json:"env,omitempty"`     // for stdio
	Timeout     string            `json:"timeout"`
	RetryCount  int               `json:"retry_count"`
}

// MCPGlobalConfig MCP 全局配置
type MCPGlobalConfig struct {
	Enabled            bool   `json:"enabled"`
	DefaultTimeout     string `json:"default_timeout"`
	MaxConcurrentCalls int    `json:"max_concurrent_calls"`
	CacheTTL           string `json:"cache_ttl"`
	LogLevel           string `json:"log_level"`
}

// ToolDefinition 工具定義結構
type ToolDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`  // JSON Schema 格式
	ClientName  string                 `json:"client_name"` // 所屬的 MCP 客戶端名稱
}

// ToolCallResult 工具調用結果
type ToolCallResult struct {
	HasToolCalls bool                   `json:"has_tool_calls"`
	ToolCalls    []RequestedToolCall    `json:"tool_calls"`
	Response     string                 `json:"response"`
	ToolResults  map[string]*ToolResult `json:"tool_results"`
}

// RequestedToolCall LLM 請求的工具調用
type RequestedToolCall struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

// ToolResult 工具執行結果
type ToolResult struct {
	Success bool   `json:"success"`
	Content string `json:"content"`
	Error   string `json:"error,omitempty"`
}

// Message 消息結構
type Message struct {
	Role       string `json:"role"` // user, assistant, tool
	Content    string `json:"content"`
	ToolCallID string `json:"tool_call_id,omitempty"`
}

// ClientInfo 客戶端信息
type ClientInfo struct {
	Name        string          `json:"name"`
	Type        string          `json:"type"`
	Client      MCPClient       `json:"-"`
	Config      MCPServerConfig `json:"config"`
	LastUsed    time.Time       `json:"last_used"`
	IsConnected bool            `json:"is_connected"`
}

// ToolInfo 工具信息結構
type ToolInfo struct {
	Name        string                   `json:"name"`
	Description string                   `json:"description"`
	Parameters  map[string]ParameterInfo `json:"parameters"`
	ClientName  string                   `json:"client_name"`
}

// ParameterInfo 參數信息
type ParameterInfo struct {
	Type        string `json:"type"`
	Description string `json:"description"`
	Required    bool   `json:"required"`
}

// IMCPToolManager MCP 工具管理器接口
type IMCPToolManager interface {
	// Initialize 初始化工具管理器
	Initialize(ctx context.Context) error

	// GetToolDefinitions 獲取所有工具定義
	GetToolDefinitions(ctx context.Context) ([]ToolDefinition, error)

	// CallTool 調用指定工具
	CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*ToolResult, error)

	// ListAvailableTools 列出所有可用工具
	ListAvailableTools(ctx context.Context) ([]ToolInfo, error)

	// Close 關閉工具管理器
	Close() error
}

// IMCPClientManager MCP 客戶端管理器接口
type IMCPClientManager interface {
	// Initialize 初始化客戶端管理器
	Initialize(ctx context.Context) error

	// GetClient 獲取指定名稱的客戶端
	GetClient(name string) (MCPClient, error)

	// GetAllClients 獲取所有客戶端
	GetAllClients() map[string]MCPClient

	// AddClient 添加新客戶端
	AddClient(name string, config MCPServerConfig) error

	// RemoveClient 移除客戶端
	RemoveClient(name string) error

	// Close 關閉所有客戶端
	Close() error
}

// IToolCalling 工具調用接口 - 所有 LLM 都實現此接口
type IToolCalling interface {
	// GetToolDefinitions 獲取可用工具定義
	GetToolDefinitions(ctx context.Context) ([]ToolDefinition, error)

	// CallToolsIfNeeded 根據 LLM 判斷調用工具
	CallToolsIfNeeded(ctx context.Context, userMessage string, conversationHistory []Message) (*ToolCallResult, error)

	// ExecuteToolCall 執行具體的工具調用
	ExecuteToolCall(ctx context.Context, toolName string, args map[string]interface{}) (*ToolResult, error)
}

// ClientType 客戶端類型常量
const (
	ClientTypeStdio = "stdio"
	ClientTypeHTTP  = "http"
	ClientTypeSSE   = "sse"
)

// 默認配置值
const (
	DefaultTimeout            = "30s"
	DefaultMaxConcurrentCalls = 10
	DefaultCacheTTL           = "5m"
	DefaultRetryCount         = 3
)
