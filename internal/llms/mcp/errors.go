package mcp

import (
	"errors"
	"fmt"
)

// MCP 相關錯誤定義
var (
	// 客戶端錯誤
	ErrMCPClientNotFound     = errors.New("MCP client not found")
	ErrMCPClientNotConnected = errors.New("MCP client not connected")
	ErrMCPClientInitFailed   = errors.New("MCP client initialization failed")
	ErrMCPClientTypeMismatch = errors.New("MCP client type mismatch")
	
	// 工具調用錯誤
	ErrMCPToolNotFound       = errors.New("MCP tool not found")
	ErrMCPToolNotAvailable   = errors.New("MCP tool not available")
	ErrMCPToolCallFailed     = errors.New("MCP tool call failed")
	ErrMCPToolTimeout        = errors.New("MCP tool call timeout")
	ErrMCPInvalidArguments   = errors.New("MCP tool invalid arguments")
	
	// 連接錯誤
	ErrMCPConnectionFailed   = errors.New("MCP connection failed")
	ErrMCPConnectionTimeout  = errors.New("MCP connection timeout")
	ErrMCPConnectionLost     = errors.New("MCP connection lost")
	
	// 配置錯誤
	ErrMCPConfigInvalid      = errors.New("MCP config invalid")
	ErrMCPConfigNotFound     = errors.New("MCP config not found")
	ErrMCPConfigParseFailed  = errors.New("MCP config parse failed")
	
	// 管理器錯誤
	ErrMCPManagerNotInit     = errors.New("MCP manager not initialized")
	ErrMCPManagerClosed      = errors.New("MCP manager closed")
	
	// 通用錯誤
	ErrMCPOperationFailed    = errors.New("MCP operation failed")
	ErrMCPInternalError      = errors.New("MCP internal error")
)

// MCPError MCP 自定義錯誤類型
type MCPError struct {
	Code    string
	Message string
	Cause   error
	Context map[string]interface{}
}

func (e *MCPError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("MCP Error [%s]: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("MCP Error [%s]: %s", e.Code, e.Message)
}

func (e *MCPError) Unwrap() error {
	return e.Cause
}

// NewMCPError 創建新的 MCP 錯誤
func NewMCPError(code, message string, cause error) *MCPError {
	return &MCPError{
		Code:    code,
		Message: message,
		Cause:   cause,
		Context: make(map[string]interface{}),
	}
}

// WithContext 添加上下文信息
func (e *MCPError) WithContext(key string, value interface{}) *MCPError {
	e.Context[key] = value
	return e
}

// 錯誤代碼常量
const (
	ErrCodeClientNotFound     = "CLIENT_NOT_FOUND"
	ErrCodeClientNotConnected = "CLIENT_NOT_CONNECTED"
	ErrCodeClientInitFailed   = "CLIENT_INIT_FAILED"
	ErrCodeClientTypeMismatch = "CLIENT_TYPE_MISMATCH"
	
	ErrCodeToolNotFound       = "TOOL_NOT_FOUND"
	ErrCodeToolNotAvailable   = "TOOL_NOT_AVAILABLE"
	ErrCodeToolCallFailed     = "TOOL_CALL_FAILED"
	ErrCodeToolTimeout        = "TOOL_TIMEOUT"
	ErrCodeInvalidArguments   = "INVALID_ARGUMENTS"
	
	ErrCodeConnectionFailed   = "CONNECTION_FAILED"
	ErrCodeConnectionTimeout  = "CONNECTION_TIMEOUT"
	ErrCodeConnectionLost     = "CONNECTION_LOST"
	
	ErrCodeConfigInvalid      = "CONFIG_INVALID"
	ErrCodeConfigNotFound     = "CONFIG_NOT_FOUND"
	ErrCodeConfigParseFailed  = "CONFIG_PARSE_FAILED"
	
	ErrCodeManagerNotInit     = "MANAGER_NOT_INIT"
	ErrCodeManagerClosed      = "MANAGER_CLOSED"
	
	ErrCodeOperationFailed    = "OPERATION_FAILED"
	ErrCodeInternalError      = "INTERNAL_ERROR"
)

// 便利函數：創建特定類型的錯誤

// NewClientNotFoundError 創建客戶端未找到錯誤
func NewClientNotFoundError(clientName string) *MCPError {
	return NewMCPError(ErrCodeClientNotFound, 
		fmt.Sprintf("Client '%s' not found", clientName), 
		ErrMCPClientNotFound).
		WithContext("client_name", clientName)
}

// NewToolNotFoundError 創建工具未找到錯誤
func NewToolNotFoundError(toolName string) *MCPError {
	return NewMCPError(ErrCodeToolNotFound, 
		fmt.Sprintf("Tool '%s' not found", toolName), 
		ErrMCPToolNotFound).
		WithContext("tool_name", toolName)
}

// NewToolCallFailedError 創建工具調用失敗錯誤
func NewToolCallFailedError(toolName string, cause error) *MCPError {
	return NewMCPError(ErrCodeToolCallFailed, 
		fmt.Sprintf("Tool call failed for '%s'", toolName), 
		cause).
		WithContext("tool_name", toolName)
}

// NewConnectionFailedError 創建連接失敗錯誤
func NewConnectionFailedError(clientName string, cause error) *MCPError {
	return NewMCPError(ErrCodeConnectionFailed, 
		fmt.Sprintf("Connection failed for client '%s'", clientName), 
		cause).
		WithContext("client_name", clientName)
}

// NewConfigInvalidError 創建配置無效錯誤
func NewConfigInvalidError(reason string) *MCPError {
	return NewMCPError(ErrCodeConfigInvalid, 
		fmt.Sprintf("Config invalid: %s", reason), 
		ErrMCPConfigInvalid).
		WithContext("reason", reason)
}

// IsRetryableError 判斷錯誤是否可重試
func IsRetryableError(err error) bool {
	var mcpErr *MCPError
	if errors.As(err, &mcpErr) {
		switch mcpErr.Code {
		case ErrCodeConnectionTimeout, ErrCodeConnectionLost, ErrCodeToolTimeout:
			return true
		default:
			return false
		}
	}
	return false
}

// IsFatalError 判斷錯誤是否為致命錯誤（不可恢復）
func IsFatalError(err error) bool {
	var mcpErr *MCPError
	if errors.As(err, &mcpErr) {
		switch mcpErr.Code {
		case ErrCodeClientTypeMismatch, ErrCodeConfigInvalid, ErrCodeConfigParseFailed:
			return true
		default:
			return false
		}
	}
	return false
}
