package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"brainHub/internal/llms/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// HTTPMCPClient HTTP MCP 客戶端實現
type HTTPMCPClient struct {
	url        string
	headers    map[string]string
	httpClient *http.Client
	logger     glog.ILogger
	mutex      sync.RWMutex
	connected  bool
	timeout    time.Duration
	requestID  int
}

// NewHTTPMCPClient 創建新的 HTTP MCP 客戶端
func NewHTTPMCPClient(url string, headers map[string]string) *HTTPMCPClient {
	return &HTTPMCPClient{
		url:     url,
		headers: headers,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		timeout: 30 * time.Second,
		logger:  g.Log(),
	}
}

// Connect 連接到 MCP 服務器
func (c *HTTPMCPClient) Connect(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.connected {
		return nil
	}

	c.logger.Infof(ctx, "Connecting to HTTP MCP server: %s", c.url)

	// 發送初始化請求測試連接
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      c.getNextRequestID(),
		Method:  "initialize",
		Params: map[string]interface{}{
			"protocolVersion": "2024-11-05",
			"capabilities":    map[string]interface{}{},
		},
	}

	_, err := c.sendRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to initialize HTTP MCP client: %v", err)
	}

	c.connected = true
	c.logger.Infof(ctx, "Successfully connected to HTTP MCP server")
	return nil
}

// ListTools 列出可用工具
func (c *HTTPMCPClient) ListTools(ctx context.Context) ([]common.ToolDefinition, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return nil, fmt.Errorf("client not connected")
	}

	// 發送 list_tools 請求
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      c.getNextRequestID(),
		Method:  "tools/list",
	}

	response, err := c.sendRequest(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools: %v", err)
	}

	// 解析響應
	var tools []common.ToolDefinition
	if response.Result != nil {
		if resultMap, ok := response.Result.(map[string]interface{}); ok {
			if toolsArray, ok := resultMap["tools"].([]interface{}); ok {
				for _, toolData := range toolsArray {
					if toolMap, ok := toolData.(map[string]interface{}); ok {
						tool := common.ToolDefinition{
							Name:        getString(toolMap, "name"),
							Description: getString(toolMap, "description"),
							Parameters:  getMap(toolMap, "input_schema"),
						}
						tools = append(tools, tool)
					}
				}
			}
		}
	}

	return tools, nil
}

// CallTool 調用工具
func (c *HTTPMCPClient) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return nil, fmt.Errorf("client not connected")
	}

	// 發送 call_tool 請求
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      c.getNextRequestID(),
		Method:  "tools/call",
		Params: map[string]interface{}{
			"name":      toolName,
			"arguments": args,
		},
	}

	response, err := c.sendRequest(ctx, request)
	if err != nil {
		return &common.ToolResult{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 解析響應
	result := &common.ToolResult{
		Success: response.Error == nil,
	}

	if response.Error != nil {
		result.Error = response.Error.Message
	} else if response.Result != nil {
		if resultMap, ok := response.Result.(map[string]interface{}); ok {
			if content, ok := resultMap["content"].(string); ok {
				result.Content = content
			} else {
				// 如果沒有 content 字段，將整個結果序列化為字符串
				if data, err := json.Marshal(response.Result); err == nil {
					result.Content = string(data)
				}
			}
		}
	}

	return result, nil
}

// Close 關閉客戶端
func (c *HTTPMCPClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.connected {
		return nil
	}

	c.connected = false
	c.logger.Info(context.Background(), "HTTP MCP client closed")
	return nil
}

// sendRequest 發送 HTTP 請求
func (c *HTTPMCPClient) sendRequest(ctx context.Context, request MCPRequest) (*MCPResponse, error) {
	// 序列化請求
	data, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// 創建 HTTP 請求
	req, err := http.NewRequestWithContext(ctx, "POST", c.url, bytes.NewBuffer(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 設置請求頭
	req.Header.Set("Content-Type", "application/json")
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	// 發送請求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	// 檢查狀態碼
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	// 解析響應
	var response MCPResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return &response, nil
}

// getNextRequestID 獲取下一個請求 ID
func (c *HTTPMCPClient) getNextRequestID() int {
	c.requestID++
	return c.requestID
}
