package clients

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"brainHub/internal/llms/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// SSEMCPClient SSE MCP 客戶端實現
type SSEMCPClient struct {
	url        string
	headers    map[string]string
	httpClient *http.Client
	logger     glog.ILogger
	mutex      sync.RWMutex
	connected  bool
	timeout    time.Duration
	eventChan  chan interface{}
	stopChan   chan struct{}
	requestID  int
}

// NewSSEMCPClient 創建新的 SSE MCP 客戶端
func NewSSEMCPClient(url string, headers map[string]string) *SSEMCPClient {
	return &SSEMCPClient{
		url:     url,
		headers: headers,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		timeout:   30 * time.Second,
		logger:    g.Log(),
		eventChan: make(chan interface{}, 100),
		stopChan:  make(chan struct{}),
	}
}

// Connect 連接到 SSE MCP 服務器
func (c *SSEMCPClient) Connect(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.connected {
		return nil
	}

	c.logger.Infof(ctx, "Connecting to SSE MCP server: %s", c.url)

	// 創建 SSE 連接
	req, err := http.NewRequestWithContext(ctx, "GET", c.url, nil)
	if err != nil {
		return fmt.Errorf("failed to create SSE request: %v", err)
	}

	// 設置 SSE 請求頭
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Set("Cache-Control", "no-cache")
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	// 發送請求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to connect to SSE server: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		return fmt.Errorf("SSE connection failed with status: %d", resp.StatusCode)
	}

	// 啟動事件監聽
	go c.listenEvents(resp)

	c.connected = true
	c.logger.Infof(ctx, "Successfully connected to SSE MCP server")
	return nil
}

// ListTools 列出可用工具
func (c *SSEMCPClient) ListTools(ctx context.Context) ([]common.ToolDefinition, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return nil, fmt.Errorf("client not connected")
	}

	// 對於 SSE 客戶端，我們可以返回一個模擬的工具列表
	tools := []common.ToolDefinition{
		{
			Name:        "sse_notification",
			Description: "接收 SSE 事件通知",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"event_type": map[string]interface{}{
						"type":        "string",
						"description": "事件類型",
					},
				},
			},
		},
	}

	return tools, nil
}

// CallTool 調用工具
func (c *SSEMCPClient) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return nil, fmt.Errorf("client not connected")
	}

	// 對於 SSE 客戶端，工具調用可能是監聽特定事件
	if toolName == "sse_notification" {
		// 等待事件
		select {
		case event := <-c.eventChan:
			if data, err := json.Marshal(event); err == nil {
				return &common.ToolResult{
					Success: true,
					Content: string(data),
				}, nil
			} else {
				return &common.ToolResult{
					Success: false,
					Error:   fmt.Sprintf("failed to marshal event: %v", err),
				}, nil
			}
		case <-ctx.Done():
			return &common.ToolResult{
				Success: false,
				Error:   "context cancelled",
			}, nil
		case <-time.After(c.timeout):
			return &common.ToolResult{
				Success: false,
				Error:   "timeout waiting for event",
			}, nil
		}
	}

	return &common.ToolResult{
		Success: false,
		Error:   fmt.Sprintf("unsupported tool: %s", toolName),
	}, nil
}

// Close 關閉客戶端
func (c *SSEMCPClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.connected {
		return nil
	}

	c.connected = false

	// 停止事件監聽
	close(c.stopChan)

	c.logger.Info(context.Background(), "SSE MCP client closed")
	return nil
}

// listenEvents 監聽 SSE 事件
func (c *SSEMCPClient) listenEvents(resp *http.Response) {
	defer resp.Body.Close()

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		select {
		case <-c.stopChan:
			return
		default:
		}

		line := scanner.Text()
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			// 嘗試解析 JSON 事件
			var event interface{}
			if err := json.Unmarshal([]byte(data), &event); err == nil {
				select {
				case c.eventChan <- event:
				default:
					// 如果通道滿了，丟棄舊事件
					select {
					case <-c.eventChan:
						c.eventChan <- event
					default:
					}
				}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		c.logger.Errorf(context.Background(), "SSE scanner error: %v", err)
	}
}
