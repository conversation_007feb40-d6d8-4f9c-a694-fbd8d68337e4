package mcp

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// ConfigManager MCP 配置管理器
type ConfigManager struct {
	logger      glog.ILogger
	configCache *MCPConfig
	cacheTime   time.Time
	cacheTTL    time.Duration
	cacheMutex  sync.RWMutex
}

// NewConfigManager 創建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		logger:   g.Log().Cat("MCPConfig"),
		cacheTTL: 5 * time.Minute, // 默認緩存 5 分鐘
	}
}

// LoadMCPConfig 從配置文件加載 MCP 配置（帶緩存）
func (cm *ConfigManager) LoadMCPConfig(ctx context.Context) (*MCPConfig, error) {
	// 檢查緩存
	cm.cacheMutex.RLock()
	if cm.configCache != nil && time.Since(cm.cacheTime) < cm.cacheTTL {
		cm.logger.Debug(ctx, "Using cached MCP configuration")
		defer cm.cacheMutex.RUnlock()
		return cm.configCache, nil
	}
	cm.cacheMutex.RUnlock()

	cm.logger.Info(ctx, "Loading MCP configuration from config file")

	// 重新加載配置
	config, err := cm.loadConfigFromFile(ctx)
	if err != nil {
		return nil, err
	}

	// 更新緩存
	cm.cacheMutex.Lock()
	cm.configCache = config
	cm.cacheTime = time.Now()
	cm.cacheMutex.Unlock()

	cm.logger.Infof(ctx, "Successfully loaded MCP configuration with %d servers", len(config.Servers))
	return config, nil
}

// loadConfigFromFile 從配置文件直接加載完整的 MCP 配置
func (cm *ConfigManager) loadConfigFromFile(ctx context.Context) (*MCPConfig, error) {
	cm.logger.Debugf(ctx, "Loading MCP configuration from config file...")

	// 直接讀取完整的 mcp_config 配置
	vMCPConfig, err := g.Cfg().Get(ctx, "mcp_config")
	if err != nil {
		cm.logger.Infof(ctx, "No mcp_config found in configuration, MCP will be disabled: %v", err)
		return cm.getDefaultConfig(), nil
	}

	var globalConfig MCPGlobalConfig
	if vMCPConfig.IsEmpty() {
		cm.logger.Info(ctx, "mcp_config is empty, MCP will be disabled")
		globalConfig = cm.getDefaultGlobalConfig()
	} else {
		cm.logger.Debugf(ctx, "Found mcp_config, parsing...")
		if err := vMCPConfig.Struct(&globalConfig); err != nil {
			cm.logger.Errorf(ctx, "Failed to parse mcp_config: %v", err)
			globalConfig = cm.getDefaultGlobalConfig()
		} else {
			cm.logger.Debugf(ctx, "Successfully parsed mcp_config: enabled=%t", globalConfig.Enabled)
			// 設置默認值
			cm.setGlobalDefaults(&globalConfig)
		}
	}

	cm.logger.Infof(ctx, "MCP global config loaded: enabled=%t", globalConfig.Enabled)

	// 如果 MCP 未啟用，返回禁用的配置
	if !globalConfig.Enabled {
		cm.logger.Info(ctx, "MCP is disabled in configuration")
		return &MCPConfig{
			Enabled: false,
			Servers: []MCPServerConfig{},
			Global:  globalConfig,
		}, nil
	}

	// 直接讀取 mcp_servers 配置
	vServers, err := g.Cfg().Get(ctx, "mcp_servers")
	if err != nil {
		cm.logger.Debugf(ctx, "No mcp_servers found: %v", err)
		return &MCPConfig{
			Enabled: globalConfig.Enabled, // 使用 globalConfig 的 Enabled 狀態
			Servers: []MCPServerConfig{},
			Global:  globalConfig,
		}, nil
	}

	var servers []MCPServerConfig
	if vServers.IsEmpty() {
		cm.logger.Warning(ctx, "No MCP servers configured")
		servers = []MCPServerConfig{}
	} else {
		if err := vServers.Struct(&servers); err != nil {
			return nil, NewConfigInvalidError(fmt.Sprintf("failed to parse mcp_servers: %v", err))
		}

		// 為每個服務器設置默認值和展開環境變量
		for i := range servers {
			cm.setServerDefaults(&servers[i])
			cm.ExpandEnvironmentVariables(&servers[i])
		}
	}

	config := &MCPConfig{
		Enabled: globalConfig.Enabled, // 使用 globalConfig 的 Enabled 狀態
		Servers: servers,
		Global:  globalConfig,
	}

	// 驗證配置
	if err := cm.validateConfig(config); err != nil {
		return nil, NewConfigInvalidError(fmt.Sprintf("config validation failed: %v", err))
	}

	return config, nil
}

// getDefaultConfig 獲取默認配置
func (cm *ConfigManager) getDefaultConfig() *MCPConfig {
	return &MCPConfig{
		Enabled: false,
		Servers: []MCPServerConfig{},
		Global:  cm.getDefaultGlobalConfig(),
	}
}

// ValidateConfig 驗證 MCP 配置
func ValidateConfig(config *MCPConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 驗證服務器配置
	for i, server := range config.Servers {
		if err := validateServerConfig(server); err != nil {
			return fmt.Errorf("server %d validation failed: %v", i, err)
		}
	}

	// 驗證全局配置
	if err := validateGlobalConfig(config.Global); err != nil {
		return fmt.Errorf("global config validation failed: %v", err)
	}

	return nil
}

// validateServerConfig 驗證服務器配置
func validateServerConfig(config MCPServerConfig) error {
	if config.Name == "" {
		return fmt.Errorf("server name cannot be empty")
	}

	if config.Type == "" {
		return fmt.Errorf("server type cannot be empty")
	}

	// 驗證服務器類型
	validTypes := map[string]bool{
		"stdio": true,
		"http":  true,
		"sse":   true,
	}
	if !validTypes[config.Type] {
		return fmt.Errorf("invalid server type: %s", config.Type)
	}

	// 根據類型驗證特定配置
	switch config.Type {
	case "stdio":
		if config.Command == "" {
			return fmt.Errorf("command cannot be empty for stdio server")
		}
	case "http", "sse":
		if config.URL == "" {
			return fmt.Errorf("URL cannot be empty for %s server", config.Type)
		}
	}

	// 驗證超時格式
	if config.Timeout != "" {
		if _, err := time.ParseDuration(config.Timeout); err != nil {
			return fmt.Errorf("invalid timeout format: %s", config.Timeout)
		}
	}

	// 驗證重試次數
	if config.RetryCount < 0 {
		return fmt.Errorf("retry count cannot be negative")
	}

	return nil
}

// validateGlobalConfig 驗證全局配置
func validateGlobalConfig(config MCPGlobalConfig) error {
	// 驗證默認超時
	if config.DefaultTimeout != "" {
		if _, err := time.ParseDuration(config.DefaultTimeout); err != nil {
			return fmt.Errorf("invalid default timeout format: %s", config.DefaultTimeout)
		}
	}

	// 驗證緩存 TTL
	if config.CacheTTL != "" {
		if _, err := time.ParseDuration(config.CacheTTL); err != nil {
			return fmt.Errorf("invalid cache TTL format: %s", config.CacheTTL)
		}
	}

	// 驗證並發調用數
	if config.MaxConcurrentCalls < 0 {
		return fmt.Errorf("max concurrent calls cannot be negative")
	}

	// 驗證日誌級別
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if config.LogLevel != "" && !validLogLevels[config.LogLevel] {
		return fmt.Errorf("invalid log level: %s", config.LogLevel)
	}

	return nil
}

// ClearCache 清理配置緩存
func (cm *ConfigManager) ClearCache() {
	cm.cacheMutex.Lock()
	defer cm.cacheMutex.Unlock()
	cm.configCache = nil
	cm.cacheTime = time.Time{}
	cm.logger.Info(context.Background(), "MCP configuration cache cleared")
}

// SetCacheTTL 設置緩存過期時間
func (cm *ConfigManager) SetCacheTTL(ttl time.Duration) {
	cm.cacheMutex.Lock()
	defer cm.cacheMutex.Unlock()
	cm.cacheTTL = ttl
	cm.logger.Infof(context.Background(), "MCP configuration cache TTL set to %v", ttl)
}

// GetCacheInfo 獲取緩存信息
func (cm *ConfigManager) GetCacheInfo() map[string]interface{} {
	cm.cacheMutex.RLock()
	defer cm.cacheMutex.RUnlock()

	info := map[string]interface{}{
		"cache_ttl": cm.cacheTTL.String(),
		"has_cache": cm.configCache != nil,
	}

	if cm.configCache != nil {
		info["cache_age"] = time.Since(cm.cacheTime).String()
		info["cache_valid"] = time.Since(cm.cacheTime) < cm.cacheTTL
		info["servers_count"] = len(cm.configCache.Servers)
		info["enabled"] = cm.configCache.Enabled
	}

	return info
}

// setServerDefaults 設置服務器配置默認值
func (cm *ConfigManager) setServerDefaults(config *MCPServerConfig) {
	if config.Timeout == "" {
		config.Timeout = DefaultTimeout
	}

	if config.RetryCount == 0 {
		config.RetryCount = DefaultRetryCount
	}

	// 驗證客戶端類型
	if config.Type == "" {
		config.Type = ClientTypeStdio // 默認為 stdio
	}

	// 根據類型設置特定默認值
	switch config.Type {
	case ClientTypeStdio:
		if config.Command == "" {
			cm.logger.Warningf(context.Background(), "STDIO client %s has no command specified", config.Name)
		}
	case ClientTypeHTTP, ClientTypeSSE:
		if config.URL == "" {
			cm.logger.Warningf(context.Background(), "%s client %s has no URL specified", config.Type, config.Name)
		}
	}
}

// setGlobalDefaults 設置全局配置默認值
func (cm *ConfigManager) setGlobalDefaults(config *MCPGlobalConfig) {
	if config.DefaultTimeout == "" {
		config.DefaultTimeout = DefaultTimeout
	}

	if config.MaxConcurrentCalls == 0 {
		config.MaxConcurrentCalls = DefaultMaxConcurrentCalls
	}

	if config.CacheTTL == "" {
		config.CacheTTL = DefaultCacheTTL
	}

	if config.LogLevel == "" {
		config.LogLevel = "info"
	}

	// 不要強制設置 Enabled 狀態，應該保持用戶配置的值
	// config.Enabled = true  // 移除這行，保持原有的 Enabled 狀態
}

// getDefaultGlobalConfig 獲取默認全局配置
func (cm *ConfigManager) getDefaultGlobalConfig() MCPGlobalConfig {
	return MCPGlobalConfig{
		Enabled:            false, // 默認應該是禁用的，只有明確配置才啟用
		DefaultTimeout:     DefaultTimeout,
		MaxConcurrentCalls: DefaultMaxConcurrentCalls,
		CacheTTL:           DefaultCacheTTL,
		LogLevel:           "info",
	}
}

// validateConfig 驗證配置
func (cm *ConfigManager) validateConfig(config *MCPConfig) error {
	if config == nil {
		return fmt.Errorf("config is nil")
	}

	if !config.Enabled {
		return nil // 如果未啟用，不需要驗證
	}

	// 驗證全局配置
	if err := cm.validateGlobalConfig(&config.Global); err != nil {
		return fmt.Errorf("global config validation failed: %v", err)
	}

	// 驗證服務器配置
	serverNames := make(map[string]bool)
	for i, server := range config.Servers {
		if err := cm.validateServerConfig(&server); err != nil {
			return fmt.Errorf("server config %d validation failed: %v", i, err)
		}

		// 檢查服務器名稱唯一性
		if serverNames[server.Name] {
			return fmt.Errorf("duplicate server name: %s", server.Name)
		}
		serverNames[server.Name] = true
	}

	return nil
}

// validateGlobalConfig 驗證全局配置
func (cm *ConfigManager) validateGlobalConfig(config *MCPGlobalConfig) error {
	// 驗證超時時間
	if _, err := time.ParseDuration(config.DefaultTimeout); err != nil {
		return fmt.Errorf("invalid default timeout: %s", config.DefaultTimeout)
	}

	// 驗證緩存 TTL
	if _, err := time.ParseDuration(config.CacheTTL); err != nil {
		return fmt.Errorf("invalid cache TTL: %s", config.CacheTTL)
	}

	// 驗證並發數
	if config.MaxConcurrentCalls <= 0 {
		return fmt.Errorf("max concurrent calls must be positive: %d", config.MaxConcurrentCalls)
	}

	// 驗證日誌級別
	validLogLevels := []string{"debug", "info", "warning", "error"}
	isValidLogLevel := false
	for _, level := range validLogLevels {
		if strings.ToLower(config.LogLevel) == level {
			isValidLogLevel = true
			break
		}
	}
	if !isValidLogLevel {
		return fmt.Errorf("invalid log level: %s", config.LogLevel)
	}

	return nil
}

// validateServerConfig 驗證服務器配置
func (cm *ConfigManager) validateServerConfig(config *MCPServerConfig) error {
	// 驗證名稱
	if config.Name == "" {
		return fmt.Errorf("server name cannot be empty")
	}

	// 驗證類型
	validTypes := []string{ClientTypeStdio, ClientTypeHTTP, ClientTypeSSE}
	isValidType := false
	for _, validType := range validTypes {
		if config.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid client type: %s", config.Type)
	}

	// 驗證超時時間
	if _, err := time.ParseDuration(config.Timeout); err != nil {
		return fmt.Errorf("invalid timeout: %s", config.Timeout)
	}

	// 驗證重試次數
	if config.RetryCount < 0 {
		return fmt.Errorf("retry count cannot be negative: %d", config.RetryCount)
	}

	// 根據類型驗證特定配置
	switch config.Type {
	case ClientTypeStdio:
		if config.Command == "" {
			return fmt.Errorf("STDIO client requires command")
		}
	case ClientTypeHTTP, ClientTypeSSE:
		if config.URL == "" {
			return fmt.Errorf("%s client requires URL", config.Type)
		}
		if !strings.HasPrefix(config.URL, "http://") && !strings.HasPrefix(config.URL, "https://") {
			return fmt.Errorf("invalid URL format: %s", config.URL)
		}
	}

	return nil
}

// GetServerConfig 根據名稱獲取服務器配置
func (cm *ConfigManager) GetServerConfig(config *MCPConfig, serverName string) (*MCPServerConfig, error) {
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}

	for _, server := range config.Servers {
		if server.Name == serverName {
			return &server, nil
		}
	}

	return nil, NewClientNotFoundError(serverName)
}

// ListServerNames 列出所有服務器名稱
func (cm *ConfigManager) ListServerNames(config *MCPConfig) []string {
	if config == nil {
		return []string{}
	}

	names := make([]string, len(config.Servers))
	for i, server := range config.Servers {
		names[i] = server.Name
	}

	return names
}

// GetServersByType 根據類型獲取服務器配置
func (cm *ConfigManager) GetServersByType(config *MCPConfig, clientType string) []MCPServerConfig {
	if config == nil {
		return []MCPServerConfig{}
	}

	var servers []MCPServerConfig
	for _, server := range config.Servers {
		if server.Type == clientType {
			servers = append(servers, server)
		}
	}

	return servers
}

// ParseTimeout 解析超時時間
func (cm *ConfigManager) ParseTimeout(timeout string) (time.Duration, error) {
	if timeout == "" {
		return time.ParseDuration(DefaultTimeout)
	}

	return time.ParseDuration(timeout)
}

// ExpandEnvironmentVariables 展開環境變量
func (cm *ConfigManager) ExpandEnvironmentVariables(config *MCPServerConfig) {
	// 展開 Headers 中的環境變量
	for key, value := range config.Headers {
		if strings.HasPrefix(value, "${") && strings.HasSuffix(value, "}") {
			envVar := value[2 : len(value)-1]
			if envValue := g.Cfg().MustGet(context.Background(), envVar).String(); envValue != "" {
				config.Headers[key] = envValue
			}
		}
	}

	// 展開 URL 中的環境變量
	if strings.Contains(config.URL, "${") {
		// 簡單的環境變量替換
		url := config.URL
		// 這裡可以添加更複雜的環境變量替換邏輯
		config.URL = url
	}
}

// CreateConfigSummary 創建配置摘要
func (cm *ConfigManager) CreateConfigSummary(config *MCPConfig) string {
	if config == nil {
		return "MCP Config: nil"
	}

	if !config.Enabled {
		return "MCP Config: Disabled"
	}

	summary := fmt.Sprintf("MCP Config: Enabled, %d servers", len(config.Servers))

	// 按類型統計服務器
	typeCounts := make(map[string]int)
	for _, server := range config.Servers {
		typeCounts[server.Type]++
	}

	var typeInfo []string
	for clientType, count := range typeCounts {
		typeInfo = append(typeInfo, fmt.Sprintf("%s: %d", clientType, count))
	}

	if len(typeInfo) > 0 {
		summary += fmt.Sprintf(" (%s)", strings.Join(typeInfo, ", "))
	}

	return summary
}

// WatchConfigChanges 監聽配置變更並自動清理緩存
func (cm *ConfigManager) WatchConfigChanges(ctx context.Context, callback func(*MCPConfig)) error {
	// 實現配置變更監聽邏輯
	// 當配置變更時，清理緩存並調用回調函數
	go func() {
		// 這裡可以實現具體的配置變更監聽邏輯
		// 例如監聽 Nacos 配置變更事件或文件變更事件
		ticker := time.NewTicker(30 * time.Second) // 每30秒檢查一次配置變更
		defer ticker.Stop()

		var lastConfigHash string

		for {
			select {
			case <-ctx.Done():
				cm.logger.Info(ctx, "Config change watching stopped")
				return
			case <-ticker.C:
				// 檢查配置是否有變更
				config, err := cm.loadConfigFromFile(ctx)
				if err != nil {
					cm.logger.Errorf(ctx, "Failed to check config changes: %v", err)
					continue
				}

				// 簡單的配置變更檢測（可以改進為更精確的哈希比較）
				currentHash := cm.CreateConfigSummary(config)
				if lastConfigHash != "" && lastConfigHash != currentHash {
					cm.logger.Info(ctx, "MCP configuration changed, clearing cache")
					cm.ClearCache()
					if callback != nil {
						callback(config)
					}
				}
				lastConfigHash = currentHash
			}
		}
	}()

	cm.logger.Info(ctx, "Config change watching started")
	return nil
}
