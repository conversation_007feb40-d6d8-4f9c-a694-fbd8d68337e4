package common

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gconv"
)

// ToolUtils 工具調用工具函數集合
type ToolUtils struct {
	logger glog.ILogger
}

// NewToolUtils 創建工具函數實例
func NewToolUtils() *ToolUtils {
	return &ToolUtils{
		logger: g.Log().Cat("ToolUtils"),
	}
}

// ConvertToOpenAITools 轉換工具定義為 OpenAI 格式
func (u *ToolUtils) ConvertToOpenAITools(toolDefinitions []ToolDefinition) []map[string]interface{} {
	var tools []map[string]interface{}
	
	for _, toolDef := range toolDefinitions {
		tool := map[string]interface{}{
			"type": "function",
			"function": map[string]interface{}{
				"name":        toolDef.Name,
				"description": toolDef.Description,
				"parameters":  toolDef.Parameters,
			},
		}
		tools = append(tools, tool)
	}
	
	return tools
}

// ConvertToClaudeTools 轉換工具定義為 Claude 格式
func (u *ToolUtils) ConvertToClaudeTools(toolDefinitions []ToolDefinition) []map[string]interface{} {
	var tools []map[string]interface{}
	
	for _, toolDef := range toolDefinitions {
		tool := map[string]interface{}{
			"name":         toolDef.Name,
			"description":  toolDef.Description,
			"input_schema": toolDef.Parameters,
		}
		tools = append(tools, tool)
	}
	
	return tools
}

// ConvertToGeminiTools 轉換工具定義為 Gemini 格式
func (u *ToolUtils) ConvertToGeminiTools(toolDefinitions []ToolDefinition) []map[string]interface{} {
	var tools []map[string]interface{}
	
	for _, toolDef := range toolDefinitions {
		tool := map[string]interface{}{
			"function_declarations": []map[string]interface{}{
				{
					"name":        toolDef.Name,
					"description": toolDef.Description,
					"parameters":  toolDef.Parameters,
				},
			},
		}
		tools = append(tools, tool)
	}
	
	return tools
}

// ExtractToolNameAndClient 從完整工具名稱中提取客戶端名稱和工具名稱
func (u *ToolUtils) ExtractToolNameAndClient(fullToolName string) (clientName, toolName string, err error) {
	parts := strings.SplitN(fullToolName, ".", 2)
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid tool name format: %s, expected format: clientName.toolName", fullToolName)
	}
	
	return parts[0], parts[1], nil
}

// BuildFullToolName 構建完整的工具名稱
func (u *ToolUtils) BuildFullToolName(clientName, toolName string) string {
	return fmt.Sprintf("%s.%s", clientName, toolName)
}

// FilterToolsByClient 根據客戶端名稱過濾工具
func (u *ToolUtils) FilterToolsByClient(tools []ToolDefinition, clientName string) []ToolDefinition {
	var filtered []ToolDefinition
	for _, tool := range tools {
		if tool.ClientName == clientName {
			filtered = append(filtered, tool)
		}
	}
	return filtered
}

// GroupToolsByClient 按客戶端分組工具
func (u *ToolUtils) GroupToolsByClient(tools []ToolDefinition) map[string][]ToolDefinition {
	groups := make(map[string][]ToolDefinition)
	for _, tool := range tools {
		groups[tool.ClientName] = append(groups[tool.ClientName], tool)
	}
	return groups
}

// ValidateToolName 驗證工具名稱格式
func (u *ToolUtils) ValidateToolName(toolName string) error {
	if toolName == "" {
		return fmt.Errorf("tool name cannot be empty")
	}
	
	if !strings.Contains(toolName, ".") {
		return fmt.Errorf("tool name must contain client prefix: %s", toolName)
	}
	
	parts := strings.Split(toolName, ".")
	if len(parts) != 2 {
		return fmt.Errorf("tool name must have exactly one dot: %s", toolName)
	}
	
	if parts[0] == "" || parts[1] == "" {
		return fmt.Errorf("client name and tool name cannot be empty: %s", toolName)
	}
	
	return nil
}

// SanitizeToolArguments 清理和標準化工具參數
func (u *ToolUtils) SanitizeToolArguments(args map[string]interface{}) map[string]interface{} {
	sanitized := make(map[string]interface{})
	
	for key, value := range args {
		// 移除空值
		if value == nil {
			continue
		}
		
		// 轉換字符串
		if str, ok := value.(string); ok {
			str = strings.TrimSpace(str)
			if str == "" {
				continue
			}
			sanitized[key] = str
		} else {
			sanitized[key] = value
		}
	}
	
	return sanitized
}

// FormatToolResult 格式化工具結果
func (u *ToolUtils) FormatToolResult(result *ToolResult, toolName string) string {
	if result == nil {
		return fmt.Sprintf("工具 %s: 無結果", toolName)
	}
	
	if !result.Success {
		return fmt.Sprintf("工具 %s 執行失敗: %s", toolName, result.Error)
	}
	
	if result.Content == "" {
		return fmt.Sprintf("工具 %s 執行成功，但無返回內容", toolName)
	}
	
	return fmt.Sprintf("工具 %s 執行成功:\n%s", toolName, result.Content)
}

// CreateToolCallSummary 創建工具調用摘要
func (u *ToolUtils) CreateToolCallSummary(toolResults map[string]*ToolResult) string {
	if len(toolResults) == 0 {
		return "未執行任何工具調用"
	}
	
	var summary []string
	successCount := 0
	failureCount := 0
	
	for toolName, result := range toolResults {
		if result.Success {
			successCount++
			summary = append(summary, fmt.Sprintf("✓ %s: 成功", toolName))
		} else {
			failureCount++
			summary = append(summary, fmt.Sprintf("✗ %s: 失敗 (%s)", toolName, result.Error))
		}
	}
	
	header := fmt.Sprintf("工具調用摘要 (成功: %d, 失敗: %d):", successCount, failureCount)
	return fmt.Sprintf("%s\n%s", header, strings.Join(summary, "\n"))
}

// EstimateToolCallDuration 估算工具調用持續時間
func (u *ToolUtils) EstimateToolCallDuration(toolName string) time.Duration {
	// 根據工具類型估算執行時間
	if strings.Contains(toolName, "filesystem") {
		return 5 * time.Second
	} else if strings.Contains(toolName, "database") {
		return 10 * time.Second
	} else if strings.Contains(toolName, "http") || strings.Contains(toolName, "api") {
		return 15 * time.Second
	} else if strings.Contains(toolName, "calculation") || strings.Contains(toolName, "math") {
		return 2 * time.Second
	}
	
	// 默認估算時間
	return 10 * time.Second
}

// LogToolCall 記錄工具調用
func (u *ToolUtils) LogToolCall(ctx context.Context, toolName string, args map[string]interface{}, startTime time.Time) {
	duration := time.Since(startTime)
	u.logger.Infof(ctx, "Tool call completed: %s, duration: %v, args: %v", 
		toolName, duration, args)
}

// LogToolResult 記錄工具結果
func (u *ToolUtils) LogToolResult(ctx context.Context, toolName string, result *ToolResult, duration time.Duration) {
	if result.Success {
		u.logger.Infof(ctx, "Tool %s succeeded in %v, content length: %d", 
			toolName, duration, len(result.Content))
	} else {
		u.logger.Errorf(ctx, "Tool %s failed in %v: %s", 
			toolName, duration, result.Error)
	}
}

// CreateTimeoutContext 創建帶超時的上下文
func (u *ToolUtils) CreateTimeoutContext(ctx context.Context, timeout string) (context.Context, context.CancelFunc) {
	duration, err := time.ParseDuration(timeout)
	if err != nil {
		u.logger.Errorf(ctx, "Invalid timeout format: %s, using default 30s", timeout)
		duration = 30 * time.Second
	}
	
	return context.WithTimeout(ctx, duration)
}

// IsToolCallNeeded 簡單的關鍵詞檢查（降級方案）
func (u *ToolUtils) IsToolCallNeeded(message string) bool {
	message = strings.ToLower(message)
	
	// 文件操作關鍵詞
	fileKeywords := []string{"文件", "檔案", "file", "讀取", "寫入", "保存", "刪除", "目錄", "folder"}
	for _, keyword := range fileKeywords {
		if strings.Contains(message, keyword) {
			return true
		}
	}
	
	// 計算關鍵詞
	calcKeywords := []string{"計算", "數學", "加", "減", "乘", "除", "求和", "平均", "統計"}
	for _, keyword := range calcKeywords {
		if strings.Contains(message, keyword) {
			return true
		}
	}
	
	// 查詢關鍵詞
	queryKeywords := []string{"查詢", "搜索", "查找", "檢索", "獲取", "取得"}
	for _, keyword := range queryKeywords {
		if strings.Contains(message, keyword) {
			return true
		}
	}
	
	// 實時信息關鍵詞
	realtimeKeywords := []string{"現在", "當前", "最新", "實時", "今天", "天氣", "股價", "新聞"}
	for _, keyword := range realtimeKeywords {
		if strings.Contains(message, keyword) {
			return true
		}
	}
	
	return false
}

// ExtractKeywordsForTool 根據關鍵詞推薦工具
func (u *ToolUtils) ExtractKeywordsForTool(message string) map[string][]string {
	message = strings.ToLower(message)
	recommendations := make(map[string][]string)
	
	// 文件系統工具
	fileKeywords := []string{"文件", "檔案", "file", "讀取", "寫入", "保存", "刪除", "目錄"}
	for _, keyword := range fileKeywords {
		if strings.Contains(message, keyword) {
			recommendations["filesystem"] = append(recommendations["filesystem"], keyword)
		}
	}
	
	// 計算工具
	calcKeywords := []string{"計算", "數學", "加", "減", "乘", "除", "求和", "平均"}
	for _, keyword := range calcKeywords {
		if strings.Contains(message, keyword) {
			recommendations["calculator"] = append(recommendations["calculator"], keyword)
		}
	}
	
	// 數據庫工具
	dbKeywords := []string{"查詢", "數據", "記錄", "表格", "統計", "分析"}
	for _, keyword := range dbKeywords {
		if strings.Contains(message, keyword) {
			recommendations["database"] = append(recommendations["database"], keyword)
		}
	}
	
	return recommendations
}

// ConvertParametersToString 將參數轉換為字符串（用於日誌）
func (u *ToolUtils) ConvertParametersToString(params map[string]interface{}) string {
	if len(params) == 0 {
		return "{}"
	}
	
	var parts []string
	for key, value := range params {
		valueStr := gconv.String(value)
		if len(valueStr) > 100 {
			valueStr = valueStr[:100] + "..."
		}
		parts = append(parts, fmt.Sprintf("%s: %s", key, valueStr))
	}
	
	return fmt.Sprintf("{%s}", strings.Join(parts, ", "))
}
