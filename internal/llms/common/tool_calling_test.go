package common

import (
	"context"
	"strings"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestToolCallHelper_ValidateToolArguments 測試工具參數驗證
func TestToolCallHelper_ValidateToolArguments(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		helper := &ToolCallHelper{}

		// 創建測試工具定義
		toolDef := ToolDefinition{
			Name:        "test_tool",
			Description: "A test tool",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"name": map[string]interface{}{
						"type":        "string",
						"description": "Name parameter",
					},
					"age": map[string]interface{}{
						"type":        "integer",
						"description": "Age parameter",
					},
				},
				"required": []interface{}{"name"},
			},
		}

		// 測試有效參數
		validArgs := map[string]interface{}{
			"name": "<PERSON>",
			"age":  30,
		}
		err := helper.ValidateToolArguments(ctx, toolDef, validArgs)
		t.AssertNil(err)

		// 測試缺少必需參數
		invalidArgs := map[string]interface{}{
			"age": 30,
		}
		err = helper.ValidateToolArguments(ctx, toolDef, invalidArgs)
		t.AssertNE(err, nil)

		// 測試只有必需參數
		minimalArgs := map[string]interface{}{
			"name": "Jane",
		}
		err = helper.ValidateToolArguments(ctx, toolDef, minimalArgs)
		t.AssertNil(err)
	})
}

// TestToolCallHelper_FormatToolResult 測試工具結果格式化
func TestToolCallHelper_FormatToolResult(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		helper := &ToolCallHelper{}

		// 測試成功結果
		successResult := &ToolResult{
			Success: true,
			Content: "Operation completed successfully",
		}
		formatted := helper.FormatToolResult(successResult)
		t.Assert(strings.Contains(formatted, "Operation completed successfully"), true)
		t.Assert(strings.Contains(formatted, "Success=true"), true)

		// 測試失敗結果
		failureResult := &ToolResult{
			Success: false,
			Error:   "Operation failed",
		}
		formatted = helper.FormatToolResult(failureResult)
		t.Assert(strings.Contains(formatted, "Operation failed"), true)
		t.Assert(strings.Contains(formatted, "Success=false"), true)
	})
}

// TestToolCallHelper_ParseToolName 測試工具名稱解析
func TestToolCallHelper_ParseToolName(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		helper := &ToolCallHelper{}

		// 測試有效的工具名稱
		clientName, toolName, err := helper.ParseToolName("client1.tool1")
		t.AssertNil(err)
		t.AssertEQ(clientName, "client1")
		t.AssertEQ(toolName, "tool1")

		// 測試複雜的工具名稱
		clientName, toolName, err = helper.ParseToolName("my-client.my_tool_name")
		t.AssertNil(err)
		t.AssertEQ(clientName, "my-client")
		t.AssertEQ(toolName, "my_tool_name")

		// 測試無效的工具名稱
		_, _, err = helper.ParseToolName("invalid_name")
		t.AssertNE(err, nil)

		// 測試空工具名稱
		_, _, err = helper.ParseToolName("")
		t.AssertNE(err, nil)

		// 測試只有點的名稱
		_, _, err = helper.ParseToolName(".")
		t.AssertNE(err, nil)
	})
}

// TestToolCallHelper_CreateToolInfo 測試創建工具信息
func TestToolCallHelper_CreateToolInfo(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		helper := &ToolCallHelper{}

		// 創建測試工具定義
		toolDef := ToolDefinition{
			Name:        "test_client.test_tool",
			Description: "A test tool for testing",
			ClientName:  "test_client",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"input": map[string]interface{}{
						"type":        "string",
						"description": "Input parameter",
					},
					"count": map[string]interface{}{
						"type":        "integer",
						"description": "Count parameter",
					},
				},
				"required": []interface{}{"input"},
			},
		}

		toolInfo := helper.CreateToolInfo(toolDef)
		t.AssertEQ(toolInfo.Name, "test_client.test_tool")
		t.AssertEQ(toolInfo.Description, "A test tool for testing")
		t.AssertEQ(toolInfo.ClientName, "test_client")
		t.AssertEQ(len(toolInfo.Parameters), 2)

		// 檢查參數信息
		inputParam := toolInfo.Parameters["input"]
		t.AssertEQ(inputParam.Type, "string")
		t.AssertEQ(inputParam.Description, "Input parameter")
		t.AssertEQ(inputParam.Required, true)

		countParam := toolInfo.Parameters["count"]
		t.AssertEQ(countParam.Type, "integer")
		t.AssertEQ(countParam.Description, "Count parameter")
		t.AssertEQ(countParam.Required, false)
	})
}

// TestToolCallHelper_ValidateParameterType 測試參數類型驗證
func TestToolCallHelper_ValidateParameterType(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		helper := &ToolCallHelper{}

		// 測試字符串類型
		err := helper.ValidateParameterType("string", "hello")
		t.AssertNil(err)

		err = helper.ValidateParameterType("string", 123)
		t.AssertNE(err, nil)

		// 測試整數類型
		err = helper.ValidateParameterType("integer", 42)
		t.AssertNil(err)

		err = helper.ValidateParameterType("integer", 42.5)
		t.AssertNE(err, nil)

		// 測試數字類型
		err = helper.ValidateParameterType("number", 42)
		t.AssertNil(err)

		err = helper.ValidateParameterType("number", 42.5)
		t.AssertNil(err)

		err = helper.ValidateParameterType("number", "not a number")
		t.AssertNE(err, nil)

		// 測試布爾類型
		err = helper.ValidateParameterType("boolean", true)
		t.AssertNil(err)

		err = helper.ValidateParameterType("boolean", false)
		t.AssertNil(err)

		err = helper.ValidateParameterType("boolean", "true")
		t.AssertNE(err, nil)

		// 測試數組類型
		err = helper.ValidateParameterType("array", []interface{}{1, 2, 3})
		t.AssertNil(err)

		err = helper.ValidateParameterType("array", "not an array")
		t.AssertNE(err, nil)

		// 測試對象類型
		err = helper.ValidateParameterType("object", map[string]interface{}{"key": "value"})
		t.AssertNil(err)

		err = helper.ValidateParameterType("object", "not an object")
		t.AssertNE(err, nil)
	})
}

// TestToolResult_String 測試工具結果字符串表示
func TestToolResult_String(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試成功結果
		result := &ToolResult{
			Success: true,
			Content: "Success content",
		}
		str := result.String()
		t.Assert(strings.Contains(str, "Success=true"), true)
		t.Assert(strings.Contains(str, "Success content"), true)

		// 測試失敗結果
		result = &ToolResult{
			Success: false,
			Error:   "Error message",
		}
		str = result.String()
		t.Assert(strings.Contains(str, "Success=false"), true)
		t.Assert(strings.Contains(str, "Error message"), true)
	})
}

// TestParameterInfo_IsRequired 測試參數是否必需
func TestParameterInfo_IsRequired(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試必需參數
		param := ParameterInfo{
			Type:        "string",
			Description: "Required parameter",
			Required:    true,
		}
		t.AssertEQ(param.Required, true)

		// 測試可選參數
		param = ParameterInfo{
			Type:        "string",
			Description: "Optional parameter",
			Required:    false,
		}
		t.AssertEQ(param.Required, false)
	})
}

// BenchmarkToolCallHelper_ValidateToolArguments 性能測試
func BenchmarkToolCallHelper_ValidateToolArguments(b *testing.B) {
	ctx := context.Background()
	helper := &ToolCallHelper{}

	toolDef := ToolDefinition{
		Name:        "benchmark_tool",
		Description: "A benchmark tool",
		Parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "Name parameter",
				},
				"value": map[string]interface{}{
					"type":        "number",
					"description": "Value parameter",
				},
			},
			"required": []interface{}{"name"},
		},
	}

	args := map[string]interface{}{
		"name":  "test",
		"value": 42.5,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = helper.ValidateToolArguments(ctx, toolDef, args)
	}
}

// BenchmarkToolCallHelper_ParseToolName 性能測試
func BenchmarkToolCallHelper_ParseToolName(b *testing.B) {
	helper := &ToolCallHelper{}
	toolName := "client.tool_name"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, _ = helper.ParseToolName(toolName)
	}
}
