package gemini

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/llm"
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestNew 測試 Gemini 實例創建
func TestNew(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		instance := New()
		t.AssertNE(instance, nil)

		geminiInstance, ok := instance.(*Gemini)
		t.Assert(ok, true)
		t.<PERSON>sert<PERSON>(geminiInstance, nil)
	})
}

// TestLogger 測試日誌記錄器
func TestLogger(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		gemini := &Gemini{}
		logger := gemini.logger()
		t.Assert<PERSON>(logger, nil)
	})
}

// TestInitialize 測試初始化方法
func TestInitialize(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{}

		// 測試 nil 參數
		err := gemini.Initialize(ctx, nil, nil)
		t.<PERSON>ser<PERSON>(err, nil)
		t.Assert(strings.Contains(err.<PERSON>r(), "the params is nil"), true)

		// 測試缺少 Vertex 配置
		params := &llm.LLMsConfig{}
		err = gemini.Initialize(ctx, params, nil)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "vertex project id is required"), true)

		// 測試缺少 region
		params = &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				ProjectID: "test-project",
			},
		}
		err = gemini.Initialize(ctx, params, nil)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "vertex region is required"), true)

		// 測試缺少模型名稱
		params = &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				ProjectID: "test-project",
				Region:    "us-central1",
			},
		}
		err = gemini.Initialize(ctx, params, nil)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "gemini model name is required"), true)
	})
}

// TestMapGenAIError 測試 genai SDK 錯誤映射
func TestMapGenAIError(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		gemini := &Gemini{}

		// 測試 nil 錯誤
		err := gemini.mapGenAIError(nil)
		t.AssertEQ(err, nil)

		// 測試認證錯誤
		authErr := errors.New("authentication failed")
		mappedErr := gemini.mapGenAIError(authErr)
		t.AssertNE(mappedErr, nil)
		t.Assert(strings.Contains(mappedErr.Error(), "authentication"), true)

		// 測試配額限制錯誤
		quotaErr := errors.New("quota exceeded")
		mappedErr = gemini.mapGenAIError(quotaErr)
		t.AssertNE(mappedErr, nil)
		t.Assert(strings.Contains(mappedErr.Error(), "quota"), true)

		// 測試無效請求錯誤
		invalidErr := errors.New("invalid request")
		mappedErr = gemini.mapGenAIError(invalidErr)
		t.AssertNE(mappedErr, nil)
		t.Assert(strings.Contains(mappedErr.Error(), "invalid"), true)

		// 測試服務不可用錯誤
		unavailableErr := errors.New("service unavailable")
		mappedErr = gemini.mapGenAIError(unavailableErr)
		t.AssertNE(mappedErr, nil)
		t.Assert(strings.Contains(mappedErr.Error(), "unavailable"), true)

		// 測試內容過濾錯誤
		filterErr := errors.New("content filter blocked")
		mappedErr = gemini.mapGenAIError(filterErr)
		t.AssertNE(mappedErr, nil)
		t.Assert(strings.Contains(mappedErr.Error(), "filter"), true)

		// 測試通用錯誤
		genericErr := errors.New("some unknown error")
		mappedErr = gemini.mapGenAIError(genericErr)
		t.AssertNE(mappedErr, nil)
		t.Assert(strings.Contains(mappedErr.Error(), "genai SDK error"), true)
	})
}

// TestInitializeGenAIChat 測試 genai.Chat 初始化
func TestInitializeGenAIChat(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{
			modelName:       "test-model",
			temperature:     0.7,
			maxOutputTokens: 4096,
		}

		// 測試未初始化客戶端的情況
		err := gemini.initializeGenAIChat(ctx)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "genai client is not initialized"), true)
	})
}

// TestProcessMediaMessageToPart 測試媒體消息轉換
func TestProcessMediaMessageToPart(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{}

		// 測試文本消息
		message := &llm.Message{
			Content:     "Hello, world!",
			ContentType: consts.ContentTypeText,
			MimeType:    "text/plain",
		}

		part, err := gemini.processMediaMessageToPart(ctx, message)
		t.AssertEQ(err, nil)
		t.AssertNE(part, nil)

		// 測試圖片消息（base64 編碼）
		imageData := "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
		imageMessage := &llm.Message{
			Content:     imageData,
			ContentType: consts.ContentMediaFile,
			MimeType:    "image/png",
		}

		part, err = gemini.processMediaMessageToPart(ctx, imageMessage)
		t.AssertEQ(err, nil)
		t.AssertNE(part, nil)
	})
}

// TestSummarizeHistory 測試歷史總結功能
func TestSummarizeHistory(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{}

		// 測試未初始化 genai.Chat 的情況
		err := gemini.summarizeHistory(ctx)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "genai.Chat instance is not initialized"), true)
	})
}

// TestGenerateContent 測試統一內容生成接口
func TestGenerateContent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{
			modelName:       "test-model",
			temperature:     0.7,
			maxOutputTokens: 4096,
		}

		// 測試 nil 請求
		_, err := gemini.GenerateContent(ctx, nil)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "request is nil"), true)

		// 測試空 prompt
		request := &llm.GenerateContentRequest{
			Prompt: "",
		}
		_, err = gemini.GenerateContent(ctx, request)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "prompt cannot be empty"), true)

		// 測試未初始化的客戶端
		request = &llm.GenerateContentRequest{
			Prompt: "Hello",
		}
		_, err = gemini.GenerateContent(ctx, request)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "genai.Chat instance is not initialized"), true)
	})
}

// TestChat 測試聊天功能
func TestChat(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{}

		// 測試 nil 消息
		_, err := gemini.Chat(ctx, nil)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "message is nil"), true)

		// 測試未初始化的 genai.Chat
		message := &llm.Message{
			Content:     "Hello",
			ContentType: consts.ContentTypeText,
		}
		_, err = gemini.Chat(ctx, message)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "genai.Chat instance is not initialized"), true)
	})
}

// TestRelease 測試資源釋放
func TestRelease(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		gemini := &Gemini{
			modelName:       "test-model",
			temperature:     0.7,
			maxOutputTokens: 4096,
		}

		// 釋放資源
		gemini.Release(ctx)

		// 由於 Release 方法目前是空實現，我們只能測試它不會 panic
		t.Assert(true, true)
	})
}
