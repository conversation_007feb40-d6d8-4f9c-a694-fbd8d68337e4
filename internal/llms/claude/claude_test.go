package claude

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/claude"
	"brainHub/internal/model/llm"
	"context"
	"strings"
	"testing"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/test/gtest"
)

// TestNew 測試 Claude 實例創建
func TestNew(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := New()
		t.AssertNE(claude, nil)

		// 驗證類型轉換
		claudeImpl, ok := claude.(*Claude)
		t.Assert(ok, true)
		t.Assert<PERSON>(claudeImpl.httpClient, nil)
	})
}

// TestLogger 測試日誌記錄器
func TestLogger(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := &<PERSON>{}
		logger := claude.logger()
		t.Assert<PERSON>(logger, nil)
	})
}

// TestInitialize 測試初始化方法
func TestInitialize(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := New().(*Claude)
		ctx := context.Background()

		// 測試 nil 參數
		err := claude.Initialize(ctx, nil, nil)
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "the params is nil")

		// 測試空的 Vertex 配置
		params := &llm.LLMsConfig{}
		err = claude.Initialize(ctx, params, nil)
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "vertex project id is required")

		// 測試缺少 region
		params.Vertex.ProjectID = "test-project"
		err = claude.Initialize(ctx, params, nil)
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "vertex region is required")

		// 測試缺少模型名稱
		params.Vertex.Region = "us-central1"
		err = claude.Initialize(ctx, params, nil)
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "claude model name is required")
	})
}

// TestLogConfigSafely 測試安全日誌記錄
func TestLogConfigSafely(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := &Claude{}
		ctx := context.Background()

		// 測試 nil 參數
		claude.logConfigSafely(ctx, nil)

		// 測試正常參數
		params := &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				ProjectID: "test-project",
				Region:    "us-central1",
				ThirdModel: llm.ModelConfig{
					Model:           "claude-3-5-sonnet-20241022",
					Temperature:     0.7,
					MaxOutputTokens: 4096,
				},
			},
		}
		claude.logConfigSafely(ctx, params)
	})
}

// TestCalculateTokenCount 測試 token 計算
func TestCalculateTokenCount(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claudeImpl := New().(*Claude)

		// 添加測試消息到歷史記錄
		claudeImpl.history.Append(claude.ClaudeMessage{
			Role: "user",
			Content: []claude.ContentPart{
				{
					Type: "text",
					Text: "Hello, how are you?",
				},
			},
		})

		claudeImpl.history.Append(claude.ClaudeMessage{
			Role: "assistant",
			Content: []claude.ContentPart{
				{
					Type: "text",
					Text: "I'm doing well, thank you!",
				},
			},
		})

		ctx := context.Background()
		tokenCount, err := claudeImpl.calculateTokenCount(ctx)
		t.AssertNil(err)
		t.Assert(tokenCount > 0, true)

		// 驗證包含基本開銷
		expectedMinTokens := consts.ClaudeTokenOverhead + int32(claudeImpl.history.Len())*consts.ClaudeMsgOverhead
		t.Assert(tokenCount >= expectedMinTokens, true)
	})
}

// TestIsSupportedImageType 測試圖片類型支援
func TestIsSupportedImageType(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := &Claude{}

		// 測試支援的類型
		supportedTypes := []string{
			"image/jpeg",
			"image/png",
			"image/gif",
			"image/webp",
		}

		for _, mimeType := range supportedTypes {
			t.Assert(claude.isSupportedImageType(mimeType), true)
		}

		// 測試不支援的類型
		unsupportedTypes := []string{
			"image/bmp",
			"image/tiff",
			"video/mp4",
			"text/plain",
		}

		for _, mimeType := range unsupportedTypes {
			t.Assert(claude.isSupportedImageType(mimeType), false)
		}

		// 測試大小寫不敏感
		t.Assert(claude.isSupportedImageType("IMAGE/JPEG"), true)
		t.Assert(claude.isSupportedImageType("Image/Png"), true)
	})
}

// TestGetSystemInstruction 測試系統指令處理
func TestGetSystemInstruction(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := &Claude{}
		ctx := context.Background()

		// 測試 nil payload
		instruction := claude.getSystemInstruction(ctx, nil)
		t.Assert(instruction, "")

		// 測試空指令
		payload := &llm.Payload{}
		instruction = claude.getSystemInstruction(ctx, payload)
		t.Assert(instruction, "")

		// 測試正常指令
		payload.SystemInstruction = "You are a helpful assistant. Today is {{.now_date}}"
		instruction = claude.getSystemInstruction(ctx, payload)
		t.AssertNE(instruction, "")
		t.Assert(len(instruction) > 0, true)
		// 驗證日期替換
		t.Assert(instruction != payload.SystemInstruction, true)
	})
}

// TestProcessMediaMessage 測試媒體消息處理
func TestProcessMediaMessage(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claudeImpl := &Claude{}
		ctx := context.Background()

		// 測試不支援的媒體類型
		message := &llm.Message{
			Content:     []byte("test data"),
			ContentType: consts.ContentMediaFile,
			MimeType:    "video/mp4",
		}

		_, err := claudeImpl.processMediaMessage(ctx, message)
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "Unsupported media type: video/mp4")

		// 測試支援的圖片類型
		message.MimeType = "image/jpeg"
		claudeMessage, err := claudeImpl.processMediaMessage(ctx, message)
		t.AssertNil(err)
		t.Assert(claudeMessage.Role, "user")
		t.Assert(len(claudeMessage.Content), 2) // 圖片 + 文本
		t.Assert(claudeMessage.Content[0].Type, "image")
		t.Assert(claudeMessage.Content[1].Type, "text")
	})
}

// TestRelease 測試資源釋放
func TestRelease(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claudeImpl := New().(*Claude)

		// 設置測試數據
		claudeImpl.endpoint = "test-endpoint"
		claudeImpl.accessToken = "test-token"
		claudeImpl.projectID = "test-project"
		claudeImpl.region = "us-central1"
		claudeImpl.modelName = "claude-3-5-sonnet"
		claudeImpl.temperature = 0.7
		claudeImpl.maxOutputTokens = 4096

		// 添加測試歷史記錄
		claudeImpl.history.Append(claude.ClaudeMessage{
			Role:    "user",
			Content: []claude.ContentPart{{Type: "text", Text: "test"}},
		})

		ctx := context.Background()
		claudeImpl.Release(ctx)

		// 驗證所有字段都被重置
		t.Assert(claudeImpl.endpoint, "")
		t.Assert(claudeImpl.accessToken, "")
		t.Assert(claudeImpl.projectID, "")
		t.Assert(claudeImpl.region, "")
		t.Assert(claudeImpl.modelName, "")
		t.Assert(claudeImpl.temperature, float32(0))
		t.Assert(claudeImpl.maxOutputTokens, int32(0))
		t.Assert(claudeImpl.history, nil)
		t.Assert(claudeImpl.payload, nil)
	})
}

// TestInitializeAccessToken 測試訪問令牌初始化
func TestInitializeAccessToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claudeImpl := &Claude{
			projectID: "test-project",
			region:    "us-central1",
		}
		ctx := context.Background()

		// 測試不存在的憑證文件
		err := claudeImpl.initializeAccessToken(ctx, "/non/existent/file.json")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "credential file does not exist")

		// 測試空文件路徑
		err = claudeImpl.initializeAccessToken(ctx, "")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "credential file does not exist")
	})
}

// TestApplyDefaults 測試預設值應用
func TestApplyDefaults(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		claude := &Claude{
			temperature:     0.7,
			maxOutputTokens: 4096,
		}

		// 測試空請求
		request := &llm.GenerateContentRequest{}
		claude.applyDefaults(ctx, request)

		t.Assert(request.MaxContinuations > 0, true)
		t.Assert(request.TotalTokenBudget > 0, true)
		t.AssertNE(request.Temperature, nil)
		t.Assert(*request.Temperature, float32(0.7))

		// 測試已設置的值不被覆蓋
		request = &llm.GenerateContentRequest{
			MaxContinuations: 5,
			TotalTokenBudget: 10000,
			Temperature:      &[]float32{0.5}[0],
		}

		originalTemp := *request.Temperature
		claude.applyDefaults(ctx, request)

		t.Assert(request.MaxContinuations, 5)
		t.Assert(request.TotalTokenBudget, int32(10000))
		t.Assert(*request.Temperature, originalTemp)
	})
}

// TestIsContentComplete 測試內容完整性檢測
func TestIsContentComplete(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := &Claude{}

		// 測試完整內容
		complete, reason := claude.isContentComplete("這是一個完整的句子。")
		t.Assert(complete, true)
		t.Assert(reason, "complete")

		// 測試空內容
		complete, reason = claude.isContentComplete("")
		t.Assert(complete, false)
		t.Assert(reason, "empty_content")

		// 測試未完成的程式碼區塊
		complete, reason = claude.isContentComplete("```python\ndef hello():\n    print('hi')")
		t.Assert(complete, false)
		t.Assert(reason, "incomplete_code_block")

		// 測試完整的程式碼區塊
		complete, reason = claude.isContentComplete("```python\ndef hello():\n    print('hi')\n```")
		t.Assert(complete, true)
		t.Assert(reason, "complete")

		// 測試未完成標記
		complete, reason = claude.isContentComplete("這是一個未完成的句子，接下來我們將...")
		t.Assert(complete, false)
		t.Assert(reason, "explicit_incomplete_marker")

		// 測試不完整的句子
		complete, reason = claude.isContentComplete("這是一個很長的不完整句子，沒有適當的結尾")
		t.Assert(complete, false)
		t.Assert(reason, "incomplete_sentence")

		// 測試短內容
		complete, reason = claude.isContentComplete("好的")
		t.Assert(complete, true)
		t.Assert(reason, "short_content_complete")
	})
}

// TestGenerateContent 測試統一內容生成接口
func TestGenerateContent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		claude := &Claude{
			maxOutputTokens: 4096,
		}

		// 測試 nil 請求
		_, err := claude.GenerateContent(ctx, nil)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "request cannot be nil"), true)

		// 測試空 prompt
		request := &llm.GenerateContentRequest{
			Prompt: "",
		}
		_, err = claude.GenerateContent(ctx, request)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "prompt cannot be empty"), true)

		// 測試未初始化的 HTTP 客戶端
		request = &llm.GenerateContentRequest{
			Prompt: "test prompt",
		}
		_, err = claude.GenerateContent(ctx, request)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "claude http client is not initialized"), true)
	})
}

// TestGenerateContentSimple 測試簡化版本的內容生成
func TestGenerateContentSimple(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		claude := &Claude{
			maxOutputTokens: 4096,
		}

		// 由於沒有實際的 HTTP 客戶端，這個測試會失敗
		// 但我們可以測試參數傳遞
		_, err := claude.GenerateContentSimple(ctx, "test prompt")
		t.AssertNE(err, nil) // 預期會失敗，因為沒有初始化 HTTP 客戶端
	})
}

// TestAPIVersionConfiguration 測試 API 版本配置
func TestAPIVersionConfiguration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := &Claude{}

		// 測試有 API 版本的配置
		params := &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				ProjectID:      "test-project",
				Region:         "us-central1",
				CredentialFile: "/test/path/credentials.json",
				ThirdModel: llm.ModelConfig{
					Model:           "claude-3-5-sonnet-20241022",
					APIVersion:      "vertex-2023-10-16",
					Temperature:     0.7,
					MaxOutputTokens: 4096,
				},
			},
		}

		// 模擬設置 API 版本的邏輯
		if !g.IsEmpty(params.Vertex.ThirdModel.APIVersion) {
			claude.apiVersion = params.Vertex.ThirdModel.APIVersion
		} else {
			claude.apiVersion = "vertex-2023-10-16" // Google Vertex AI Claude API 專用版本
		}

		t.Assert(claude.apiVersion, "vertex-2023-10-16")

		// 測試沒有 API 版本的配置
		params.Vertex.ThirdModel.APIVersion = ""
		if g.IsEmpty(params.Vertex.ThirdModel.APIVersion) {
			claude.apiVersion = "vertex-2023-10-16" // 預設值
		}

		t.Assert(claude.apiVersion, "vertex-2023-10-16")
	})
}

// TestClaudeRequestFormat 測試 Claude 請求格式是否符合 Google Vertex AI API 規範
func TestClaudeRequestFormat(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建測試請求
		request := claude.ClaudeRequest{
			AnthropicVersion: "vertex-2023-10-16",
			MaxTokens:        1024,
			Temperature:      0.7,
			Messages: []claude.ClaudeMessage{
				{
					Role: "user",
					Content: []claude.ContentPart{
						{
							Type: "text",
							Text: "Hello, Claude!",
						},
					},
				},
			},
			Stream: false,
		}

		// 序列化為 JSON，使用自定義的 MarshalJSON 方法
		requestBytes, err := request.MarshalJSON()
		t.AssertNil(err)
		jsonStr := string(requestBytes)

		// 記錄生成的 JSON 以便調試
		t.Logf("Generated JSON: %s", jsonStr)

		// 驗證 JSON 結構
		parsedJson := gjson.New(jsonStr)
		t.Assert(parsedJson.Get("anthropic_version").String(), "vertex-2023-10-16")
		t.Assert(parsedJson.Get("max_tokens").Int32(), int32(1024))
		t.Assert(parsedJson.Get("temperature").Float32(), float32(0.7))
		t.Assert(parsedJson.Get("stream").Bool(), false)
		t.Assert(len(parsedJson.Get("messages").Array()), 1)

		// 調試消息結構
		t.Logf("Messages array: %v", parsedJson.Get("messages").Array())
		t.Logf("First message: %v", parsedJson.Get("messages.0"))
		t.Logf("First message role: %v", parsedJson.Get("messages.0.role"))

		t.Assert(parsedJson.Get("messages.0.role").String(), "user")
		t.Assert(parsedJson.Get("messages.0.content.0.type").String(), "text")
		t.Assert(parsedJson.Get("messages.0.content.0.text").String(), "Hello, Claude!")

		// 驗證沒有不應該存在的字段
		t.Assert(parsedJson.Get("messages.0.content.0.source").IsNil(), true)
	})
}

// TestContentPartSerialization 測試 ContentPart 的 JSON 序列化
func TestContentPartSerialization(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試文本類型的 ContentPart
		textPart := claude.ContentPart{
			Type: "text",
			Text: "Hello, Claude!",
		}

		// 序列化文本部分
		textJson, err := textPart.MarshalJSON()
		t.AssertNil(err)
		t.Logf("Text ContentPart JSON: %s", string(textJson))

		// 驗證文本部分只包含 type 和 text 字段
		parsedTextJson := gjson.New(string(textJson))
		t.Assert(parsedTextJson.Get("type").String(), "text")
		t.Assert(parsedTextJson.Get("text").String(), "Hello, Claude!")
		// 確保沒有 source 字段
		t.Assert(parsedTextJson.Get("source").IsNil(), true)

		// 測試圖片類型的 ContentPart
		imagePart := claude.ContentPart{
			Type: "image",
			Source: &claude.ImageSource{
				Type:      "base64",
				MediaType: "image/jpeg",
				Data:      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
			},
		}

		// 序列化圖片部分
		imageJson, err := imagePart.MarshalJSON()
		t.AssertNil(err)
		t.Logf("Image ContentPart JSON: %s", string(imageJson))

		// 驗證圖片部分只包含 type 和 source 字段
		parsedImageJson := gjson.New(string(imageJson))
		t.Assert(parsedImageJson.Get("type").String(), "image")
		t.Assert(parsedImageJson.Get("source.type").String(), "base64")
		t.Assert(parsedImageJson.Get("source.media_type").String(), "image/jpeg")
		// 確保沒有 text 字段
		t.Assert(parsedImageJson.Get("text").IsNil(), true)
	})
}
