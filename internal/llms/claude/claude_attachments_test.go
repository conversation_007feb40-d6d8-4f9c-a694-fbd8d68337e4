package claude

import (
	"brainHub/internal/model"
	claudeModel "brainHub/internal/model/claude"
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestProcessAttachments 測試附件處理功能
func TestProcessAttachments(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建 Claude 實例
		claude := New().(*Claude)

		ctx := context.Background()

		// 測試 nil 附件
		err := claude.processAttachments(ctx, nil)
		t.AssertNil(err)

		// 測試純文本附件處理
		attachments := &model.Asset{
			PlainText: []string{"這是純文本內容測試"},
		}

		// 記錄處理前的歷史記錄數量
		initialHistoryCount := claude.history.Len()

		// 處理附件
		err = claude.processAttachments(ctx, attachments)
		t.AssertNil(err)

		// 驗證歷史記錄是否增加
		t.Assert(claude.history.Len() > initialHistoryCount, true)

		// 驗證處理了 1 個純文本附件
		expectedCount := initialHistoryCount + 1
		t.Assert(claude.history.Len(), expectedCount)
	})
}

// TestProcessAttachmentsWithNonExistentFiles 測試不存在文件的處理
func TestProcessAttachmentsWithNonExistentFiles(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := New().(*Claude)

		ctx := context.Background()

		// 測試不存在的文件
		attachments := &model.Asset{
			WebPageFiles: []string{"/non/existent/file.md"},
			Files:        []string{"/non/existent/file.txt"},
			PlainText:    []string{"有效的純文本"},
		}

		initialHistoryCount := claude.history.Len()

		// 處理附件（應該跳過不存在的文件，但處理純文本）
		err := claude.processAttachments(ctx, attachments)
		t.AssertNil(err)

		// 驗證只處理了純文本（歷史記錄增加 1）
		t.Assert(claude.history.Len(), initialHistoryCount+1)
	})
}

// TestProcessAttachmentsEmptyContent 測試空內容處理
func TestProcessAttachmentsEmptyContent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := New().(*Claude)

		ctx := context.Background()

		// 測試空附件
		attachments := &model.Asset{
			WebPageFiles: []string{},
			Files:        []string{},
			PlainText:    []string{},
		}

		initialHistoryCount := claude.history.Len()

		// 處理空附件
		err := claude.processAttachments(ctx, attachments)
		t.AssertNil(err)

		// 驗證歷史記錄沒有變化
		t.Assert(claude.history.Len(), initialHistoryCount)
	})
}

// TestProcessAttachmentsWithFiles 測試文件處理功能
func TestProcessAttachmentsWithFiles(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := New().(*Claude)

		ctx := context.Background()

		// 創建測試文件
		tempDir := t.TempDir()

		// 創建測試文本文件
		textFile := filepath.Join(tempDir, "test.txt")
		err := os.WriteFile(textFile, []byte("這是測試文本內容"), 0644)
		t.AssertNil(err)

		// 創建測試 Markdown 文件
		mdFile := filepath.Join(tempDir, "test.md")
		err = os.WriteFile(mdFile, []byte("# 測試標題\n這是測試 Markdown 內容"), 0644)
		t.AssertNil(err)

		// 測試文件附件處理
		attachments := &model.Asset{
			WebPageFiles: []string{mdFile},
			Files:        []string{textFile},
			PlainText:    []string{"這是純文本內容"},
		}

		initialHistoryCount := claude.history.Len()

		// 處理附件
		err = claude.processAttachments(ctx, attachments)
		t.AssertNil(err)

		// 驗證歷史記錄增加了 3 個（1個網頁文件 + 1個普通文件 + 1個純文本）
		expectedCount := initialHistoryCount + 3
		t.Assert(claude.history.Len(), expectedCount)

		// 驗證消息內容
		if claude.history.Len() >= 3 {
			historySlice := claude.history.Slice()

			// 檢查網頁文件消息
			if webPageMsg, ok := historySlice[0].(claudeModel.ClaudeMessage); ok {
				t.Assert(webPageMsg.Role, "user")
				t.Assert(len(webPageMsg.Content) > 0, true)
				t.Assert(webPageMsg.Content[0].Type, "text")
			}

			// 檢查普通文件消息
			if fileMsg, ok := historySlice[1].(claudeModel.ClaudeMessage); ok {
				t.Assert(fileMsg.Role, "user")
				t.Assert(len(fileMsg.Content) > 0, true)
				t.Assert(fileMsg.Content[0].Type, "text")
			}

			// 檢查純文本消息
			if plainTextMsg, ok := historySlice[2].(claudeModel.ClaudeMessage); ok {
				t.Assert(plainTextMsg.Role, "user")
				t.Assert(len(plainTextMsg.Content) > 0, true)
				t.Assert(plainTextMsg.Content[0].Type, "text")
			}
		}
	})
}
