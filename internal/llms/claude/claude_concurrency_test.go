package claude

import (
	"brainHub/internal/model/claude"
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestClaudeHistoryConcurrency 測試 Claude 歷史記錄的併發安全性
func TestClaudeHistoryConcurrency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建 Claude 實例
		claudeInstance := New().(*Claude)

		// 驗證 history 已正確初始化為 garray.Array
		t.AssertNE(claudeInstance.history, nil)
		t.Assert(claudeInstance.history.Len(), 0)

		// 併發測試參數
		numGoroutines := 10
		messagesPerGoroutine := 5
		var wg sync.WaitGroup

		// 併發添加消息
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				for j := 0; j < messagesPerGoroutine; j++ {
					message := claude.ClaudeMessage{
						Role: "user",
						Content: []claude.ContentPart{
							{
								Type: "text",
								Text: fmt.Sprintf("Message from goroutine %d, message %d", goroutineID, j),
							},
						},
					}

					// 使用 garray.Array 的線程安全方法
					claudeInstance.history.Append(message)

					// 短暫延遲以增加併發競爭
					time.Sleep(time.Millisecond)
				}
			}(i)
		}

		// 等待所有 goroutine 完成
		wg.Wait()

		// 驗證所有消息都被正確添加
		expectedCount := numGoroutines * messagesPerGoroutine
		t.Assert(claudeInstance.history.Len(), expectedCount)

		// 驗證歷史記錄內容的完整性
		historySlice := claudeInstance.history.Slice()
		t.Assert(len(historySlice), expectedCount)

		// 驗證每個消息都是正確的類型
		for _, item := range historySlice {
			_, ok := item.(claude.ClaudeMessage)
			t.Assert(ok, true)
		}

		// 測試併發讀取
		var readWg sync.WaitGroup
		readResults := make([]int, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			readWg.Add(1)
			go func(goroutineID int) {
				defer readWg.Done()

				// 併發讀取歷史記錄長度
				readResults[goroutineID] = claudeInstance.history.Len()
			}(i)
		}

		readWg.Wait()

		// 驗證所有讀取結果都一致
		for _, result := range readResults {
			t.Assert(result, expectedCount)
		}

		// 測試清空操作
		claudeInstance.history.Clear()
		t.Assert(claudeInstance.history.Len(), 0)
	})
}

// TestClaudeCalculateTokenCountConcurrency 測試 token 計算的併發安全性
func TestClaudeCalculateTokenCountConcurrency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Claude 實例並添加一些測試數據
		claudeInstance := New().(*Claude)

		// 添加測試消息
		for i := 0; i < 5; i++ {
			message := claude.ClaudeMessage{
				Role: "user",
				Content: []claude.ContentPart{
					{
						Type: "text",
						Text: fmt.Sprintf("Test message %d with some content for token calculation", i),
					},
				},
			}
			claudeInstance.history.Append(message)
		}

		// 併發調用 calculateTokenCount
		numGoroutines := 10
		var wg sync.WaitGroup
		results := make([]int32, numGoroutines)
		errors := make([]error, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				// 併發計算 token 數量
				count, err := claudeInstance.calculateTokenCount(ctx)
				results[goroutineID] = count
				errors[goroutineID] = err
			}(i)
		}

		wg.Wait()

		// 驗證所有結果都一致且沒有錯誤
		expectedCount := results[0]
		for i, result := range results {
			t.Assert(errors[i], nil)
			t.Assert(result, expectedCount)
			t.Assert(result > 0, true) // 應該有一些 token
		}
	})
}

// TestClaudeDeadlockPrevention 測試死鎖預防
func TestClaudeDeadlockPrevention(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Claude 實例
		claudeInstance := New().(*Claude)

		// 模擬複雜的併發操作，確保不會發生死鎖
		numGoroutines := 20
		var wg sync.WaitGroup

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				// 混合讀寫操作
				for j := 0; j < 10; j++ {
					switch j % 4 {
					case 0:
						// 添加消息
						message := claude.ClaudeMessage{
							Role: "user",
							Content: []claude.ContentPart{
								{
									Type: "text",
									Text: fmt.Sprintf("Goroutine %d message %d", goroutineID, j),
								},
							},
						}
						claudeInstance.history.Append(message)

					case 1:
						// 讀取長度
						_ = claudeInstance.history.Len()

					case 2:
						// 獲取切片
						_ = claudeInstance.history.Slice()

					case 3:
						// 計算 token（如果有數據）
						if claudeInstance.history.Len() > 0 {
							_, _ = claudeInstance.calculateTokenCount(ctx)
						}
					}

					// 短暫延遲
					time.Sleep(time.Microsecond * 100)
				}
			}(i)
		}

		// 設置超時以檢測死鎖
		done := make(chan bool, 1)
		go func() {
			wg.Wait()
			done <- true
		}()

		select {
		case <-done:
			// 測試成功完成，沒有死鎖
			t.Log("Concurrency test completed successfully without deadlock")
		case <-time.After(10 * time.Second):
			// 超時，可能發生死鎖
			t.Fatal("Test timed out, possible deadlock detected")
		}

		// 驗證最終狀態
		finalCount := claudeInstance.history.Len()
		t.Assert(finalCount >= 0, true)
		t.Logf("Final history count: %d", finalCount)
	})
}

// TestClaudeReleaseConcurrency 測試 Release 方法的併發安全性
func TestClaudeReleaseConcurrency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Claude 實例並添加數據
		claudeInstance := New().(*Claude)

		// 添加測試數據
		for i := 0; i < 10; i++ {
			message := claude.ClaudeMessage{
				Role: "user",
				Content: []claude.ContentPart{
					{
						Type: "text",
						Text: fmt.Sprintf("Test message %d", i),
					},
				},
			}
			claudeInstance.history.Append(message)
		}

		// 併發調用 Release
		numGoroutines := 5
		var wg sync.WaitGroup

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				claudeInstance.Release(ctx)
			}()
		}

		wg.Wait()

		// 驗證資源已被清理
		t.Assert(claudeInstance.history, nil)
		t.Assert(claudeInstance.httpClient, nil)
		t.Assert(claudeInstance.payload, nil)
	})
}
