package claude

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/llm"
	"context"
	"testing"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/test/gtest"
)

// TestTokenRefreshMechanism 測試 token 刷新機制
func TestTokenRefreshMechanism(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Claude 實例
		claude := New().(*Claude)

		// 模擬初始化狀態
		claude.projectID = "test-project"
		claude.region = "us-central1"
		claude.llmsConfig = &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				CredentialFile: "/path/to/test/credentials.json",
			},
		}

		// 測試 refreshAccessToken 方法
		t.AssertNE(claude.refreshAccessToken, nil)

		// 測試緩存清除邏輯
		cacheKey := "claude_token_test-project_us-central1"

		// 設置測試緩存
		err := gcache.Set(ctx, cacheKey, "test-token", time.Minute)
		t.AssertNil(err)

		// 驗證緩存存在
		cachedValue, err := gcache.Get(ctx, cacheKey)
		t.AssertNil(err)
		t.AssertNE(cachedValue, nil)

		// 清除緩存（模擬 refreshAccessToken 的行為）
		_, err = gcache.Remove(ctx, cacheKey)
		t.AssertNil(err)

		// 驗證緩存已清除
		cachedValue, err = gcache.Get(ctx, cacheKey)
		t.AssertNil(err)
		t.AssertEQ(cachedValue, nil)
	})
}

// TestAuthenticationErrorDetection 測試認證錯誤檢測
func TestAuthenticationErrorDetection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 401 錯誤檢測
		err401 := gerror.NewCodef(gcode.CodeNotAuthorized, "Authentication failed with status code 401")
		t.Assert(gerror.HasCode(err401, gcode.CodeNotAuthorized), true)

		// 測試 403 錯誤檢測
		err403 := gerror.NewCodef(gcode.CodeNotAuthorized, "Authentication failed with status code 403")
		t.Assert(gerror.HasCode(err403, gcode.CodeNotAuthorized), true)

		// 測試其他錯誤不會被誤判
		errOther := gerror.New("Some other error")
		t.Assert(gerror.HasCode(errOther, gcode.CodeNotAuthorized), false)
	})
}

// TestTokenCacheKeyGeneration 測試 token 緩存鍵生成
func TestTokenCacheKeyGeneration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		projectID := "test-project-123"
		region := "asia-east1"

		expectedKey := "claude_token_test-project-123_asia-east1"
		actualKey := g.NewVar(projectID).String() + "_" + g.NewVar(region).String()
		actualKey = "claude_token_" + actualKey

		t.AssertEQ(actualKey, expectedKey)
	})
}

// TestRetryLogicWithTokenRefresh 測試包含 token 刷新的重試邏輯
func TestRetryLogicWithTokenRefresh(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 這個測試驗證重試邏輯的結構是否正確
		// 實際的 API 調用需要真實的憑證文件，所以這裡只測試邏輯結構

		maxRetryAttempts := consts.ClaudeMaxRetryAttempts
		t.AssertEQ(maxRetryAttempts, 3)

		// 驗證重試常數設置合理
		t.AssertGT(maxRetryAttempts, 1)
		t.AssertLT(maxRetryAttempts, 10)
	})
}

// TestPayloadValidation 測試 payload 驗證邏輯
func TestPayloadValidation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		claude := New().(*Claude)

		// 測試空 payload
		claude.payload = nil
		t.AssertEQ(claude.payload, nil)

		// 測試有效 llmsConfig
		validConfig := &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				CredentialFile: "/valid/path/credentials.json",
			},
		}
		claude.llmsConfig = validConfig
		t.AssertNE(claude.llmsConfig, nil)
		t.AssertNE(claude.llmsConfig.Vertex.CredentialFile, "")

		// 測試無效 llmsConfig（缺少憑證文件）
		invalidConfig := &llm.LLMsConfig{
			Vertex: llm.VertexConfig{
				CredentialFile: "",
			},
		}
		claude.llmsConfig = invalidConfig
		t.AssertEQ(claude.llmsConfig.Vertex.CredentialFile, "")
	})
}
