package embeddings

import (
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestNewLLMParamsAdapter 測試創建適配器
func TestNewLLMParamsAdapter(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()
		t.AssertNE(adapter, nil)
	})
}

// TestDetermineEmbeddingProvider 測試確定提供商
func TestDetermineEmbeddingProvider(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 Azure OpenAI
		llmParams := &model.LLMParams{
			LLMType: consts.AOAI,
		}
		provider, err := adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingAOAI)

		// 測試 OpenAI
		llmParams.LLMType = "openai"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingOpenAI)

		// 測試 Vertex AI Gemini
		llmParams.LLMType = consts.VertexAIGemini
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 測試 Vertex AI Claude
		llmParams.LLMType = consts.VertexAIClaude
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 測試從 BaseURL 推斷 Azure OpenAI
		llmParams.LLMType = "unknown"
		llmParams.BaseUrl = "https://test.openai.azure.com"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingAOAI)

		// 測試從 BaseURL 推斷 OpenAI
		llmParams.BaseUrl = "https://api.openai.com"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingOpenAI)

		// 測試從 BaseURL 推斷 Vertex AI
		llmParams.BaseUrl = "https://us-central1-aiplatform.googleapis.com"
		provider, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertEQ(err, nil)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 測試無法確定提供商
		llmParams.LLMType = "unknown"
		llmParams.BaseUrl = "https://unknown.com"
		_, err = adapter.determineEmbeddingProvider(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("cannot determine embedding provider", err.Error())
	})
}

// TestDetermineEmbeddingModel 測試確定模型
func TestDetermineEmbeddingModel(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試使用 EmbeddingModel 字段
		llmParams := &model.LLMParams{
			EmbeddingModel: "custom-embedding-model",
			LLMType:        consts.AOAI,
		}
		model := adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "custom-embedding-model")

		// 測試 Azure OpenAI 默認模型
		llmParams.EmbeddingModel = ""
		llmParams.LLMType = consts.AOAI
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "text-embedding-ada-002")

		// 測試 OpenAI 默認模型
		llmParams.LLMType = "openai"
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "text-embedding-3-small")

		// 測試 Vertex AI 默認模型（回退到 AOAI）
		llmParams.LLMType = consts.VertexAIGemini
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, "text-embedding-ada-002") // 回退到 AOAI 默認模型

		// 測試未知類型默認模型
		llmParams.LLMType = "unknown"
		model = adapter.determineEmbeddingModel(llmParams)
		t.Assert(model, consts.EmbeddingDefaultModel)
	})
}

// TestValidateLLMParamsForEmbedding 測試驗證 LLMParams
func TestValidateLLMParamsForEmbedding(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 nil 參數
		err := adapter.ValidateLLMParamsForEmbedding(nil)
		t.AssertNE(err, nil)
		t.AssertIN("LLMParams cannot be nil", err.Error())

		// 測試缺少 LLMType
		llmParams := &model.LLMParams{}
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("LLMType is required", err.Error())

		// 測試 Azure OpenAI 缺少必需字段
		llmParams.LLMType = consts.AOAI
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("BaseUrl is required", err.Error())

		llmParams.BaseUrl = "https://test.openai.azure.com"
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("Token is required", err.Error())

		// 測試完整的 Azure OpenAI 配置
		llmParams.Token = "test-token"
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertEQ(err, nil)

		// 測試 OpenAI 缺少 Token
		llmParams.LLMType = "openai"
		llmParams.Token = ""
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertNE(err, nil)
		t.AssertIN("Token is required", err.Error())

		// 測試完整的 OpenAI 配置
		llmParams.Token = "test-token"
		err = adapter.ValidateLLMParamsForEmbedding(llmParams)
		t.AssertEQ(err, nil)
	})
}

// TestExtractEmbeddingModelFromJSON 測試從 JSON 提取模型
func TestExtractEmbeddingModelFromJSON(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試空字符串
		model, err := adapter.ExtractEmbeddingModelFromJSON("")
		t.AssertEQ(err, nil)
		t.Assert(model, "")

		// 測試有效 JSON
		jsonStr := `{"embedding_model": "text-embedding-ada-002"}`
		model, err = adapter.ExtractEmbeddingModelFromJSON(jsonStr)
		t.AssertEQ(err, nil)
		t.Assert(model, "text-embedding-ada-002")

		// 測試沒有 embedding_model 字段的 JSON
		jsonStr = `{"other_field": "value"}`
		model, err = adapter.ExtractEmbeddingModelFromJSON(jsonStr)
		t.AssertEQ(err, nil)
		t.Assert(model, "")

		// 測試無效 JSON
		jsonStr = `{invalid json}`
		_, err = adapter.ExtractEmbeddingModelFromJSON(jsonStr)
		t.AssertNE(err, nil)
		t.AssertIN("failed to parse JSON", err.Error())
	})
}

// TestGlobalAdapter 測試全局適配器
func TestGlobalAdapter(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試獲取全局適配器
		adapter1 := GetAdapter()
		adapter2 := GetAdapter()
		t.Assert(adapter1, adapter2) // 應該是同一個實例

		// 測試便捷函數
		llmParams := &model.LLMParams{
			LLMType:        consts.AOAI,
			BaseUrl:        "https://test.openai.azure.com",
			Token:          "test-token",
			EmbeddingModel: "text-embedding-ada-002",
		}

		// 測試驗證便捷函數
		err := ValidateLLMParamsForEmbedding(llmParams)
		t.AssertEQ(err, nil)

		// 測試創建便捷函數
		embeddingInstance, err := CreateEmbeddingFromLLMParams(llmParams)
		if err != nil {
			// 如果失敗，可能是因為網絡連接問題，這是正常的
			t.AssertIN("Failed to create", err.Error())
		} else {
			// 如果成功，驗證實例不為空
			t.AssertNE(embeddingInstance, nil)
		}
	})
}

// TestExtractAOAIConfigFromBaseURL 測試從 base_url JSON 中提取 AOAI 配置
func TestExtractAOAIConfigFromBaseURL(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 Gemini 配置格式
		geminiBaseURL := `{"region":"us-east5","project_id":"aile-ai-development","include_thoughts":false,"thinking_budget":0,"base_url":"https://aile-chatbot.openai.azure.com/","api_version":"2024-05-01-preview"}`

		aoaiConfig, err := adapter.extractAOAIConfigFromBaseURL(geminiBaseURL)
		t.AssertNil(err)
		t.AssertNE(aoaiConfig, nil)
		t.Assert(aoaiConfig.BaseURL, "https://aile-chatbot.openai.azure.com/")
		t.Assert(aoaiConfig.APIVersion, "2024-05-01-preview")

		// 測試 Vertex.Claude 配置格式
		claudeBaseURL := `{"region":"us-east5","project_id":"aile-ai-development","base_url":"https://aile-chatbot.openai.azure.com/","api_version":"2024-05-01-preview"}`

		aoaiConfig, err = adapter.extractAOAIConfigFromBaseURL(claudeBaseURL)
		t.AssertNil(err)
		t.AssertNE(aoaiConfig, nil)
		t.Assert(aoaiConfig.BaseURL, "https://aile-chatbot.openai.azure.com/")
		t.Assert(aoaiConfig.APIVersion, "2024-05-01-preview")

		// 測試空字符串
		_, err = adapter.extractAOAIConfigFromBaseURL("")
		t.AssertNE(err, nil)

		// 測試無效 JSON
		_, err = adapter.extractAOAIConfigFromBaseURL("invalid json")
		t.AssertNE(err, nil)

		// 測試缺少 AOAI 配置的 JSON
		incompleteJSON := `{"region":"us-east5","project_id":"aile-ai-development"}`
		aoaiConfig, err = adapter.extractAOAIConfigFromBaseURL(incompleteJSON)
		t.AssertNil(err)
		t.Assert(aoaiConfig.BaseURL, "")
		t.Assert(aoaiConfig.APIVersion, "")
	})
}

// TestNewConfigStructure 測試新的配置結構
func TestNewConfigStructure(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 Gemini 新配置結構
		geminiParams := &model.LLMParams{
			LLMName:        "AileChatBot-Gemini",
			LLMType:        consts.VertexAIGemini,
			BaseUrl:        `{"region":"us-east5","project_id":"aile-ai-development","include_thoughts":false,"thinking_budget":0,"base_url":"https://aile-chatbot.openai.azure.com/","api_version":"2024-05-01-preview"}`,
			ModelId:        "gemini-2.5-flash",
			EmbeddingModel: "text-embedding-ada-002",
			Token:          "********************************",
			Temperature:    0.0,
			MaxToken:       4096,
		}

		config, err := adapter.ConvertToEmbeddingConfig(geminiParams)
		t.AssertNil(err)
		t.AssertNE(config, nil)
		// 重要：回退後 Provider 應該是 AOAI，不是 VertexAI
		t.Assert(config.Provider, consts.EmbeddingAOAI)
		t.Assert(config.Model, "text-embedding-ada-002")
		t.Assert(config.BaseURL, "https://aile-chatbot.openai.azure.com/")
		t.Assert(config.APIKey, "********************************")
		t.Assert(config.APIVersion, "2024-05-01-preview")

		// 測試 Vertex.Claude 新配置結構
		claudeParams := &model.LLMParams{
			LLMName:        "AileChatBot",
			LLMType:        consts.VertexAIClaude,
			BaseUrl:        `{"region":"us-east5","project_id":"aile-ai-development","base_url":"https://aile-chatbot.openai.azure.com/","api_version":"2024-05-01-preview"}`,
			ModelId:        "claude-sonnet-4@20250514",
			EmbeddingModel: "text-embedding-ada-002",
			Token:          "********************************",
			APIVersion:     "vertex-2023-10-16",
			Temperature:    0.0,
			MaxToken:       4096,
		}

		config, err = adapter.ConvertToEmbeddingConfig(claudeParams)
		t.AssertNil(err)
		t.AssertNE(config, nil)
		// 重要：回退後 Provider 應該是 AOAI，不是 VertexAI
		t.Assert(config.Provider, consts.EmbeddingAOAI)
		t.Assert(config.Model, "text-embedding-ada-002")
		t.Assert(config.BaseURL, "https://aile-chatbot.openai.azure.com/")
		t.Assert(config.APIKey, "********************************")
		t.Assert(config.APIVersion, "2024-05-01-preview") // 應該使用從 base_url 提取的版本
	})
}

// TestProviderFallbackConsistency 測試 Provider 字段在回退時的一致性
func TestProviderFallbackConsistency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試 Gemini 回退時 Provider 字段的變更
		geminiParams := &model.LLMParams{
			LLMName:        "test-gemini-provider",
			LLMType:        consts.VertexAIGemini,
			BaseUrl:        `{"region":"us-east5","project_id":"test-project","base_url":"https://test-aoai.openai.azure.com/","api_version":"2024-05-01-preview"}`,
			EmbeddingModel: "text-embedding-ada-002",
			Token:          "test-api-key",
		}

		// 確定提供商類型（應該是 VertexAI）
		provider, err := adapter.determineEmbeddingProvider(geminiParams)
		t.AssertNil(err)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 轉換配置（應該回退到 AOAI）
		config, err := adapter.ConvertToEmbeddingConfig(geminiParams)
		t.AssertNil(err)
		t.AssertNE(config, nil)

		// 關鍵驗證：Provider 應該從 VertexAI 變更為 AOAI
		t.Assert(config.Provider, consts.EmbeddingAOAI)

		// 測試 Vertex.Claude 回退時 Provider 字段的變更
		claudeParams := &model.LLMParams{
			LLMName:        "test-claude-provider",
			LLMType:        consts.VertexAIClaude,
			BaseUrl:        `{"region":"us-east5","project_id":"test-project","base_url":"https://test-aoai.openai.azure.com/","api_version":"2024-05-01-preview"}`,
			EmbeddingModel: "text-embedding-ada-002",
			Token:          "test-api-key",
		}

		// 確定提供商類型（應該是 VertexAI）
		provider, err = adapter.determineEmbeddingProvider(claudeParams)
		t.AssertNil(err)
		t.Assert(provider, consts.EmbeddingVertexAI)

		// 轉換配置（應該回退到 AOAI）
		config, err = adapter.ConvertToEmbeddingConfig(claudeParams)
		t.AssertNil(err)
		t.AssertNE(config, nil)

		// 關鍵驗證：Provider 應該從 VertexAI 變更為 AOAI
		t.Assert(config.Provider, consts.EmbeddingAOAI)

		// 驗證 AOAI 類型不會被改變
		aoaiParams := &model.LLMParams{
			LLMName:        "test-aoai-provider",
			LLMType:        consts.AOAI,
			BaseUrl:        "https://test-aoai.openai.azure.com/",
			EmbeddingModel: "text-embedding-ada-002",
			Token:          "test-api-key",
			APIVersion:     "2024-05-01-preview",
		}

		config, err = adapter.ConvertToEmbeddingConfig(aoaiParams)
		t.AssertNil(err)
		t.AssertNE(config, nil)

		// AOAI 類型應該保持不變
		t.Assert(config.Provider, consts.EmbeddingAOAI)
	})
}

// TestEndToEndFallbackConsistency 測試端到端回退機制的一致性
func TestEndToEndFallbackConsistency(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		adapter := NewLLMParamsAdapter()

		// 測試完整的回退流程：從 LLMParams 到最終的 embedding 實例
		geminiParams := &model.LLMParams{
			LLMName:        "test-e2e-gemini",
			LLMType:        consts.VertexAIGemini,
			BaseUrl:        `{"region":"us-east5","project_id":"test-project","base_url":"https://test-aoai.openai.azure.com/","api_version":"2024-05-01-preview"}`,
			EmbeddingModel: "text-embedding-ada-002",
			Token:          "test-api-key",
		}

		// 1. 測試配置轉換
		config, err := adapter.ConvertToEmbeddingConfig(geminiParams)
		t.AssertNil(err)
		t.AssertNE(config, nil)
		t.Assert(config.Provider, consts.EmbeddingAOAI) // 應該是 AOAI

		// 2. 測試 Factory 創建的實例
		factory := GetFactory()
		embedding, err := factory.CreateEmbedding(config.Provider)
		t.AssertNil(err)
		t.AssertNE(embedding, nil)

		// 3. 驗證實例的 Provider 標識
		t.Assert(embedding.GetProvider(), consts.EmbeddingAOAI)

		// 4. 測試完整的創建流程
		embeddingFromParams, err := adapter.CreateEmbeddingFromLLMParams(geminiParams)
		t.AssertNil(err)
		t.AssertNE(embeddingFromParams, nil)

		// 5. 驗證最終實例的 Provider 標識
		t.Assert(embeddingFromParams.GetProvider(), consts.EmbeddingAOAI)
	})
}
