package embeddings

import (
	"brainHub/internal/model/embedding"
	"context"
)

// IEmbeddings 嵌入模型統一接口
// 設計為支持多種 embedding 提供商的通用接口
type IEmbeddings interface {
	// Initialize 初始化 embedding 實例
	// 參數：
	//   - ctx: 上下文
	//   - config: embedding 配置
	// 返回：
	//   - error: 初始化錯誤
	Initialize(ctx context.Context, config *embedding.EmbeddingConfig) error

	// GenerateEmbeddings 批次生成文本向量
	// 參數：
	//   - ctx: 上下文
	//   - texts: 文本列表
	// 返回：
	//   - [][]float32: 向量列表，每個向量對應一個輸入文本
	//   - error: 處理錯誤
	GenerateEmbeddings(ctx context.Context, texts []string) ([][]float32, error)

	// GenerateSingleEmbedding 生成單個文本向量
	// 參數：
	//   - ctx: 上下文
	//   - text: 單個文本
	// 返回：
	//   - []float32: 生成的向量
	//   - error: 處理錯誤
	GenerateSingleEmbedding(ctx context.Context, text string) ([]float32, error)

	// GenerateEmbeddingsWithUsage 生成向量並返回使用統計
	// 參數：
	//   - ctx: 上下文
	//   - request: 嵌入請求
	// 返回：
	//   - *embedding.EmbeddingResponse: 完整響應，包含向量和使用統計
	//   - error: 處理錯誤
	GenerateEmbeddingsWithUsage(ctx context.Context, request *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error)

	// GetDimensions 獲取向量維度
	// 返回：
	//   - int: 向量維度
	GetDimensions() int

	// GetModelName 獲取模型名稱
	// 返回：
	//   - string: 當前使用的模型名稱
	GetModelName() string

	// GetProvider 獲取提供商名稱
	// 返回：
	//   - string: 提供商名稱
	GetProvider() string

	// GetMaxBatchSize 獲取最大批次大小
	// 返回：
	//   - int: 最大批次大小
	GetMaxBatchSize() int

	// IsHealthy 檢查服務健康狀態
	// 參數：
	//   - ctx: 上下文
	// 返回：
	//   - bool: 是否健康
	//   - error: 檢查錯誤
	IsHealthy(ctx context.Context) (bool, error)

	// GetMetrics 獲取性能指標
	// 返回：
	//   - *embedding.EmbeddingMetrics: 性能指標
	GetMetrics() *embedding.EmbeddingMetrics

	// Release 釋放資源
	// 參數：
	//   - ctx: 上下文
	Release(ctx context.Context)
}

// EmbeddingFactory 嵌入模型工廠接口
// 用於創建不同類型的 embedding 實例
type EmbeddingFactory interface {
	// CreateEmbedding 創建未初始化的 embedding 實例
	// 注意：返回的實例需要調用 Initialize() 方法才能使用
	// 參數：
	//   - provider: 提供商類型
	// 返回：
	//   - IEmbeddings: embedding 實例
	//   - error: 創建錯誤
	CreateEmbedding(provider string) (IEmbeddings, error)

	// CreateEmbeddingWithConfig 創建並初始化 embedding 實例
	// 參數：
	//   - config: embedding 配置
	// 返回：
	//   - IEmbeddings: 已初始化的 embedding 實例
	//   - error: 創建或初始化錯誤
	CreateEmbeddingWithConfig(config *embedding.EmbeddingConfig) (IEmbeddings, error)

	// GetSupportedProviders 獲取支持的提供商列表
	// 返回：
	//   - []string: 支持的提供商列表
	GetSupportedProviders() []string

	// ValidateConfig 驗證配置
	// 參數：
	//   - config: embedding 配置
	// 返回：
	//   - error: 驗證錯誤
	ValidateConfig(config *embedding.EmbeddingConfig) error
}

// BatchProcessor 批次處理器接口
// 用於優化大量文本的 embedding 生成
type BatchProcessor interface {
	// ProcessBatch 處理批次文本
	// 參數：
	//   - ctx: 上下文
	//   - texts: 文本列表
	//   - batchSize: 批次大小
	// 返回：
	//   - *embedding.BatchResult: 批次處理結果
	//   - error: 處理錯誤
	ProcessBatch(ctx context.Context, texts []string, batchSize int) (*embedding.BatchResult, error)

	// ProcessBatchAsync 異步處理批次文本
	// 參數：
	//   - ctx: 上下文
	//   - texts: 文本列表
	//   - batchSize: 批次大小
	//   - callback: 結果回調函數
	// 返回：
	//   - error: 處理錯誤
	ProcessBatchAsync(ctx context.Context, texts []string, batchSize int, callback func(*embedding.BatchResult, error)) error
}

// CacheManager 緩存管理器接口
// 用於緩存已生成的 embedding 向量
type CacheManager interface {
	// Get 獲取緩存的向量
	// 參數：
	//   - key: 緩存鍵（通常是文本的哈希值）
	// 返回：
	//   - []float32: 緩存的向量
	//   - bool: 是否存在
	Get(key string) ([]float32, bool)

	// Set 設置緩存向量
	// 參數：
	//   - key: 緩存鍵
	//   - vector: 向量
	//   - ttl: 過期時間（秒）
	Set(key string, vector []float32, ttl int)

	// Delete 刪除緩存
	// 參數：
	//   - key: 緩存鍵
	Delete(key string)

	// Clear 清空所有緩存
	Clear()

	// GetStats 獲取緩存統計
	// 返回：
	//   - map[string]interface{}: 緩存統計信息
	GetStats() map[string]interface{}
}
