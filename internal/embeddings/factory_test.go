package embeddings

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/embedding"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestNewFactory 測試工廠創建
func TestNewFactory(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory()
		t.AssertNE(factory, nil)

		defaultFactory, ok := factory.(*DefaultFactory)
		t.<PERSON><PERSON><PERSON>(ok, true)
		t.AssertNE(defaultFactory, nil)
	})
}

// TestCreateEmbedding 測試創建 embedding 實例
func TestCreateEmbedding(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory()

		// 測試支持的提供商（創建未初始化的實例）
		embeddingInstance, err := factory.CreateEmbedding(consts.EmbeddingAOAI)
		t.AssertEQ(err, nil)
		t.<PERSON>sert<PERSON>(embeddingInstance, nil)
		t.<PERSON>ser<PERSON>(embeddingInstance.GetProvider(), consts.EmbeddingAOAI)

		// 測試不支持的提供商
		_, err = factory.CreateEmbedding("unsupported")
		t.AssertNE(err, nil)
		t.AssertIN("unsupported embedding provider", err.Error())

		// 測試未實現的提供商
		_, err = factory.CreateEmbedding(consts.EmbeddingOpenAI)
		t.AssertNE(err, nil)
		t.AssertIN("not implemented yet", err.Error())

		// 測試大小寫不敏感
		embeddingInstance, err = factory.CreateEmbedding("AOAI")
		t.AssertEQ(err, nil)
		t.AssertNE(embeddingInstance, nil)

		// 測試帶空格的輸入
		embeddingInstance, err = factory.CreateEmbedding(" aoai ")
		t.AssertEQ(err, nil)
		t.AssertNE(embeddingInstance, nil)
	})
}

// TestGetSupportedProviders 測試獲取支持的提供商
func TestGetSupportedProviders(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory()
		providers := factory.GetSupportedProviders()

		t.AssertGT(len(providers), 0)
		t.AssertIN(consts.EmbeddingAOAI, providers)
		// 其他提供商暫時未實現，所以不應該在列表中
		t.AssertNI(consts.EmbeddingOpenAI, providers)
	})
}

// TestValidateConfig 測試配置驗證
func TestValidateConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory()

		// 測試 nil 配置
		err := factory.ValidateConfig(nil)
		t.AssertNE(err, nil)
		t.AssertIN("config cannot be nil", err.Error())

		// 測試無效提供商
		invalidConfig := &embedding.EmbeddingConfig{
			Provider: "invalid",
		}
		err = factory.ValidateConfig(invalidConfig)
		t.AssertNE(err, nil)

		// 測試 Azure OpenAI 配置驗證
		aoaiConfig := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingAOAI,
			Model:    "text-embedding-ada-002",
		}
		err = factory.ValidateConfig(aoaiConfig)
		t.AssertNE(err, nil) // 缺少必需字段

		// 測試完整的 Azure OpenAI 配置
		completeAoaiConfig := &embedding.EmbeddingConfig{
			Provider:   consts.EmbeddingAOAI,
			BaseURL:    "https://test.openai.azure.com",
			APIKey:     "test-key",
			APIVersion: "2023-05-15",
			Model:      "text-embedding-ada-002",
		}
		err = factory.ValidateConfig(completeAoaiConfig)
		t.AssertEQ(err, nil)

		// 驗證默認值是否被設置
		t.Assert(completeAoaiConfig.MaxBatchSize, consts.EmbeddingDefaultBatchSize)
		t.Assert(completeAoaiConfig.Timeout, consts.EmbeddingDefaultTimeout)
		t.Assert(completeAoaiConfig.MaxRetries, consts.EmbeddingMaxRetryAttempts)
		t.Assert(completeAoaiConfig.RetryDelay, consts.EmbeddingRetryDelaySecond)
	})
}

// TestValidateAOAIConfig 測試 Azure OpenAI 配置驗證
func TestValidateAOAIConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory().(*DefaultFactory)

		// 測試缺少 BaseURL
		config := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingAOAI,
			Model:    "text-embedding-ada-002",
		}
		err := factory.validateAOAIConfig(config)
		t.AssertNE(err, nil)
		t.AssertIN("BaseURL", err.Error())

		// 測試缺少 API Key
		config.BaseURL = "https://test.openai.azure.com"
		err = factory.validateAOAIConfig(config)
		t.AssertNE(err, nil)
		t.AssertIN("APIKey", err.Error())

		// 測試缺少 API Version
		config.APIKey = "test-key"
		err = factory.validateAOAIConfig(config)
		t.AssertNE(err, nil)
		t.AssertIN("APIVersion", err.Error())

		// 測試完整配置
		config.APIVersion = "2023-05-15"
		err = factory.validateAOAIConfig(config)
		t.AssertEQ(err, nil)

		// 測試不支持的模型（應該有警告但不報錯）
		config.Model = "unsupported-model"
		err = factory.validateAOAIConfig(config)
		t.AssertEQ(err, nil) // 不支持的模型只會警告，不會報錯
	})
}

// TestValidateOpenAIConfig 測試 OpenAI 配置驗證
func TestValidateOpenAIConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory().(*DefaultFactory)

		// 測試缺少 API Key
		config := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingOpenAI,
			Model:    "text-embedding-3-small",
		}
		err := factory.validateOpenAIConfig(config)
		t.AssertNE(err, nil)
		t.AssertIN("APIKey", err.Error())

		// 測試完整配置
		config.APIKey = "test-key"
		err = factory.validateOpenAIConfig(config)
		t.AssertEQ(err, nil)

		// 驗證默認 BaseURL 是否被設置
		t.Assert(config.BaseURL, "https://api.openai.com/v1")
	})
}

// TestValidateVertexAIConfig 測試 Vertex AI 配置驗證
func TestValidateVertexAIConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory().(*DefaultFactory)

		// 測試缺少 BaseURL
		config := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingVertexAI,
			Model:    "textembedding-gecko",
		}
		err := factory.validateVertexAIConfig(config)
		t.AssertNE(err, nil)
		t.AssertIN("BaseURL", err.Error())

		// 測試完整配置
		config.BaseURL = "https://us-central1-aiplatform.googleapis.com"
		err = factory.validateVertexAIConfig(config)
		t.AssertEQ(err, nil)
	})
}

// TestSetDefaultValues 測試設置默認值
func TestSetDefaultValues(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory().(*DefaultFactory)

		config := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingAOAI,
			Model:    "text-embedding-ada-002",
		}

		factory.setDefaultValues(config)

		t.Assert(config.MaxBatchSize, consts.EmbeddingDefaultBatchSize)
		t.Assert(config.Timeout, consts.EmbeddingDefaultTimeout)
		t.Assert(config.MaxRetries, consts.EmbeddingMaxRetryAttempts)
		t.Assert(config.RetryDelay, consts.EmbeddingRetryDelaySecond)
		t.Assert(config.Dimensions, 1536) // text-embedding-ada-002 的維度

		// 測試不覆蓋已設置的值
		config.MaxBatchSize = 50
		config.Timeout = 60
		factory.setDefaultValues(config)

		t.Assert(config.MaxBatchSize, 50) // 不應該被覆蓋
		t.Assert(config.Timeout, 60)      // 不應該被覆蓋
	})
}

// TestGlobalFactory 測試全局工廠
func TestGlobalFactory(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試獲取全局工廠
		factory1 := GetFactory()
		factory2 := GetFactory()
		t.Assert(factory1, factory2) // 應該是同一個實例

		// 測試設置自定義工廠
		customFactory := NewFactory()
		SetFactory(customFactory)
		factory3 := GetFactory()
		t.Assert(factory3, customFactory)

		// 測試創建便捷函數（未初始化）
		embeddingInstance, err := CreateEmbedding(consts.EmbeddingAOAI)
		t.AssertEQ(err, nil)
		t.AssertNE(embeddingInstance, nil)

		// 測試創建並初始化便捷函數
		config := &embedding.EmbeddingConfig{
			Provider:   consts.EmbeddingAOAI,
			BaseURL:    "https://test.openai.azure.com",
			APIKey:     "test-key",
			APIVersion: "2023-05-15",
			Model:      "text-embedding-ada-002",
		}
		instance, err := CreateEmbeddingWithConfig(config)
		if err != nil {
			// 預期失敗，因為會嘗試實際連接 API
			t.AssertIN("failed to initialize", err.Error())
		} else {
			// 如果成功創建，確保實例不為空並釋放資源
			t.AssertNE(instance, nil)
			instance.Release(context.Background())
		}

		// 測試配置驗證便捷函數
		err = ValidateConfig(config)
		t.AssertEQ(err, nil)
	})
}

// TestCreateEmbeddingFromLLMParams 測試從 LLMParams 創建
func TestCreateEmbeddingFromLLMParams(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 nil 參數
		_, err := CreateEmbeddingFromLLMParams(nil)
		t.AssertNE(err, nil)
		t.AssertIN("LLMParams cannot be nil", err.Error())
	})
}

// TestGetEmbeddingConfigFromLLMParams 測試從 LLMParams 獲取配置
func TestGetEmbeddingConfigFromLLMParams(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 nil 參數
		_, err := GetEmbeddingConfigFromLLMParams(nil)
		t.AssertNE(err, nil)
		t.AssertIN("invalid LLMParams type", err.Error())
	})
}

// TestCreateEmbeddingWithConfig 測試創建並初始化 embedding 實例
func TestCreateEmbeddingWithConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewFactory()

		// 測試 nil 配置
		_, err := factory.CreateEmbeddingWithConfig(nil)
		t.AssertNE(err, nil)
		t.AssertIN("config cannot be nil", err.Error())

		// 測試無效配置
		invalidConfig := &embedding.EmbeddingConfig{
			Provider: "invalid",
		}
		_, err = factory.CreateEmbeddingWithConfig(invalidConfig)
		t.AssertNE(err, nil)
		t.AssertIN("config validation failed", err.Error())

		// 測試有效配置（會失敗，因為會嘗試實際連接 API）
		validConfig := &embedding.EmbeddingConfig{
			Provider:     consts.EmbeddingAOAI,
			BaseURL:      "https://test.openai.azure.com",
			APIKey:       "test-api-key",
			APIVersion:   "2023-05-15",
			Model:        "text-embedding-ada-002",
			Dimensions:   1536,
			MaxBatchSize: 100,
			Timeout:      30,
			MaxRetries:   3,
			RetryDelay:   2,
		}
		instance, err := factory.CreateEmbeddingWithConfig(validConfig)
		if err != nil {
			// 預期失敗，因為會嘗試實際連接 API
			t.AssertIN("failed to initialize", err.Error())
		} else {
			// 如果成功創建，確保實例不為空並釋放資源
			t.AssertNE(instance, nil)
			instance.Release(context.Background())
		}
	})
}
