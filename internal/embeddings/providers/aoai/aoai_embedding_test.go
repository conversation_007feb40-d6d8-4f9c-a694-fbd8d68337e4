package aoai

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/embedding"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestNew 測試創建新實例
func TestNew(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()
		t.AssertNE(embeddingInstance, nil)

		aoaiEmbedding, ok := embeddingInstance.(*AoAiEmbedding)
		t.Assert(ok, true)
		t.AssertNE(aoaiEmbedding.metrics, nil)
		t.Assert(aoaiEmbedding.initialized, false)
	})
}

// TestInitialize 測試初始化
func TestInitialize(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()
		ctx := context.Background()

		// 測試無效配置
		err := embeddingInstance.Initialize(ctx, nil)
		t.AssertNE(err, nil)
		t.AssertIN("config cannot be nil", err.Error())

		// 測試無效提供商
		invalidConfig := &embedding.EmbeddingConfig{
			Provider: "invalid",
		}
		err = embeddingInstance.Initialize(ctx, invalidConfig)
		t.AssertNE(err, nil)
		t.AssertIN("invalid provider", err.Error())

		// 測試缺少必需字段
		incompleteConfig := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingAOAI,
		}
		err = embeddingInstance.Initialize(ctx, incompleteConfig)
		t.AssertNE(err, nil)

		// 測試重複初始化（使用模擬配置）
		validConfig := createMockConfig()
		// 注意：這裡會失敗，因為沒有真實的 API 密鑰，但我們可以測試配置驗證
		err = embeddingInstance.Initialize(ctx, validConfig)
		// 在測試環境中，這可能會因為網絡連接失敗，但配置驗證應該通過
		if err != nil {
			t.AssertIN("Failed to create", err.Error()) // 預期的網絡錯誤
		}
	})
}

// TestGetters 測試 getter 方法
func TestGetters(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New().(*AoAiEmbedding)

		// 設置測試數據
		embeddingInstance.modelName = "test-model"
		embeddingInstance.dimensions = 1536
		embeddingInstance.maxBatchSize = 100

		t.Assert(embeddingInstance.GetModelName(), "test-model")
		t.Assert(embeddingInstance.GetDimensions(), 1536)
		t.Assert(embeddingInstance.GetMaxBatchSize(), 100)
		t.Assert(embeddingInstance.GetProvider(), consts.EmbeddingAOAI)
	})
}

// TestGenerateEmbeddings 測試生成向量（未初始化）
func TestGenerateEmbeddings(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()
		ctx := context.Background()

		// 測試未初始化的實例
		_, err := embeddingInstance.GenerateEmbeddings(ctx, []string{"test"})
		t.AssertNE(err, nil)
		t.AssertIN("not initialized", err.Error())

		// 測試空文本列表
		_, err = embeddingInstance.GenerateEmbeddings(ctx, []string{})
		t.AssertNE(err, nil)
		t.AssertIN("not initialized", err.Error())
	})
}

// TestGenerateSingleEmbedding 測試生成單個向量（未初始化）
func TestGenerateSingleEmbedding(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()
		ctx := context.Background()

		// 測試未初始化的實例
		_, err := embeddingInstance.GenerateSingleEmbedding(ctx, "test")
		t.AssertNE(err, nil)
		t.AssertIN("not initialized", err.Error())

		// 測試空文本
		_, err = embeddingInstance.GenerateSingleEmbedding(ctx, "")
		t.AssertNE(err, nil)
		t.AssertIN("not initialized", err.Error())
	})
}

// TestGenerateEmbeddingsWithUsage 測試帶使用統計的向量生成
func TestGenerateEmbeddingsWithUsage(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()
		ctx := context.Background()

		// 測試未初始化的實例
		_, err := embeddingInstance.GenerateEmbeddingsWithUsage(ctx, nil)
		t.AssertNE(err, nil)
		t.AssertIN("not initialized", err.Error())

		// 測試 nil 請求
		_, err = embeddingInstance.GenerateEmbeddingsWithUsage(ctx, nil)
		t.AssertNE(err, nil)
	})
}

// TestIsHealthy 測試健康檢查
func TestIsHealthy(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()
		ctx := context.Background()

		// 測試未初始化的實例
		healthy, err := embeddingInstance.IsHealthy(ctx)
		t.Assert(healthy, false)
		t.AssertNE(err, nil)
		t.AssertIN("not initialized", err.Error())
	})
}

// TestGetMetrics 測試獲取指標
func TestGetMetrics(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New()

		metrics := embeddingInstance.GetMetrics()
		t.AssertNE(metrics, nil)
		t.Assert(metrics.RequestCount, 0)
		t.Assert(metrics.SuccessCount, 0)
		t.Assert(metrics.ErrorCount, 0)
	})
}

// TestRelease 測試資源釋放
func TestRelease(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New().(*AoAiEmbedding)
		ctx := context.Background()

		// 測試釋放未初始化的實例
		embeddingInstance.Release(ctx)
		t.Assert(embeddingInstance.initialized, false)

		// 模擬已初始化狀態
		embeddingInstance.initialized = true
		embeddingInstance.Release(ctx)
		t.Assert(embeddingInstance.initialized, false)
	})
}

// TestValidateConfig 測試配置驗證
func TestValidateConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New().(*AoAiEmbedding)

		// 測試 nil 配置
		err := embeddingInstance.validateConfig(nil)
		t.AssertNE(err, nil)
		t.AssertIN("config cannot be nil", err.Error())

		// 測試無效提供商
		invalidConfig := &embedding.EmbeddingConfig{
			Provider: "invalid",
		}
		err = embeddingInstance.validateConfig(invalidConfig)
		t.AssertNE(err, nil)
		t.AssertIN("invalid provider", err.Error())

		// 測試缺少 BaseURL
		incompleteConfig := &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingAOAI,
		}
		err = embeddingInstance.validateConfig(incompleteConfig)
		t.AssertNE(err, nil)
		t.AssertIN("base URL is required", err.Error())

		// 測試完整配置
		validConfig := createMockConfig()
		err = embeddingInstance.validateConfig(validConfig)
		t.AssertEQ(err, nil)
	})
}

// TestUpdateMetrics 測試指標更新
func TestUpdateMetrics(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New().(*AoAiEmbedding)

		// 測試成功指標更新
		embeddingInstance.updateMetricsOnSuccess(5, 1000)
		metrics := embeddingInstance.GetMetrics()
		t.Assert(metrics.RequestCount, 1)
		t.Assert(metrics.SuccessCount, 1)
		t.Assert(metrics.ErrorCount, 0)
		t.Assert(metrics.AverageLatency, 1000.0)

		// 測試錯誤指標更新
		embeddingInstance.updateMetricsOnError()
		metrics = embeddingInstance.GetMetrics()
		t.Assert(metrics.RequestCount, 2)
		t.Assert(metrics.SuccessCount, 1)
		t.Assert(metrics.ErrorCount, 1)
	})
}

// TestSplitTextsByBatchSize 測試批次分割
func TestSplitTextsByBatchSize(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New().(*AoAiEmbedding)
		embeddingInstance.maxBatchSize = 3

		texts := []string{"text1", "text2", "text3", "text4", "text5"}

		// 測試正常批次大小
		batches := embeddingInstance.splitTextsByBatchSize(texts, 2)
		t.Assert(len(batches), 3)
		t.Assert(len(batches[0]), 2)
		t.Assert(len(batches[1]), 2)
		t.Assert(len(batches[2]), 1)

		// 測試使用默認批次大小
		batches = embeddingInstance.splitTextsByBatchSize(texts, 0)
		t.Assert(len(batches), 2) // 5 texts / 3 max batch size = 2 batches
	})
}

// TestGenerateCacheKey 測試緩存鍵生成
func TestGenerateCacheKey(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		embeddingInstance := New().(*AoAiEmbedding)
		embeddingInstance.modelName = "test-model"
		embeddingInstance.config = &embedding.EmbeddingConfig{
			Provider: consts.EmbeddingAOAI,
		}

		key1 := embeddingInstance.generateCacheKey("test text")
		key2 := embeddingInstance.generateCacheKey("test text")
		key3 := embeddingInstance.generateCacheKey("different text")

		// 相同文本應該生成相同的鍵
		t.Assert(key1, key2)
		// 不同文本應該生成不同的鍵
		t.AssertNE(key1, key3)
		// 鍵應該包含前綴
		t.AssertIN(consts.EmbeddingCacheKeyPrefix, key1)
	})
}

// createMockConfig 創建模擬配置用於測試
func createMockConfig() *embedding.EmbeddingConfig {
	return &embedding.EmbeddingConfig{
		Provider:     consts.EmbeddingAOAI,
		BaseURL:      "https://test.openai.azure.com",
		APIKey:       "test-api-key",
		APIVersion:   "2023-05-15",
		Model:        "text-embedding-ada-002",
		Dimensions:   1536,
		MaxBatchSize: 100,
		Timeout:      30,
		MaxRetries:   3,
		RetryDelay:   2,
	}
}
