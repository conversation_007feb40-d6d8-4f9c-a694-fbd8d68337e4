package aoai

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/embedding"
	"context"
	"crypto/md5"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	langchainembeddings "github.com/tmc/langchaingo/embeddings"
	"github.com/tmc/langchaingo/llms/openai"
)

// IEmbeddings 嵌入模型統一接口（避免循環導入）
type IEmbeddings interface {
	Initialize(ctx context.Context, config *embedding.EmbeddingConfig) error
	GenerateEmbeddings(ctx context.Context, texts []string) ([][]float32, error)
	GenerateSingleEmbedding(ctx context.Context, text string) ([]float32, error)
	GenerateEmbeddingsWithUsage(ctx context.Context, request *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error)
	GetDimensions() int
	GetModelName() string
	GetProvider() string
	GetMaxBatchSize() int
	IsHealthy(ctx context.Context) (bool, error)
	GetMetrics() *embedding.EmbeddingMetrics
	Release(ctx context.Context)
}

// AoAiEmbedding Azure OpenAI Embedding 服務實現
// 提供安全、高效能的文本向量化功能
type AoAiEmbedding struct {
	embedder     langchainembeddings.Embedder // langchaingo embedder 實例
	llm          *openai.LLM                  // OpenAI LLM 實例
	config       *embedding.EmbeddingConfig   // 配置信息
	modelName    string                       // 模型名稱
	dimensions   int                          // 向量維度
	maxBatchSize int                          // 最大批次大小
	metrics      *embedding.EmbeddingMetrics  // 性能指標
	metricsMutex sync.RWMutex                 // 保護 metrics 的讀寫鎖
	initialized  bool                         // 初始化狀態
	initMutex    sync.RWMutex                 // 保護初始化狀態的讀寫鎖
}

// New 創建新的 Azure OpenAI Embedding 實例
func New() IEmbeddings {
	return &AoAiEmbedding{
		metrics: &embedding.EmbeddingMetrics{},
	}
}

// logger 返回專用的日誌記錄器
func (a *AoAiEmbedding) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogEmbedding)
}

// Initialize 初始化 embedding 實例
func (a *AoAiEmbedding) Initialize(ctx context.Context, config *embedding.EmbeddingConfig) error {
	a.initMutex.Lock()
	defer a.initMutex.Unlock()

	if a.initialized {
		a.logger().Warning(ctx, "AoAi embedding already initialized")
		return nil
	}

	a.logger().Info(ctx, "Initializing AoAi embedding service")

	// 驗證配置
	if err := a.validateConfig(config); err != nil {
		a.logger().Error(ctx, "Config validation failed:", err)
		return gerror.Wrap(err, "config validation failed")
	}

	// 保存配置
	a.config = config
	a.modelName = config.Model
	a.dimensions = config.Dimensions
	a.maxBatchSize = config.MaxBatchSize

	// 創建 OpenAI LLM 實例
	var err error
	a.llm, err = openai.New(
		openai.WithBaseURL(config.BaseURL),
		openai.WithAPIType(openai.APITypeAzure),
		openai.WithAPIVersion(config.APIVersion),
		openai.WithModel(config.Model),
		openai.WithToken(config.APIKey),
		openai.WithEmbeddingModel(config.Model),
	)
	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to create OpenAI LLM instance")
		a.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	// 創建 embedder
	a.embedder, err = langchainembeddings.NewEmbedder(a.llm)
	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to create embedder")
		a.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	a.initialized = true
	a.logger().Info(ctx, "AoAi embedding service initialized successfully")

	// 記錄配置信息（安全方式）
	a.logConfigSafely(ctx, config)

	return nil
}

// validateConfig 驗證配置
func (a *AoAiEmbedding) validateConfig(config *embedding.EmbeddingConfig) error {
	if config == nil {
		return gerror.New("config cannot be nil")
	}

	if config.Provider != consts.EmbeddingAOAI {
		return gerror.Newf("invalid provider: expected %s, got %s", consts.EmbeddingAOAI, config.Provider)
	}

	if config.BaseURL == "" {
		return gerror.New("base URL is required")
	}

	if config.APIKey == "" {
		return gerror.New("API key is required")
	}

	if config.APIVersion == "" {
		return gerror.New("API version is required")
	}

	if config.Model == "" {
		return gerror.New("model is required")
	}

	return nil
}

// logConfigSafely 安全地記錄配置資訊，避免洩露敏感資料
func (a *AoAiEmbedding) logConfigSafely(ctx context.Context, config *embedding.EmbeddingConfig) {
	safeConfig := *config
	if safeConfig.APIKey != "" {
		safeConfig.APIKey = "***masked***"
	}

	a.logger().Infof(ctx, "AoAi embedding config: Provider=%s, Model=%s, Dimensions=%d, MaxBatchSize=%d, BaseURL=%s",
		safeConfig.Provider, safeConfig.Model, safeConfig.Dimensions, safeConfig.MaxBatchSize, safeConfig.BaseURL)
}

// GenerateEmbeddings 批次生成文本向量
func (a *AoAiEmbedding) GenerateEmbeddings(ctx context.Context, texts []string) ([][]float32, error) {
	if !a.isInitialized() {
		return nil, gerror.New("embedding service not initialized")
	}

	if len(texts) == 0 {
		return [][]float32{}, nil
	}

	startTime := time.Now()
	a.logger().Debugf(ctx, "Generating embeddings for %d texts", len(texts))

	// 驗證文本長度
	for i, text := range texts {
		if len(text) > consts.EmbeddingMaxTextLength {
			return nil, gerror.Newf("text %d exceeds maximum length %d", i, consts.EmbeddingMaxTextLength)
		}
	}

	// 使用 langchaingo 生成 embeddings
	embeddings, err := a.embedder.EmbedDocuments(ctx, texts)
	if err != nil {
		a.updateMetricsOnError()
		wrappedErr := gerror.Wrap(err, "failed to generate embeddings")
		a.logger().Error(ctx, wrappedErr)
		return nil, wrappedErr
	}

	// 轉換為 float32 格式
	result := make([][]float32, len(embeddings))
	for i, emb := range embeddings {
		result[i] = make([]float32, len(emb))
		for j, val := range emb {
			result[i][j] = float32(val)
		}
	}

	// 更新性能指標
	processingTime := time.Since(startTime).Milliseconds()
	a.updateMetricsOnSuccess(len(texts), processingTime)

	a.logger().Debugf(ctx, "Generated embeddings for %d texts in %dms", len(texts), processingTime)
	return result, nil
}

// GenerateSingleEmbedding 生成單個文本向量
func (a *AoAiEmbedding) GenerateSingleEmbedding(ctx context.Context, text string) ([]float32, error) {
	if !a.isInitialized() {
		return nil, gerror.New("embedding service not initialized")
	}

	if text == "" {
		return nil, gerror.New("text cannot be empty")
	}

	startTime := time.Now()
	a.logger().Debugf(ctx, "Generating single embedding for text length: %d", len(text))

	// 驗證文本長度
	if len(text) > consts.EmbeddingMaxTextLength {
		return nil, gerror.Newf("text exceeds maximum length %d", consts.EmbeddingMaxTextLength)
	}

	// 使用 langchaingo 生成 embedding
	embedding, err := a.embedder.EmbedQuery(ctx, text)
	if err != nil {
		a.updateMetricsOnError()
		wrappedErr := gerror.Wrap(err, "failed to generate single embedding")
		a.logger().Error(ctx, wrappedErr)
		return nil, wrappedErr
	}

	// 轉換為 float32 格式
	result := make([]float32, len(embedding))
	for i, val := range embedding {
		result[i] = float32(val)
	}

	// 更新性能指標
	processingTime := time.Since(startTime).Milliseconds()
	a.updateMetricsOnSuccess(1, processingTime)

	a.logger().Debugf(ctx, "Generated single embedding in %dms", processingTime)
	return result, nil
}

// GenerateEmbeddingsWithUsage 生成向量並返回使用統計
func (a *AoAiEmbedding) GenerateEmbeddingsWithUsage(ctx context.Context, request *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error) {
	if !a.isInitialized() {
		return nil, gerror.New("embedding service not initialized")
	}

	if request == nil {
		return nil, gerror.New("request cannot be nil")
	}

	startTime := time.Now()

	// 生成 embeddings
	embeddings, err := a.GenerateEmbeddings(ctx, request.Texts)
	if err != nil {
		return nil, err
	}

	processingTime := time.Since(startTime).Milliseconds()

	// 計算 token 使用量（簡化計算）
	totalTokens := 0
	for _, text := range request.Texts {
		// 簡化的 token 計算：大約每 4 個字符 = 1 token
		totalTokens += len(text) / 4
	}

	response := &embedding.EmbeddingResponse{
		Embeddings:  embeddings,
		Model:       a.modelName,
		ProcessTime: processingTime,
		Provider:    consts.EmbeddingAOAI,
		Usage: embedding.Usage{
			PromptTokens: totalTokens,
			TotalTokens:  totalTokens,
		},
	}

	return response, nil
}

// isInitialized 檢查是否已初始化
func (a *AoAiEmbedding) isInitialized() bool {
	a.initMutex.RLock()
	defer a.initMutex.RUnlock()
	return a.initialized
}

// GetDimensions 獲取向量維度
func (a *AoAiEmbedding) GetDimensions() int {
	return a.dimensions
}

// GetModelName 獲取模型名稱
func (a *AoAiEmbedding) GetModelName() string {
	return a.modelName
}

// GetProvider 獲取提供商名稱
func (a *AoAiEmbedding) GetProvider() string {
	return consts.EmbeddingAOAI
}

// GetMaxBatchSize 獲取最大批次大小
func (a *AoAiEmbedding) GetMaxBatchSize() int {
	return a.maxBatchSize
}

// IsHealthy 檢查服務健康狀態
func (a *AoAiEmbedding) IsHealthy(ctx context.Context) (bool, error) {
	if !a.isInitialized() {
		return false, gerror.New("service not initialized")
	}

	// 執行簡單的健康檢查：生成一個測試 embedding
	testText := "health check"
	_, err := a.GenerateSingleEmbedding(ctx, testText)
	if err != nil {
		a.logger().Warningf(ctx, "Health check failed: %v", err)
		return false, err
	}

	return true, nil
}

// GetMetrics 獲取性能指標
func (a *AoAiEmbedding) GetMetrics() *embedding.EmbeddingMetrics {
	a.metricsMutex.RLock()
	defer a.metricsMutex.RUnlock()

	// 返回指標的副本
	metricsCopy := *a.metrics
	return &metricsCopy
}

// Release 釋放資源
func (a *AoAiEmbedding) Release(ctx context.Context) {
	a.initMutex.Lock()
	defer a.initMutex.Unlock()

	if !a.initialized {
		return
	}

	a.logger().Info(ctx, "Releasing AoAi embedding service resources")

	// 清理資源
	a.embedder = nil
	a.llm = nil
	a.config = nil
	a.initialized = false

	a.logger().Info(ctx, "AoAi embedding service resources released")
}

// updateMetricsOnSuccess 更新成功指標
func (a *AoAiEmbedding) updateMetricsOnSuccess(textCount int, processingTime int64) {
	a.metricsMutex.Lock()
	defer a.metricsMutex.Unlock()

	a.metrics.RequestCount++
	a.metrics.SuccessCount++
	a.metrics.LastRequestTime = time.Now().Unix()

	// 更新平均延遲
	if a.metrics.RequestCount == 1 {
		a.metrics.AverageLatency = float64(processingTime)
	} else {
		a.metrics.AverageLatency = (a.metrics.AverageLatency*float64(a.metrics.RequestCount-1) + float64(processingTime)) / float64(a.metrics.RequestCount)
	}

	// 簡化的 token 計算
	estimatedTokens := int64(textCount * 100) // 假設每個文本平均 100 tokens
	a.metrics.TotalTokens += estimatedTokens
}

// updateMetricsOnError 更新錯誤指標
func (a *AoAiEmbedding) updateMetricsOnError() {
	a.metricsMutex.Lock()
	defer a.metricsMutex.Unlock()

	a.metrics.RequestCount++
	a.metrics.ErrorCount++
	a.metrics.LastRequestTime = time.Now().Unix()
}

// generateCacheKey 生成緩存鍵
func (a *AoAiEmbedding) generateCacheKey(text string) string {
	hash := md5.Sum([]byte(fmt.Sprintf("%s:%s:%s", a.modelName, a.config.Provider, text)))
	return fmt.Sprintf("%s%x", consts.EmbeddingCacheKeyPrefix, hash)
}

// splitTextsByBatchSize 按批次大小分割文本
func (a *AoAiEmbedding) splitTextsByBatchSize(texts []string, batchSize int) [][]string {
	if batchSize <= 0 {
		batchSize = a.maxBatchSize
	}

	var batches [][]string
	for i := 0; i < len(texts); i += batchSize {
		end := i + batchSize
		if end > len(texts) {
			end = len(texts)
		}
		batches = append(batches, texts[i:end])
	}

	return batches
}

// retryWithBackoff 帶退避的重試機制
func (a *AoAiEmbedding) retryWithBackoff(ctx context.Context, operation func() error) error {
	var lastErr error

	for attempt := 1; attempt <= a.config.MaxRetries; attempt++ {
		lastErr = operation()
		if lastErr == nil {
			return nil
		}

		a.logger().Infof(ctx, "Attempt %d/%d failed: %v", attempt, a.config.MaxRetries, lastErr)

		if attempt < a.config.MaxRetries {
			delay := time.Duration(a.config.RetryDelay) * time.Second
			a.logger().Debugf(ctx, "Retrying in %v...", delay)
			time.Sleep(delay)
		}
	}

	return gerror.Wrapf(lastErr, "operation failed after %d attempts", a.config.MaxRetries)
}
