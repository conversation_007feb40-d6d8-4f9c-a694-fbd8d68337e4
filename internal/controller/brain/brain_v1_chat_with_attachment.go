package brain

import (
	"brainHub/api/brain/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"brainHub/internal/service"
	"brainHub/utility"
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
)

func (c *ControllerV1) ChatWithAttachment(ctx context.Context, req *v1.ChatWithAttachmentReq) (res *v1.ChatWithAttachmentRes, err error) {
	res = &v1.ChatWithAttachmentRes{}
	r := ghttp.RequestFromCtx(ctx)
	fnProcessError := func(e error) {
		if e != nil {
			code := gerror.Code(e)
			res.Code = code.Code()
			if code == gcode.CodeNil {
				res.Message = e.Error()
			} else {
				res.Message = code.Message()
			}
		}

	}
	req.Channel = consts.ChLine
	userMessage := utility.ConvertAnyToGenericMessage(req)

	var resp = &llm.ResponseData{
		TenantID:  req.TenantID,
		UserID:    req.UserID,
		ServiceID: req.ServiceID,
		Channel:   req.Channel,
	}

	ai, err := service.AiRouter().Select(ctx, &model.AiSelectorInput{
		TenantID:  req.TenantID,
		ServiceID: req.ServiceID,
		UserID:    req.UserID,
		Channel:   consts.ChLine,
	})

	if err != nil {
		fnProcessError(err)

	} else {

		pathName := gfile.Temp("brainHub")
		fileName, e := req.Attachment.Save(pathName)

		if e != nil {
			fnProcessError(e)
			resp.Response = e.Error()
		} else {
			fullFileName := gfile.Join(pathName, fileName)
			//  remove the temporary file after processing
			defer func() {
				_ = gfile.RemoveFile(fullFileName)
			}()

			contentData := gfile.GetBytes(fullFileName)
			resp, err = ai.Chat(
				ctx,
				&llm.Message{
					ContentType: consts.ContentMediaFile,
					Content:     contentData,
					MimeType:    req.Mime,
				},
			)

			if err != nil {
				fnProcessError(err)
				resp.Response = err.Error()
			} else {
				res.Code = consts.Success.Code()
				res.Message = consts.Success.Message()

				resp.TenantID = req.TenantID
				resp.ServiceID = req.ServiceID
				resp.UserID = req.UserID
				resp.Channel = req.Channel

				res.ResponseData = resp

			}
		}

	}
	aiMessage := utility.ConvertAnyToGenericMessage(resp)
	if userMessage != nil {
		_ = service.DSH().InsertNewChatMessage(ctx, userMessage)
	}
	if aiMessage != nil {
		_ = service.DSH().InsertNewChatMessage(ctx, aiMessage)
	}

	r.Response.WriteJsonExit(res)
	return
}
