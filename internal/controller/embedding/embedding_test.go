package embedding

import (
	"brainHub/api/embedding/v1"
	"brainHub/internal/consts"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestControllerV1_GenerateEmbeddings 測試生成向量接口
func TestControllerV1_GenerateEmbeddings(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV1()
		ctx := context.Background()

		// 測試無效參數
		req := &v1.GenerateEmbeddingsReq{}
		res, err := controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code()) // 應該返回錯誤

		// 測試缺少租戶ID
		req = &v1.GenerateEmbeddingsReq{
			ServiceID: "test_service",
			Texts:     []string{"test text"},
		}
		res, err = controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.Assert<PERSON>(res.Code, consts.Success.Code())
		// GoFrame 返回的是通用錯誤消息
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試缺少服務ID
		req = &v1.GenerateEmbeddingsReq{
			TenantID: "test_tenant",
			Texts:    []string{"test text"},
		}
		res, err = controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試空文本列表
		req = &v1.GenerateEmbeddingsReq{
			TenantID:  "test_tenant",
			ServiceID: "test_service",
			Texts:     []string{},
		}
		res, err = controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試文本過長
		longText := make([]byte, consts.EmbeddingMaxTextLength+1)
		for i := range longText {
			longText[i] = 'a'
		}
		req = &v1.GenerateEmbeddingsReq{
			TenantID:  "test_tenant",
			ServiceID: "test_service",
			Texts:     []string{string(longText)},
		}
		res, err = controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試文本數量過多
		manyTexts := make([]string, consts.EmbeddingDefaultBatchSize+1)
		for i := range manyTexts {
			manyTexts[i] = "test text"
		}
		req = &v1.GenerateEmbeddingsReq{
			TenantID:  "test_tenant",
			ServiceID: "test_service",
			Texts:     manyTexts,
		}
		res, err = controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試包含空文本
		req = &v1.GenerateEmbeddingsReq{
			TenantID:  "test_tenant",
			ServiceID: "test_service",
			Texts:     []string{"valid text", ""},
		}
		res, err = controller.GenerateEmbeddings(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)
	})
}

// TestControllerV1_GetEmbeddingHealth 測試健康檢查接口
func TestControllerV1_GetEmbeddingHealth(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV1()
		ctx := context.Background()

		// 測試無效參數
		req := &v1.GetEmbeddingHealthReq{}
		res, err := controller.GetEmbeddingHealth(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code()) // 應該返回錯誤

		// 測試缺少租戶ID
		req = &v1.GetEmbeddingHealthReq{
			ServiceID: "test_service",
		}
		res, err = controller.GetEmbeddingHealth(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試缺少服務ID
		req = &v1.GetEmbeddingHealthReq{
			TenantID: "test_tenant",
		}
		res, err = controller.GetEmbeddingHealth(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)
	})
}

// TestControllerV1_GetEmbeddingMetrics 測試性能指標接口
func TestControllerV1_GetEmbeddingMetrics(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV1()
		ctx := context.Background()

		// 測試無效參數
		req := &v1.GetEmbeddingMetricsReq{}
		res, err := controller.GetEmbeddingMetrics(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code()) // 應該返回錯誤

		// 測試缺少租戶ID
		req = &v1.GetEmbeddingMetricsReq{
			ServiceID: "test_service",
		}
		res, err = controller.GetEmbeddingMetrics(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)

		// 測試缺少服務ID
		req = &v1.GetEmbeddingMetricsReq{
			TenantID: "test_tenant",
		}
		res, err = controller.GetEmbeddingMetrics(ctx, req)
		t.AssertEQ(err, nil)
		t.AssertNE(res.Code, consts.Success.Code())
		t.AssertIN("Invalid Parameter", res.Message)
	})
}

// TestNewV1 測試控制器創建
func TestNewV1(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV1()
		t.AssertNE(controller, nil)

		// 驗證類型
		_, ok := controller.(*ControllerV1)
		t.Assert(ok, true)
	})
}

// BenchmarkGenerateEmbeddings 性能測試
func BenchmarkGenerateEmbeddings(b *testing.B) {
	controller := NewV1()
	ctx := context.Background()

	req := &v1.GenerateEmbeddingsReq{
		TenantID:  "test_tenant",
		ServiceID: "test_service",
		Texts:     []string{"benchmark test text"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = controller.GenerateEmbeddings(ctx, req)
	}
}

// BenchmarkGetEmbeddingHealth 健康檢查性能測試
func BenchmarkGetEmbeddingHealth(b *testing.B) {
	controller := NewV1()
	ctx := context.Background()

	req := &v1.GetEmbeddingHealthReq{
		TenantID:  "test_tenant",
		ServiceID: "test_service",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = controller.GetEmbeddingHealth(ctx, req)
	}
}

// BenchmarkGetEmbeddingMetrics 性能指標性能測試
func BenchmarkGetEmbeddingMetrics(b *testing.B) {
	controller := NewV1()
	ctx := context.Background()

	req := &v1.GetEmbeddingMetricsReq{
		TenantID:  "test_tenant",
		ServiceID: "test_service",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = controller.GetEmbeddingMetrics(ctx, req)
	}
}
