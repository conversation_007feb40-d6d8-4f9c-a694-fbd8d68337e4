package embedding

import (
	"brainHub/api/embedding/v1"
	"brainHub/internal/consts"
	"brainHub/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// GetEmbeddingMetrics 獲取 embedding 服務性能指標
func (c *ControllerV1) GetEmbeddingMetrics(ctx context.Context, req *v1.GetEmbeddingMetricsReq) (res *v1.GetEmbeddingMetricsRes, err error) {
	res = &v1.GetEmbeddingMetricsRes{}
	r := ghttp.RequestFromCtx(ctx)

	// 錯誤處理函數
	fnProcessError := func(e error) {
		if e != nil {
			code := gerror.Code(e)
			res.Code = code.Code()
			if code == gcode.CodeNil {
				res.Message = e.Error()
			} else {
				res.Message = code.Message()
			}

			// Log error
			g.Log().Cat(consts.CatalogEmbedding).Errorf(ctx,
				"GetEmbeddingMetrics failed: tenant_id=%s, service_id=%s, error=%v",
				req.TenantID, req.ServiceID, e)
		}
	}

	// Parameter validation
	if req.TenantID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "tenant_id cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	if req.ServiceID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "service_id cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	// Log request
	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"GetEmbeddingMetrics request: tenant_id=%s, service_id=%s",
		req.TenantID, req.ServiceID)

	// Get embedding service metrics
	metrics, err := service.Embedding().GetEmbeddingMetrics(ctx, req.TenantID, req.ServiceID)
	if err != nil {
		fnProcessError(err)
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	// Build response data
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.Data = metrics

	// Log success
	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"GetEmbeddingMetrics success: tenant_id=%s, service_id=%s, request_count=%d, success_count=%d, error_count=%d, avg_latency=%.2f",
		req.TenantID, req.ServiceID, metrics.RequestCount, metrics.SuccessCount, metrics.ErrorCount, metrics.AverageLatency)

	if r != nil {
		r.Response.WriteJsonExit(res)
	}
	return
}
