package model

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
)

type MQMessage struct {
	Schema          string  `json:"schema"`
	Table           string  `json:"table"`
	WhereConditions string  `json:"where_conditions"`
	WhereParams     g.Slice `json:"where_params"`
	// data to be written
	Data any `json:"data"`
}

func (m *MQMessage) String() string {
	return gjson.New(m).MustToJsonIndentString()
}
