package omnichannel

// Root 主要回答結構體，支援 Text 和 Carousel 類型
type Root struct {
	QuickReply *QuickReply `json:"quickReply,omitempty"`
	Text       string      `json:"text"`
	Type       string      `json:"type"`
	Elements   []*Element  `json:"elements,omitempty"` // 新增：支援 Carousel 元素
}

// QuickReply 快速回覆結構體
type QuickReply struct {
	Items []*Item `json:"items"`
}

// Item 快速回覆項目
type Item struct {
	Action *Action `json:"action"`
	Type   string  `json:"type"`
}

// Action 動作結構體，支援 Postback 和 Url 類型
type Action struct {
	Data        string `json:"data,omitempty"`
	DisplayText string `json:"displayText,omitempty"`
	Title       string `json:"title"`
	Type        string `json:"type"`
	Url         string `json:"url,omitempty"` // 新增：支援 Url 類型動作
}

// Element Carousel 元素結構體
type Element struct {
	Title    string    `json:"title"`    // 標題
	Subtitle string    `json:"subtitle"` // 副標題
	ImageUrl string    `json:"imageUrl"` // 圖片網址
	Actions  []*Action `json:"actions"`  // 動作陣列
}

// CarouselInput Carousel 類型輸入結構
type CarouselInput struct {
	Text     string             `json:"text"`     // 描述文字
	Elements []*CarouselElement `json:"elements"` // Carousel 元素陣列
}

// CarouselElement Carousel 元素輸入結構
type CarouselElement struct {
	Title    string            `json:"title"`    // 標題
	Subtitle string            `json:"subtitle"` // 副標題
	ImageUrl string            `json:"imageUrl"` // 圖片網址
	Actions  []*CarouselAction `json:"actions"`  // 動作陣列
}

// CarouselAction Carousel 動作輸入結構
type CarouselAction struct {
	Type        string `json:"type"`                  // 動作類型："Postback" 或 "Url"
	Title       string `json:"title"`                 // 動作標題
	Data        string `json:"data,omitempty"`        // Postback 類型使用
	DisplayText string `json:"displayText,omitempty"` // Postback 類型使用
	Url         string `json:"url,omitempty"`         // Url 類型使用
}
