package llm

type Message struct {
	Content     any    `json:"content"`
	ContentType string `json:"content_type"`
	MimeType    string `json:"mime_type"`
}

// GenerateContentRequest 統一的生成內容請求結構
// 適用於所有 LLM 模型（Gemini、AoAi、Claude）
type GenerateContentRequest struct {
	Prompt            string   `json:"prompt"`                       // 必填：輸入提示詞
	MaxContinuations  int      `json:"max_continuations"`            // 最大續寫次數，預設 3
	TotalTokenBudget  int32    `json:"total_token_budget"`           // 總體 token 預算限制
	IncludeThinking   bool     `json:"include_thinking"`             // 是否保留思考過程（僅 Gemini 支援）
	Temperature       *float32 `json:"temperature,omitempty"`        // 可選：覆蓋預設溫度
	SystemInstruction string   `json:"system_instruction,omitempty"` // 可選：系統指令
	StopSequences     []string `json:"stop_sequences,omitempty"`     // 可選：停止序列
}

// GenerateContentResponse 統一的生成內容響應結構
// 對應資料庫表結構和跨模型需求
type GenerateContentResponse struct {
	LLMName           string   `json:"llm_name"`                   // 對應 DB: llm_name - 使用的模型名稱
	InputContent      string   `json:"input_content"`              // 對應 DB: input_content - 原始輸入內容
	OutputContent     string   `json:"output_content"`             // 對應 DB: output_content - 最終生成內容
	InputTokens       int32    `json:"input_tokens"`               // 對應 DB: input_tokens - 輸入 token 數量
	OutputTokens      int32    `json:"output_tokens"`              // 對應 DB: output_tokens - 輸出 token 數量
	TotalTokens       int32    `json:"total_tokens"`               // 總 token 數量（input + output）
	ContinuationCount int      `json:"continuation_count"`         // 實際續寫次數
	IsComplete        bool     `json:"is_complete"`                // 內容是否完整生成
	ThinkingProcess   string   `json:"thinking_process,omitempty"` // 思考過程（Gemini 專用）
	SafetyWarnings    []string `json:"safety_warnings,omitempty"`  // 安全過濾警告
	FinishReason      string   `json:"finish_reason,omitempty"`    // 完成原因
	GenerationTime    int64    `json:"generation_time"`            // 生成耗時（毫秒）
}
