package model

import "github.com/gogf/gf/v2/frame/g"

type SystemInstruction struct {
	System              string                  `json:"system"`
	ServiceInstructions []ServiceSysInstruction `json:"service_instructions"`
}

type ServiceSysInstruction struct {
	ServiceID      string `json:"service_id"`
	Channel        string `json:"channel"`
	SysInstruction string `json:"sys_instruction"`
}

func (s *SystemInstruction) IsEmpty() bool {
	return g.IsEmpty(s.System) && len(s.ServiceInstructions) == 0
}
