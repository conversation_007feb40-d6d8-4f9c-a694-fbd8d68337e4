package claude

import "github.com/gogf/gf/v2/encoding/gjson"

// ClaudeMessage Claude 消息結構
// 用於表示 Claude API 中的單個消息
type ClaudeMessage struct {
	Role    string        `json:"role"`    // 角色：user 或 assistant
	Content []ContentPart `json:"content"` // 消息內容
}

// MarshalJSON 自定義 JSON 序列化，確保 ContentPart 正確序列化
func (cm ClaudeMessage) MarshalJSON() ([]byte, error) {
	// 手動序列化 Content 數組
	contentJsonArray := make([]map[string]interface{}, len(cm.Content))
	for i, part := range cm.Content {
		switch part.Type {
		case "text":
			contentJsonArray[i] = map[string]interface{}{
				"type": part.Type,
				"text": part.Text,
			}
		case "image":
			contentJsonArray[i] = map[string]interface{}{
				"type":   part.Type,
				"source": part.Source,
			}
		default:
			// 預設情況
			contentData := map[string]interface{}{
				"type": part.Type,
			}
			if part.Text != "" {
				contentData["text"] = part.Text
			}
			if part.Source != nil {
				contentData["source"] = part.Source
			}
			contentJsonArray[i] = contentData
		}
	}

	// 構建完整的消息對象
	messageData := map[string]interface{}{
		"role":    cm.Role,
		"content": contentJsonArray,
	}

	jsonStr := gjson.New(messageData).MustToJsonString()
	return []byte(jsonStr), nil
}

// ContentPart 內容部分
// 支援文本、圖片、工具使用和工具結果等多種內容類型，根據 Google Vertex AI Claude API 規範設計
type ContentPart struct {
	Type      string                 `json:"type"`                  // 內容類型：text, image, tool_use, tool_result
	Text      string                 `json:"text,omitempty"`        // 文本內容（僅當 type="text" 時使用）
	Source    *ImageSource           `json:"source,omitempty"`      // 圖片來源（僅當 type="image" 時使用）
	ID        string                 `json:"id,omitempty"`          // 工具調用 ID（僅當 type="tool_use" 或 "tool_result" 時使用）
	Name      string                 `json:"name,omitempty"`        // 工具名稱（僅當 type="tool_use" 時使用）
	Input     map[string]interface{} `json:"input,omitempty"`       // 工具輸入（僅當 type="tool_use" 時使用）
	Content   string                 `json:"content,omitempty"`     // 工具結果內容（僅當 type="tool_result" 時使用）
	IsError   bool                   `json:"is_error,omitempty"`    // 是否為錯誤結果（僅當 type="tool_result" 時使用）
	ToolUseID string                 `json:"tool_use_id,omitempty"` // 對應的工具使用 ID（僅當 type="tool_result" 時使用）
}

// MarshalJSON 自定義 JSON 序列化，確保根據類型只包含相關字段
func (cp ContentPart) MarshalJSON() ([]byte, error) {
	switch cp.Type {
	case "text":
		// 文本類型只包含 type 和 text 字段
		jsonStr := gjson.New(map[string]interface{}{
			"type": cp.Type,
			"text": cp.Text,
		}).MustToJsonString()
		return []byte(jsonStr), nil
	case "image":
		// 圖片類型只包含 type 和 source 字段
		jsonStr := gjson.New(map[string]interface{}{
			"type":   cp.Type,
			"source": cp.Source,
		}).MustToJsonString()
		return []byte(jsonStr), nil
	case "tool_use":
		// 工具使用類型包含 type, id, name, input 字段
		result := map[string]interface{}{
			"type": cp.Type,
			"id":   cp.ID,
			"name": cp.Name,
		}
		if cp.Input != nil {
			result["input"] = cp.Input
		}
		jsonStr := gjson.New(result).MustToJsonString()
		return []byte(jsonStr), nil
	case "tool_result":
		// 工具結果類型包含 type, tool_use_id, content, is_error 字段
		result := map[string]interface{}{
			"type":        cp.Type,
			"tool_use_id": cp.ToolUseID,
			"content":     cp.Content,
		}
		if cp.IsError {
			result["is_error"] = cp.IsError
		}
		jsonStr := gjson.New(result).MustToJsonString()
		return []byte(jsonStr), nil
	default:
		// 預設情況，包含所有非空字段
		result := map[string]interface{}{
			"type": cp.Type,
		}
		if cp.Text != "" {
			result["text"] = cp.Text
		}
		if cp.Source != nil {
			result["source"] = cp.Source
		}
		jsonStr := gjson.New(result).MustToJsonString()
		return []byte(jsonStr), nil
	}
}

// ImageSource 圖片來源
// 用於 base64 編碼的圖片數據
type ImageSource struct {
	Type      string `json:"type"`       // 固定為 "base64"
	MediaType string `json:"media_type"` // MIME 類型
	Data      string `json:"data"`       // Base64 編碼的圖片數據
}

// ClaudeRequest Claude API 請求結構
// 包含完整的 API 請求參數，符合 Google Vertex AI Claude API 規範
type ClaudeRequest struct {
	AnthropicVersion string          `json:"anthropic_version"` // API 版本
	MaxTokens        int32           `json:"max_tokens"`        // 最大 token 數
	Temperature      float32         `json:"temperature"`       // 溫度參數
	System           string          `json:"system,omitempty"`  // 系統指令
	Messages         []ClaudeMessage `json:"messages"`          // 消息列表
	Stream           bool            `json:"stream"`            // 是否流式響應
	Tools            []ClaudeTool    `json:"tools,omitempty"`   // 工具定義列表
}

// MarshalJSON 自定義 JSON 序列化，確保所有嵌套結構正確序列化
func (cr ClaudeRequest) MarshalJSON() ([]byte, error) {
	// 手動序列化 Messages 數組
	messagesJsonArray := make([]map[string]interface{}, len(cr.Messages))
	for i, message := range cr.Messages {
		// 手動序列化每個消息的內容
		contentArray := make([]map[string]interface{}, len(message.Content))
		for j, part := range message.Content {
			switch part.Type {
			case "text":
				contentArray[j] = map[string]interface{}{
					"type": part.Type,
					"text": part.Text,
				}
			case "image":
				contentArray[j] = map[string]interface{}{
					"type":   part.Type,
					"source": part.Source,
				}
			default:
				// 預設情況
				contentData := map[string]interface{}{
					"type": part.Type,
				}
				if part.Text != "" {
					contentData["text"] = part.Text
				}
				if part.Source != nil {
					contentData["source"] = part.Source
				}
				contentArray[j] = contentData
			}
		}

		messagesJsonArray[i] = map[string]interface{}{
			"role":    message.Role,
			"content": contentArray,
		}
	}

	// 構建完整的請求對象
	requestData := map[string]interface{}{
		"anthropic_version": cr.AnthropicVersion,
		"max_tokens":        cr.MaxTokens,
		"temperature":       cr.Temperature,
		"messages":          messagesJsonArray,
		"stream":            cr.Stream,
	}

	// 只有當 System 不為空時才添加
	if cr.System != "" {
		requestData["system"] = cr.System
	}

	jsonStr := gjson.New(requestData).MustToJsonString()
	return []byte(jsonStr), nil
}

// ClaudeResponse Claude API 響應結構
// 包含 API 返回的完整響應信息
type ClaudeResponse struct {
	ID           string                  `json:"id"`
	Type         string                  `json:"type"`
	Role         string                  `json:"role"`
	Content      []ClaudeResponseContent `json:"content"`
	Model        string                  `json:"model"`
	StopReason   string                  `json:"stop_reason"`
	StopSequence string                  `json:"stop_sequence"`
	Usage        ClaudeUsage             `json:"usage"`
}

// ClaudeResponseContent 響應內容
// 表示 API 響應中的內容部分，支持文本和工具調用
type ClaudeResponseContent struct {
	Type  string                 `json:"type"`
	Text  string                 `json:"text,omitempty"`
	ID    string                 `json:"id,omitempty"`
	Name  string                 `json:"name,omitempty"`
	Input map[string]interface{} `json:"input,omitempty"`
}

// ClaudeUsage token 使用情況
// 記錄 API 調用的 token 消耗統計
type ClaudeUsage struct {
	InputTokens  int32 `json:"input_tokens"`
	OutputTokens int32 `json:"output_tokens"`
}

// ClaudeTool Claude 工具定義
type ClaudeTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// ClaudeToolUse Claude 工具使用
type ClaudeToolUse struct {
	ID    string                 `json:"id"`
	Name  string                 `json:"name"`
	Input map[string]interface{} `json:"input"`
}

// ClaudeToolResult Claude 工具結果
type ClaudeToolResult struct {
	ToolUseID string `json:"tool_use_id"`
	Content   string `json:"content"`
	IsError   bool   `json:"is_error,omitempty"`
}
