package model

import "time"

type GenericMessage struct {
	TenantID    string    `json:"tenant_id"`
	ServiceID   string    `json:"service_id"`
	UserID      string    `json:"user_id"`
	Role        string    `json:"role"`
	DisplayName string    `json:"display_name"`
	Channel     string    `json:"channel"`
	MessageType string    `json:"message_type"`
	Message     string    `json:"message"`
	Meta        string    `json:"meta"`
	CrateAt     time.Time `json:"crate_at"`
}
