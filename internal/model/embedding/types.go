package embedding

// EmbeddingConfig 嵌入模型配置結構
// 設計為通用配置，支持多種 embedding 提供商
type EmbeddingConfig struct {
	Provider     string `json:"provider"`       // 提供商類型：aoai, openai, vertex, huggingface 等
	BaseURL      string `json:"base_url"`       // API 端點 URL
	APIKey       string `json:"api_key"`        // API 密鑰
	APIVersion   string `json:"api_version"`    // API 版本
	Model        string `json:"model"`          // embedding 模型名稱
	Dimensions   int    `json:"dimensions"`     // 向量維度
	MaxBatchSize int    `json:"max_batch_size"` // 批次處理大小
	Timeout      int    `json:"timeout"`        // 請求超時時間（秒）
	MaxRetries   int    `json:"max_retries"`    // 最大重試次數
	RetryDelay   int    `json:"retry_delay"`    // 重試延遲（秒）
}

// EmbeddingRequest 嵌入請求結構
type EmbeddingRequest struct {
	Texts      []string `json:"texts" v:"required#文本列表不能為空"` // 待處理的文本列表
	Model      string   `json:"model,omitempty"`             // 可選：覆蓋默認模型
	Dimensions int      `json:"dimensions,omitempty"`        // 可選：覆蓋默認維度
	User       string   `json:"user,omitempty"`              // 可選：用戶標識
}

// EmbeddingResponse 嵌入響應結構
type EmbeddingResponse struct {
	Embeddings  [][]float32 `json:"embeddings"`   // 生成的向量列表
	Model       string      `json:"model"`        // 實際使用的模型名稱
	Usage       Usage       `json:"usage"`        // Token 使用統計
	ProcessTime int64       `json:"process_time"` // 處理時間（毫秒）
	Provider    string      `json:"provider"`     // 提供商名稱
}

// Usage Token 使用統計
type Usage struct {
	PromptTokens int `json:"prompt_tokens"` // 輸入 Token 數量
	TotalTokens  int `json:"total_tokens"`  // 總 Token 數量
}

// EmbeddingMetrics 性能指標
type EmbeddingMetrics struct {
	RequestCount    int64   `json:"request_count"`     // 請求總數
	SuccessCount    int64   `json:"success_count"`     // 成功請求數
	ErrorCount      int64   `json:"error_count"`       // 錯誤請求數
	AverageLatency  float64 `json:"average_latency"`   // 平均延遲（毫秒）
	TotalTokens     int64   `json:"total_tokens"`      // 總 Token 消耗
	LastRequestTime int64   `json:"last_request_time"` // 最後請求時間戳
}

// BatchResult 批次處理結果
type BatchResult struct {
	Embeddings [][]float32 `json:"embeddings"` // 成功生成的向量
	Errors     []error     `json:"errors"`     // 各文本的錯誤信息
	Indices    []int       `json:"indices"`    // 成功處理的文本索引
}

// EmbeddingProvider 提供商類型常量
const (
	ProviderAzureOpenAI = "aoai"
	ProviderOpenAI      = "openai"
	ProviderVertexAI    = "vertex"
	ProviderHuggingFace = "huggingface"
	ProviderLocal       = "local"
)

// 默認配置常量
const (
	DefaultMaxBatchSize = 100  // 默認批次大小
	DefaultTimeout      = 30   // 默認超時時間（秒）
	DefaultMaxRetries   = 3    // 默認最大重試次數
	DefaultRetryDelay   = 2    // 默認重試延遲（秒）
	DefaultDimensions   = 1536 // 默認向量維度（text-embedding-ada-002）
)

// 模型配置映射
var ModelDimensions = map[string]int{
	"text-embedding-ada-002":  1536, // OpenAI/Azure OpenAI
	"text-embedding-3-small":  1536, // OpenAI
	"text-embedding-3-large":  3072, // OpenAI
	"textembedding-gecko":     768,  // Google Vertex AI
	"textembedding-gecko-001": 768,  // Google Vertex AI
	"text-embedding-004":      768,  // Google Vertex AI
}

// GetModelDimensions 獲取模型的向量維度
func GetModelDimensions(model string) int {
	if dim, exists := ModelDimensions[model]; exists {
		return dim
	}
	return DefaultDimensions
}

// ValidateProvider 驗證提供商類型
func ValidateProvider(provider string) bool {
	switch provider {
	case ProviderAzureOpenAI, ProviderOpenAI, ProviderVertexAI, ProviderHuggingFace, ProviderLocal:
		return true
	default:
		return false
	}
}

// NewDefaultConfig 創建默認配置
func NewDefaultConfig(provider string) *EmbeddingConfig {
	return &EmbeddingConfig{
		Provider:     provider,
		MaxBatchSize: DefaultMaxBatchSize,
		Timeout:      DefaultTimeout,
		MaxRetries:   DefaultMaxRetries,
		RetryDelay:   DefaultRetryDelay,
		Dimensions:   DefaultDimensions,
	}
}
