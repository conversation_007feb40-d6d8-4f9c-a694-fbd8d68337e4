package tenant

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/llms/aoai"
	"brainHub/internal/llms/claude"
	"brainHub/internal/llms/gemini"
	"context"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/os/gtimer"
	"github.com/gogf/gf/v2/util/gconv"
	"time"
)

// AiModelConfig 租户AI模型配置
// 用于管理租户的AI模型和用户的LLM实例
type AiModelConfig struct {
	TenantID      string          `json:"tenant_id"`  // 租户ID
	ModelType     string          `json:"model_type"` // 模型名称
	userIDWithLLM *gmap.StrAnyMap // 用户ID到LLM实例的映射
}

// LLMUsage LLM使用记录
// 记录LLM实例的最后更新时间和实例本身
type LLMUsage struct {
	UpdateAt *gtime.Time // 最后更新时间
	LLM      llms.ILLMs  // LLM实例
}

// Create 初始化AiModelConfig
// 创建用户ID到LLM实例的映射，并启动定时器清理超时的LLM实例
func (t *AiModelConfig) Create(ctx context.Context) (err error) {
	if t.userIDWithLLM == nil {
		t.userIDWithLLM = gmap.NewStrAnyMap(true)
		t.checkLLMTimeout(ctx)
	}

	return
}

// checkLLMTimeout 检查并清理超时的LLM实例
func (t *AiModelConfig) checkLLMTimeout(ctx context.Context) {
	gtimer.SetInterval(ctx, 5*time.Second, func(ctx context.Context) {
		userIDs := garray.NewStrArray()
		vTimeout, err := g.Cfg().Get(ctx, "system.ai_reserve_ttl", "1h")
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}

		t.userIDWithLLM.Iterator(func(userId string, v interface{}) bool {
			var llmObj *LLMUsage
			err := gconv.Struct(v, &llmObj)
			if err != nil {
				g.Log().Error(ctx, err)
				return true
			}

			if llmObj != nil {
				if gtime.Now().Sub(llmObj.UpdateAt) >= vTimeout.Duration() {
					userIDs.Append(userId)
					g.Log().Debugf(ctx, "Remove timeout LLM for user [%s]", userId)
				}
			}

			return true
		})

		if userIDs.Len() > 0 {
			t.userIDWithLLM.Removes(userIDs.Slice())
		}
	})
}

// LLM 获取或初始化指定用户ID的LLM实例
// 返回LLM实例、是否需要初始化的标志和可能的错误
// 如果用户已有LLM实例，则更新最后使用时间并返回该实例
// 如果用户没有LLM实例，则根据ModelName创建新的实例
func (t *AiModelConfig) LLM(userId string) (llms.ILLMs, bool, error) {
	if t.userIDWithLLM == nil {
		return nil, false, gerror.New("AiModelConfig not initialized, please call Create() first")
	}

	// 检查用户是否已有LLM实例
	if t.userIDWithLLM.Contains(userId) {
		var llmObj *LLMUsage
		err := t.userIDWithLLM.GetVar(userId).Struct(&llmObj)
		if err != nil {
			return nil, false, gerror.Wrap(err, "failed to get LLM usage")
		}

		if llmObj != nil {
			// 更新最后使用时间
			llmObj.UpdateAt = gtime.Now()
			return llmObj.LLM, false, nil
		}
	}

	// 创建新的LLM实例
	var llm llms.ILLMs

	// 根據模型類型創建對應的 LLM 實例
	switch t.ModelType {
	case consts.VertexAIGemini:
		// 使用 Gemini 模型
		llm = gemini.New()
		llmUsage := &LLMUsage{LLM: llm, UpdateAt: gtime.Now()}
		t.userIDWithLLM.Set(userId, llmUsage)
		return llmUsage.LLM, true, nil

	case consts.VertexAIClaude:
		// 使用 Claude 模型
		llm = claude.New()
		llmUsage := &LLMUsage{LLM: llm, UpdateAt: gtime.Now()}
		t.userIDWithLLM.Set(userId, llmUsage)
		return llmUsage.LLM, true, nil

	case consts.AOAI:
		// 使用 Azure OpenAI 模型
		llm = aoai.New()
		llmUsage := &LLMUsage{LLM: llm, UpdateAt: gtime.Now()}
		t.userIDWithLLM.Set(userId, llmUsage)
		return llmUsage.LLM, true, nil

	default:
		// 不支持的模型类型
		return nil, false, gerror.Newf("unsupported model type [%v]", t.ModelType)
	}
}

// Release 释放AiModelConfig资源
// 清空用户LLM映射
func (t *AiModelConfig) Release(ctx context.Context) {
	if t.userIDWithLLM != nil {
		t.userIDWithLLM.Clear()
	}
}

// GetUserCount 获取当前活跃用户数量
func (t *AiModelConfig) GetUserCount() int {
	if t.userIDWithLLM == nil {
		return 0
	}
	return t.userIDWithLLM.Size()
}
