package tenant

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms/aoai"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestAiModelConfig_LLM_AOAI 測試 Azure OpenAI 類型的 LLM 實例創建
func TestAiModelConfig_LLM_AOAI(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 AiModelConfig 實例
		config := &AiModelConfig{
			TenantID:  "test-tenant",
			ModelType: consts.AOAI,
		}

		// 初始化配置
		err := config.Create(ctx)
		t.AssertNil(err)

		// 測試獲取 AOAI LLM 實例
		llm, needInit, err := config.LLM("test-user-1")
		t.AssertNil(err)
		t.Assert(needInit, true) // 第一次創建應該需要初始化
		t.AssertNE(llm, nil)

		// 驗證返回的是 AOAI 實例
		_, ok := llm.(*aoai.AoAi)
		t.Assert(ok, true)

		// 測試再次獲取同一用戶的 LLM 實例（應該返回緩存的實例）
		llm2, needInit2, err2 := config.LLM("test-user-1")
		t.AssertNil(err2)
		t.Assert(needInit2, false) // 第二次獲取不需要初始化
		t.AssertNE(llm2, nil)
		t.Assert(llm, llm2) // 應該是同一個實例

		// 測試不同用戶獲取 LLM 實例
		llm3, needInit3, err3 := config.LLM("test-user-2")
		t.AssertNil(err3)
		t.Assert(needInit3, true) // 新用戶需要初始化
		t.AssertNE(llm3, nil)
		// 注意：由於 LLM 實例可能共享某些資源，我們只驗證它們不是 nil

		// 驗證用戶數量
		userCount := config.GetUserCount()
		t.Assert(userCount, 2) // 應該有兩個用戶

		// 測試釋放資源
		config.Release(ctx)
		userCountAfterRelease := config.GetUserCount()
		t.Assert(userCountAfterRelease, 0) // 釋放後應該沒有用戶
	})
}

// TestAiModelConfig_LLM_UnsupportedType 測試不支持的模型類型
func TestAiModelConfig_LLM_UnsupportedType(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 AiModelConfig 實例，使用不支持的模型類型
		config := &AiModelConfig{
			TenantID:  "test-tenant",
			ModelType: "unsupported-model",
		}

		// 初始化配置
		err := config.Create(ctx)
		t.AssertNil(err)

		// 測試獲取不支持的 LLM 類型
		llm, needInit, err := config.LLM("test-user")
		t.AssertNE(err, nil) // 應該返回錯誤
		t.Assert(needInit, false)
		t.AssertEQ(llm, nil) // 應該返回 nil

		// 驗證錯誤消息包含模型類型信息
		t.AssertIN("unsupported-model", err.Error())
	})
}

// TestAiModelConfig_LLM_AllSupportedTypes 測試所有支持的 LLM 類型
func TestAiModelConfig_LLM_AllSupportedTypes(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試所有支持的模型類型
		supportedTypes := []string{
			consts.VertexAIGemini,
			consts.VertexAIClaude,
			consts.AOAI,
		}

		for _, modelType := range supportedTypes {
			// 創建配置
			config := &AiModelConfig{
				TenantID:  "test-tenant",
				ModelType: modelType,
			}

			// 初始化配置
			err := config.Create(ctx)
			t.AssertNil(err)

			// 測試獲取 LLM 實例
			llm, needInit, err := config.LLM("test-user")
			t.AssertNil(err)
			t.Assert(needInit, true)
			t.AssertNE(llm, nil)

			// 清理資源
			config.Release(ctx)
		}
	})
}

// TestAiModelConfig_LLM_NotInitialized 測試未初始化的配置
func TestAiModelConfig_LLM_NotInitialized(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建未初始化的 AiModelConfig 實例
		config := &AiModelConfig{
			TenantID:  "test-tenant",
			ModelType: consts.AOAI,
		}

		// 不調用 Create() 方法，直接嘗試獲取 LLM
		llm, needInit, err := config.LLM("test-user")
		t.AssertNE(err, nil) // 應該返回錯誤
		t.Assert(needInit, false)
		t.AssertEQ(llm, nil) // 應該返回 nil

		// 驗證錯誤消息
		t.AssertIN("not initialized", err.Error())
	})
}
