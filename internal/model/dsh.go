package model

import (
	"github.com/gogf/gf/v2/frame/g"
	"time"
)

type GetContentsReq struct {
	Schema    string  `json:"schema"`
	Table     string  `json:"table"`
	WhereCond string  `json:"where_cond"`
	Params    g.Slice `json:"params"`
	Fields    g.Slice `json:"fields"`
	Limit     int     `json:"limit"`
	RawSQL    string  `json:"raw_sql"`
	Order     string  `json:"order"`
}

type GetContentsRes struct {
	Contents []map[string]any `json:"contents"`
	Code     int              `json:"code"`
	Message  string           `json:"message"`
	Cost     string           `json:"cost"`
}
type ChatMessage struct {
	Role     string    `json:"role"`
	Message  string    `json:"message"`
	CreateAt time.Time `json:"create_at"`
}
