package consts

// 時間格式常量
// 用於時間格式化和解析
const (
	// 日期格式
	TimeFormatYearMonth     = "Y_m"      // 年月格式（用於表名）
	TimeFormatYearMonthDay  = "Y-m-d"    // 年月日格式
	TimeFormatDateTime      = "Y-m-d H:i:s" // 完整日期時間格式
	TimeFormatLogFile       = "brainHub_{Y-m-d}.log" // 日誌檔案名格式

	// 時間間隔格式
	TimeFormatDuration1Day  = "1d"       // 1 天間隔
	TimeFormatDuration3Days = "3d"       // 3 天間隔
	TimeFormatDuration7Days = "7d"       // 7 天間隔
	TimeFormatDuration40Sec = "40s"      // 40 秒間隔
	TimeFormatDuration2Hour = "2h"       // 2 小時間隔
	TimeFormatDuration1Hour = "1h"       // 1 小時間隔
)

// 字符串格式常量
// 用於各種字符串格式化操作
const (
	// 通用字符串
	StringEmpty     = ""        // 空字符串
	StringSuccess   = "success" // 成功字符串
	StringError     = "error"   // 錯誤字符串
	StringFailed    = "failed"  // 失敗字符串
	StringDefault   = "default" // 預設字符串

	// 狀態字符串
	StringEnabled   = "enabled"  // 啟用狀態
	StringDisabled  = "disabled" // 禁用狀態
	StringActive    = "active"   // 活躍狀態
	StringInactive  = "inactive" // 非活躍狀態

	// 布爾值字符串表示
	StringTrue  = "true"  // 真值字符串
	StringFalse = "false" // 假值字符串
)

// 日誌級別常量
// 用於日誌系統的級別設置
const (
	LogLevelDebug   = "debug"   // 調試級別
	LogLevelInfo    = "info"    // 信息級別
	LogLevelWarn    = "warn"    // 警告級別
	LogLevelWarning = "warning" // 警告級別（別名）
	LogLevelError   = "error"   // 錯誤級別
	LogLevelAll     = "all"     // 所有級別
)

// 編碼格式常量
// 用於各種編碼和解碼操作
const (
	EncodingUTF8    = "utf-8"    // UTF-8 編碼
	EncodingBase64  = "base64"   // Base64 編碼
	EncodingJSON    = "json"     // JSON 格式
	EncodingXML     = "xml"      // XML 格式
	EncodingYAML    = "yaml"     // YAML 格式
)

// 數值格式常量
// 用於數值相關的格式化
const (
	// 百分比
	PercentageZero    = 0.0   // 0%
	PercentageTwenty  = 0.2   // 20%
	PercentageFifty   = 0.5   // 50%
	PercentageEighty  = 0.8   // 80%
	PercentageEightyFive = 0.85 // 85%
	PercentageHundred = 1.0   // 100%

	// 溫度值
	TemperatureLow    = 0.1   // 低溫度
	TemperatureMedium = 0.3   // 中等溫度
	TemperatureHigh   = 0.7   // 高溫度
	TemperatureMax    = 1.0   // 最高溫度
)

// 字符分隔符常量
// 用於字符串分割和連接
const (
	SeparatorComma     = ","    // 逗號分隔符
	SeparatorSemicolon = ";"    // 分號分隔符
	SeparatorColon     = ":"    // 冒號分隔符
	SeparatorDot       = "."    // 點分隔符
	SeparatorSlash     = "/"    // 斜線分隔符
	SeparatorBackslash = "\\"   // 反斜線分隔符
	SeparatorPipe      = "|"    // 管道分隔符
	SeparatorUnderscore = "_"   // 下劃線分隔符
	SeparatorHyphen    = "-"    // 連字符分隔符
)

// 特殊字符常量
const (
	CharNewline = "\n"   // 換行符
	CharTab     = "\t"   // 製表符
	CharSpace   = " "    // 空格符
	CharQuote   = "\""   // 雙引號
	CharSingleQuote = "'" // 單引號
)

// API 版本常量
const (
	APIVersionVertexClaude = "vertex-2023-10-16" // Google Vertex AI Claude API 專用版本
)

// 角色常量
const (
	RoleUserClaude = "user" // Claude 用戶角色
	RoleTextType   = "text" // 文本類型
)

// 緩存相關字符串常量
const (
	StringMasked = "***masked***" // 敏感信息遮罩字符串
)
