package consts

// 配置鍵名常量
// 用於從配置文件中獲取各種系統配置參數
const (
	// 系統配置鍵
	ConfigSystemDefaultAI          = "system.default_ai"                    // 預設 AI 模型配置鍵
	ConfigSystemDataSyncName       = "system.data_sync.name"               // 資料同步服務名稱配置鍵
	ConfigSystemDataSyncScheme     = "system.data_sync.scheme"             // 資料同步服務協議配置鍵
	ConfigSystemChatMessageDuration = "system.chat_message.recent_duration" // 聊天訊息保留時間配置鍵
	ConfigSystemAISendRetryTTL     = "system.ai_send_retry_ttl"            // AI 發送重試時間配置鍵
	ConfigSystemAIRouterTTL        = "system.ai_router_ttl"                // AI 路由器 TTL 配置鍵
	ConfigSystemAIReserveTTL       = "system.ai_reserve_ttl"               // AI 保留時間配置鍵
	ConfigSystemAssetManagementName = "system.asset_management.name"        // 資產管理服務名稱配置鍵
	ConfigSystemAssetManagementScheme = "system.asset_management.scheme"    // 資產管理服務協議配置鍵

	// LLM 配置鍵
	ConfigDefaultLLMs                = "default_llms"                      // 預設 LLM 配置鍵
	ConfigLLMsGeminiMaxContinuations = "llms.gemini.max_continuations"     // Gemini 最大續寫次數配置鍵
	ConfigLLMsGeminiTotalTokenBudget = "llms.gemini.total_token_budget"    // Gemini 總 Token 預算配置鍵
	ConfigLLMsAoAiMaxContinuations   = "llms.aoai.max_continuations"       // AoAi 最大續寫次數配置鍵
	ConfigLLMsAoAiTotalTokenBudget   = "llms.aoai.total_token_budget"      // AoAi 總 Token 預算配置鍵
	ConfigLLMsClaudeMaxContinuations = "llms.claude.max_continuations"     // Claude 最大續寫次數配置鍵
	ConfigLLMsClaudeTotalTokenBudget = "llms.claude.total_token_budget"    // Claude 總 Token 預算配置鍵

	// Nacos 配置鍵
	ConfigNacosMode = "nacos.mode" // Nacos 模式配置鍵

	// RabbitMQ 配置鍵
	ConfigRabbitMQURL = "rabbitMQ.url" // RabbitMQ 連接 URL 配置鍵

	// Prompt 配置鍵
	ConfigPromptsAudioToText = "prompts.audio_to_text_prompt" // 音頻轉文本提示詞配置鍵

	// 系統加密設定配置鍵
	ConfigSystemEncryptionPrefix      = "system.encryption_setting.prefix"            // 加密設定前綴配置鍵
	ConfigSystemEncryptionSettingURL  = "system.encryption_setting.setting_url"      // 加密設定 URL 配置鍵
	ConfigSystemEncryptionInstruction = "system.encryption_setting.system_instruction" // 加密設定系統指令配置鍵
	ConfigSystemEncryptionTemplate    = "system.encryption_setting.template"         // 加密設定模板配置鍵

	// 系統路徑配置鍵
	ConfigSystemAssetPath = "system.asset_path" // 資產路徑配置鍵
)

// 預設配置值常量
// 當配置文件中沒有指定值時使用的預設值
const (
	// 系統預設值
	DefaultAIModel           = "gemini"     // 預設 AI 模型
	DefaultDataSyncName      = "dsh.svc"    // 預設資料同步服務名稱
	DefaultDataSyncScheme    = "http"       // 預設資料同步服務協議
	DefaultChatMessageDuration = "3d"       // 預設聊天訊息保留時間
	DefaultAssetManagementName = "ams.svc"  // 預設資產管理服務名稱
	DefaultAssetManagementScheme = "http"   // 預設資產管理服務協議

	// LLM 預設值
	DefaultMaxContinuations    = 3     // 預設最大續寫次數
	DefaultTotalTokenBudget    = 8000  // 預設總 Token 預算
	DefaultThinkingBudget      = 1000  // 預設思考預算

	// 網路預設值
	DefaultScheme = "http" // 預設網路協議

	// Prompt 預設值
	DefaultAudioToTextPrompt = "請將這個音頻文件轉換為文本，只返回轉換後的文本內容，不要添加任何額外的說明或格式。" // 預設音頻轉文本提示詞

	// Token 預設值
	DefaultAudioTokenBudget = 4096 // 預設音頻處理 Token 預算

	// 路徑預設值
	DefaultAssetPath = "./asset" // 預設資產路徑
)
