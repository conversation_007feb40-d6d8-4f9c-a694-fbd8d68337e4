package consts

import "time"

// 緩存過期時間常量
// 用於設置各種緩存的過期時間
const (
	// 基礎緩存時間（秒）
	CacheTimeOneMinute     = 60          // 1 分鐘
	CacheTimeFiveMinutes   = 300         // 5 分鐘
	CacheTimeThirtyMinutes = 1800        // 30 分鐘
	CacheTimeOneHour       = 3600        // 1 小時
	CacheTimeOneDay        = 86400       // 1 天

	// 特定功能緩存時間
	CacheTimeEmbeddingProvider = 1800    // Embedding 提供商緩存時間（30 分鐘）
	CacheTimeTokenCalculation  = 30      // Token 計算結果緩存時間（30 秒）
	CacheTimeSystemInstruction = 86400   // 系統指令緩存時間（1 天）
	CacheTimeLLMParams         = 86400   // LLM 參數緩存時間（1 天）

	// 毫秒單位緩存時間
	CacheTimeThirtyMinutesMs = 30 * 60 * 1000 // 30 分鐘（毫秒）
)

// 緩存相關配置常量
const (
	// Redis 存在性檢查結果
	CacheExistsTrue  = 1 // Redis 鍵存在
	CacheExistsFalse = 0 // Redis 鍵不存在

	// 緩存操作相關
	CacheDefaultTenant  = "default"  // 預設租戶
	CacheDefaultService = "default"  // 預設服務
)

// 時間相關常量
// 用於緩存時間計算和設置
var (
	// 基於 gtime.D 的時間常量
	CacheTimeOneDayDuration = time.Hour * 24 // 1 天時間間隔
)

// 緩存鍵前綴常量
// 用於構建不同類型的緩存鍵
const (
	CacheKeyPrefixEmbedding = "embedding_provider:" // Embedding 提供商緩存鍵前綴
	CacheKeyPrefixToken     = "token_cache:"         // Token 緩存鍵前綴
)
