package consts

const (
	ExchangeName = "dsh"
)

// the type for mariadb  action
const (
	ActionInsert         = "insert"
	ActionDelete         = "delete"
	ActionUpdate         = "update"
	ActionUpdateOrInsert = "updateOrInsert"
	ActionCreateSchema   = "createSchema"
	ActionCreateTable    = "createTable"
)

// the type for weaviate action
const (
	ActionCreateCollection  = "create_collection"
	ActionAddNewProperties  = "add_new_properties"
	ActionCreateData        = "create_data"
	ActionUpdateProperties  = "update_properties"
	ActionCreateTenant      = "create_tenant"
	ActionClearDataByFilter = "clear_data_by_filter"
	ActionEmptyCollection   = "empty_collection"
	ActionDeleteCollection  = "delete_collection"
	ActionDeleteTenants     = "delete_tenants"
	ActionUpdateVector      = "update_vector"
)
