package consts

// 資料庫欄位名稱常量
// 用於資料庫查詢和操作中的欄位名稱
const (
	// 通用欄位
	DBFieldMessage   = "message"    // 訊息內容欄位
	DBFieldRole      = "role"       // 角色欄位
	DBFieldCreateAt  = "create_at"  // 創建時間欄位
	DBFieldTenantID  = "tenant_id"  // 租戶 ID 欄位
	DBFieldServiceID = "service_id" // 服務 ID 欄位
	DBFieldUserID    = "user_id"    // 用戶 ID 欄位
	DBFieldChannel   = "channel"    // 渠道欄位
	DBFieldLLMName   = "llm_name"   // LLM 名稱欄位

	// 查詢條件欄位
	DBConditionTenantID = "tenant_id = ?"                                                      // 租戶 ID 查詢條件
	DBConditionChatMessage = "tenant_id = ? and service_id= ? and user_id = ? and create_at between ? and ? and channel= ?" // 聊天訊息查詢條件

	// 排序條件
	DBOrderCreateAtDesc = "create_at desc" // 按創建時間降序排列
)

// 資料庫查詢相關常量
const (
	// 查詢結果處理
	DBResultEmpty = 0 // 空查詢結果
	DBResultFound = 1 // 找到查詢結果

	// 陣列索引
	DBArrayFirstIndex = 0 // 陣列第一個元素索引
)

// SQL 查詢相關常量
const (
	// 查詢欄位組合
	DBFieldsChatMessage = "message,role,create_at" // 聊天訊息查詢欄位
	DBFieldsLLMName     = "llm_name"               // LLM 名稱查詢欄位
)
