package consts

import "time"

// 網路超時時間常量
// 用於設置各種網路操作的超時時間
const (
	// 基礎超時時間（秒）
	NetworkTimeoutDefault  = 30  // 預設超時時間
	NetworkTimeoutShort    = 5   // 短超時時間
	NetworkTimeoutMedium   = 15  // 中等超時時間
	NetworkTimeoutLong     = 60  // 長超時時間
	NetworkTimeoutVeryLong = 120 // 很長超時時間

	// 特定功能超時時間
	NetworkTimeoutEmbedding = 30 // Embedding 服務超時時間
	NetworkTimeoutLLM       = 60 // LLM 服務超時時間
	NetworkTimeoutDatabase  = 10 // 資料庫操作超時時間
)

// 重試次數常量
// 用於設置各種操作的重試次數
const (
	// 基礎重試次數
	NetworkRetryDefault  = 3  // 預設重試次數
	NetworkRetryLow      = 1  // 低重試次數
	NetworkRetryMedium   = 3  // 中等重試次數
	NetworkRetryHigh     = 5  // 高重試次數
	NetworkRetryVeryHigh = 10 // 很高重試次數

	// 特定功能重試次數
	NetworkRetryMessageQ   = 5 // 消息隊列重試次數
	NetworkRetryConnection = 3 // 連接重試次數
)

// 重試間隔時間常量
// 用於設置重試操作之間的間隔時間
const (
	// 基礎重試間隔（秒）
	NetworkRetryIntervalDefault = 2  // 預設重試間隔
	NetworkRetryIntervalShort   = 1  // 短重試間隔
	NetworkRetryIntervalMedium  = 5  // 中等重試間隔
	NetworkRetryIntervalLong    = 10 // 長重試間隔

	// 特定功能重試間隔
	NetworkRetryIntervalMessageQ = 5 // 消息隊列重試間隔
)

// 時間間隔常量（time.Duration 類型）
// 用於需要 time.Duration 類型的場景
var (
	// 重試間隔
	NetworkRetryIntervalSecond      = time.Second      // 1 秒間隔
	NetworkRetryIntervalFiveSeconds = time.Second * 5  // 5 秒間隔
	NetworkRetryIntervalTenSeconds  = time.Second * 10 // 10 秒間隔

	// 重連間隔
	NetworkReconnectIntervalMax = time.Minute * 5 // 最大重連間隔（5 分鐘）
)

// 連接相關常量
const (
	// 連接池配置
	NetworkConnectionPoolSize = 10 // 連接池大小
	NetworkMaxConcurrentCalls = 10 // 最大並發調用數
	NetworkMessageWorkerCount = 10 // 消息處理工作者數量

	// 連接狀態
	NetworkConnectionHealthy   = true  // 連接健康
	NetworkConnectionUnhealthy = false // 連接不健康

	// 通道緩衝區大小
	NetworkChannelBufferSize = 1 // 通道緩衝區大小
)

// 網路協議常量
const (
	NetworkProtocolHTTP  = "http"  // HTTP 協議
	NetworkProtocolHTTPS = "https" // HTTPS 協議
	NetworkProtocolTCP   = "tcp"   // TCP 協議
	NetworkProtocolAMQP  = "amqp"  // AMQP 協議

	// 交換機類型
	NetworkExchangeTypeDirect = "direct" // 直接交換機類型
)

// 網路地址相關常量
const (
	NetworkAddressLocalhost = "127.0.0.1" // 本地地址
	NetworkPortDefault      = "9001"      // 預設端口
)

// HTTP 客戶端超時時間常量
const (
	HTTPTimeoutDefault = 60 // 預設 HTTP 超時時間（秒）
	HTTPTimeoutShort   = 30 // 短 HTTP 超時時間（秒）
)

// 時間間隔常量（time.Duration 類型）
var (
	HTTPTimeoutDefaultDuration = time.Duration(HTTPTimeoutDefault) * time.Second // 60 秒超時
	HTTPTimeoutShortDuration   = time.Duration(HTTPTimeoutShort) * time.Second   // 30 秒超時
)

// 緩存過期時間常量
const (
	CacheExpiryTokenMinutes = 50 // Token 緩存過期時間（分鐘）
)

// CacheExpiryTokenDuration 時間間隔常量（time.Duration 類型）
var (
	CacheExpiryTokenDuration = time.Duration(CacheExpiryTokenMinutes) * time.Minute // 50 分鐘緩存
)
