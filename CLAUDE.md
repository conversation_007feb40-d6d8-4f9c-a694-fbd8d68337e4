# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

這是一個基於 GoFrame 框架構建的 Go 語言項目，名為 "brainHub"。它是一個AI聊天和文件處理後端服務，包含以下核心功能：

- **Brain API**: AI聊天功能，支持聊天和帶附件的聊天
- **Storage API**: 文件上傳、下載、列表和刪除功能  
- **Omnichannel API**: 全管道聊天功能
- **LLM Router**: 大語言模型路由，支持 Google Vertex AI

## Development Commands

### 構建和運行
```bash
# 安裝 GoFrame CLI 工具
make cli

# 構建項目
make build

# 運行項目 (構建後)
./main

# 或直接運行源碼
go run main.go
```

### 開發工具命令
```bash
# 更新 GoFrame 到最新版本
make up

# 生成控制器和 SDK
make ctrl

# 生成 DAO/DO/Entity 文件
make dao

# 生成服務文件
make service

# 生成枚舉文件
make enums

# 生成 protobuf 文件
make pb
make pbentity
```

### Docker 和部署
```bash
# 構建 Docker 鏡像
make image

# 構建並推送鏡像
make image.push

# 部署到 Kubernetes
make deploy
```

### 快速部署到 GCP
```bash
# 構建並上傳到 GCP 虛擬機
./build.sh

# 連接到 GCP 虛擬機
./connect.sh
```

## Architecture Overview

### 目錄結構
- `api/`: API 定義文件，包含各模組的 v1 版本接口
- `internal/controller/`: 控制器實現，處理 HTTP 請求
- `internal/logic/`: 業務邏輯層
- `internal/service/`: 服務層接口
- `internal/model/`: 數據模型定義
- `internal/dao/`: 數據訪問層
- `internal/llms/`: 大語言模型集成（Vertex AI）
- `manifest/`: Kubernetes 部署配置
- `hack/`: 構建和開發工具配置

### 核心模組
1. **Brain**: AI 聊天核心模組，處理文本和文件聊天
2. **Storage**: 文件存儲管理，支持 Google Cloud Storage  
3. **Omnichannel**: 全管道聊天功能
4. **LLM Router**: 智能路由，決定使用哪個 LLM 處理請求

### 技術棧
- **框架**: GoFrame v2.9.0
- **AI**: Google Vertex AI (Gemini)
- **存儲**: Google Cloud Storage
- **緩存**: Redis
- **容器**: Docker + Kubernetes
- **網頁抓取**: Playwright-go
- **其他**: YouTube API, HTML to Markdown

## Configuration

- 主配置文件: `manifest/config/config.yaml`
- 構建配置: `hack/config.yaml`
- GCP 認證: `key/key.json`

## Development Notes

- 使用 GoFrame 的自動生成功能來創建控制器、服務和數據層
- API 按版本（v1）組織，便於後續擴展
- 支持中間件進行請求/響應日誌記錄
- 項目遵循 GoFrame 的標準項目結構