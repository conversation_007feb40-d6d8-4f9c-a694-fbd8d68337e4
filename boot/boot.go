package boot

import (
	"brainHub/internal/consts"
	"fmt"
	cfgNacos "github.com/gogf/gf/contrib/config/nacos/v2"
	regNacos "github.com/gogf/gf/contrib/registry/nacos/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gsel"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
)

var readyCh = make(chan struct{})

func WaitReady() {
	<-readyCh
}

func init() {
	initNacos()
	close(readyCh)
}
func initNacos() {
	ctx := gctx.GetInitCtx()
	vMode, err := g.Cfg().Get(ctx, "nacos.mode", "")
	if err != nil {
		panic(err)
	}
	prefix := fmt.Sprintf("nacos.%s", vMode.String())
	fnGetVar := func(tag string) *g.Var {
		v, err := g.Cfg().Get(ctx, prefix+"."+tag)
		if err != nil {
			panic(err)
		}
		return v
	}
	vIP := fnGetVar("ip")
	vPort := fnGetVar("port")
	vCatchDir := fnGetVar("reg.catchDir")
	vLogDir := fnGetVar("reg.logDir")
	vNameSpace := fnGetVar("nameSpace")
	vGroup := fnGetVar("reg.serviceGroup")
	vUserName := fnGetVar("userName")
	vPass := fnGetVar("password")
	vServiceName := fnGetVar("reg.serviceName")
	vClusterName := fnGetVar("reg.clusterName")
	vMaxSize := fnGetVar("log.maxSize")
	vMaxAge := fnGetVar("log.maxAge")
	vLocalTime := fnGetVar("log.localTime")
	vCompress := fnGetVar("log.compress")
	vMaxBackup := fnGetVar("log.maxBackup")
	var url = vIP.String()

	if !vPort.IsNil() && !vPort.IsEmpty() {
		url += ":" + vPort.String()
	}

	logOption := &constant.ClientLogRollingConfig{
		MaxSize:    vMaxSize.Int(),
		MaxAge:     vMaxAge.Int(),
		MaxBackups: vMaxBackup.Int(),
		LocalTime:  vLocalTime.Bool(),
		Compress:   vCompress.Bool(),
	}

	consts.ServiceName = vServiceName.String()

	gsvc.SetRegistry(
		regNacos.New(url,

			constant.WithUsername(vUserName.String()),
			constant.WithPassword(vPass.String()),
			constant.WithNamespaceId(vNameSpace.String()),
			constant.WithCacheDir(vCatchDir.String()),
			constant.WithLogRollingConfig(logOption),
			constant.WithLogDir(vLogDir.String()),
		).
			SetClusterName(vClusterName.String()).
			SetGroupName(vGroup.String()),
	)

	gsel.SetBuilder(gsel.NewBuilderRoundRobin())

	sc := constant.ServerConfig{
		IpAddr: vIP.String(),
		Port:   vPort.Uint64(),
		Scheme: "http",
	}
	vCfgCatchDir := fnGetVar("cfg.catchDir")
	vCfgLogDir := fnGetVar("cfg.logDir")
	vDataID := fnGetVar("cfg.dataId")
	vServiceGroup := fnGetVar("cfg.serviceGroup")
	cc := constant.ClientConfig{
		CacheDir:         vCfgCatchDir.String(),
		LogDir:           vCfgLogDir.String(),
		NamespaceId:      vNameSpace.String(),
		Username:         vUserName.String(),
		Password:         vPass.String(),
		LogRollingConfig: logOption,
	}
	configParam := vo.ConfigParam{
		DataId: vDataID.String(),
		Group:  vServiceGroup.String(),
	}
	adapter, err := cfgNacos.New(
		ctx,
		cfgNacos.Config{
			ServerConfigs: []constant.ServerConfig{sc},
			ClientConfig:  cc,
			ConfigParam:   configParam,
			Watch:         true,
		},
	)
	if err != nil {
		panic(err)
	}

	g.Cfg().SetAdapter(adapter)

}
