# 多階段構建 Dockerfile for MCP Demo

# 第一階段：構建階段
FROM golang:1.21-alpine AS builder

# 設置工作目錄
WORKDIR /app

# 安裝必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 複製 go mod 文件
COPY go.mod go.sum ./

# 下載依賴
RUN go mod download

# 複製源代碼
COPY . .

# 構建應用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o mcp-demo main.go

# 第二階段：運行階段
FROM alpine:latest

# 安裝運行時依賴
RUN apk --no-cache add \
    ca-certificates \
    python3 \
    py3-pip \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# 創建非 root 用戶
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 設置工作目錄
WORKDIR /app

# 創建必要的目錄
RUN mkdir -p /app/logs /app/data /app/config && \
    chown -R appuser:appgroup /app

# 複製構建的二進制文件
COPY --from=builder /app/mcp-demo /app/mcp-demo

# 複製配置文件
COPY --from=builder /app/config.yaml /app/config.yaml

# 複製時區數據
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# 設置時區
ENV TZ=Asia/Shanghai

# 設置環境變量
ENV APP_ENV=production
ENV PORT=8080
ENV CONFIG_FILE=/app/config.yaml

# 確保二進制文件可執行
RUN chmod +x /app/mcp-demo

# 切換到非 root 用戶
USER appuser

# 暴露端口
EXPOSE 8080

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# 設置入口點
ENTRYPOINT ["/app/mcp-demo"]

# 默認命令
CMD []

# 元數據標籤
LABEL maintainer="brainHub Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="MCP Demo Application for brainHub"
LABEL org.opencontainers.image.title="MCP Demo"
LABEL org.opencontainers.image.description="Model Context Protocol demonstration application"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="brainHub"
LABEL org.opencontainers.image.licenses="MIT"
