# MCP Demo 配置文件

# 應用配置
app:
  name: "MCP Demo Server"
  version: "1.0.0"
  port: 8080
  debug: true

# MCP 配置
mcp:
  enabled: true
  
  # MCP 服務器配置
  servers:
    # 文件系統演示服務器
    - name: "demo-filesystem"
      type: "stdio"
      description: "演示文件系統操作工具"
      command: "python3"
      args: 
        - "-c"
        - |
          import json
          import sys
          import os
          
          def handle_request():
              # 簡單的 MCP 服務器模擬
              tools = [
                  {
                      "name": "read_file",
                      "description": "讀取文件內容",
                      "input_schema": {
                          "type": "object",
                          "properties": {
                              "path": {"type": "string", "description": "文件路徑"}
                          },
                          "required": ["path"]
                      }
                  },
                  {
                      "name": "list_files",
                      "description": "列出目錄中的文件",
                      "input_schema": {
                          "type": "object",
                          "properties": {
                              "directory": {"type": "string", "description": "目錄路徑"}
                          },
                          "required": ["directory"]
                      }
                  }
              ]
              
              # 返回工具列表
              response = {
                  "jsonrpc": "2.0",
                  "result": {"tools": tools},
                  "id": 1
              }
              print(json.dumps(response))
          
          if __name__ == "__main__":
              handle_request()
      timeout: "30s"
      retry_count: 3
      env:
        - "PYTHONPATH=/usr/local/lib/python3.9/site-packages"
    
    # 計算器演示服務器
    - name: "demo-calculator"
      type: "stdio"
      description: "演示計算器功能"
      command: "python3"
      args:
        - "-c"
        - |
          import json
          import sys
          import math
          
          def handle_request():
              tools = [
                  {
                      "name": "calculate",
                      "description": "執行數學計算",
                      "input_schema": {
                          "type": "object",
                          "properties": {
                              "expression": {"type": "string", "description": "數學表達式"}
                          },
                          "required": ["expression"]
                      }
                  },
                  {
                      "name": "sqrt",
                      "description": "計算平方根",
                      "input_schema": {
                          "type": "object",
                          "properties": {
                              "number": {"type": "number", "description": "要計算平方根的數字"}
                          },
                          "required": ["number"]
                      }
                  }
              ]
              
              response = {
                  "jsonrpc": "2.0",
                  "result": {"tools": tools},
                  "id": 1
              }
              print(json.dumps(response))
          
          if __name__ == "__main__":
              handle_request()
      timeout: "30s"
      retry_count: 3
    
    # HTTP 演示服務器（如果有可用的 HTTP MCP 服務器）
    - name: "demo-web-api"
      type: "http"
      description: "演示 Web API 工具"
      url: "http://localhost:9000/mcp"
      headers:
        Content-Type: "application/json"
        User-Agent: "MCP-Demo/1.0"
      timeout: "60s"
      retry_count: 2
      # 注意：這個服務器可能不可用，僅用於演示配置
  
  # 全局配置
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 10
    cache_ttl: "5m"
    log_level: "info"
    
    # 性能配置
    performance:
      enable_connection_pool: true
      pool_size: 5
      enable_compression: false
      enable_keep_alive: true
      max_idle_time: "2m"

# 日誌配置
logger:
  level: "info"
  format: "text"  # text 或 json
  output: "stdout"  # stdout, file, 或文件路徑
  
  # 文件日誌配置（當 output 為 file 時）
  file:
    path: "./logs/mcp-demo.log"
    max_size: "10MB"
    max_backups: 5
    max_age: "7d"
    compress: true

# 監控配置
monitoring:
  enabled: true
  
  # 指標配置
  metrics:
    enabled: true
    endpoint: "/metrics"
    
  # 健康檢查配置
  health:
    enabled: true
    endpoint: "/health"
    
  # 性能分析配置
  pprof:
    enabled: true
    endpoint: "/debug/pprof"

# 安全配置
security:
  # API 認證（演示環境可以禁用）
  auth:
    enabled: false
    api_key: "demo-api-key"
  
  # CORS 配置
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]
  
  # 速率限制
  rate_limit:
    enabled: false
    requests_per_minute: 100

# 演示數據配置
demo:
  # 預定義的演示工具調用
  sample_calls:
    - name: "文件讀取演示"
      tool: "demo-filesystem.read_file"
      parameters:
        path: "/tmp/demo.txt"
      description: "演示讀取文件功能"
    
    - name: "目錄列表演示"
      tool: "demo-filesystem.list_files"
      parameters:
        directory: "/tmp"
      description: "演示列出目錄文件功能"
    
    - name: "數學計算演示"
      tool: "demo-calculator.calculate"
      parameters:
        expression: "2 + 3 * 4"
      description: "演示數學計算功能"
    
    - name: "平方根計算演示"
      tool: "demo-calculator.sqrt"
      parameters:
        number: 16
      description: "演示平方根計算功能"
  
  # 演示場景
  scenarios:
    - name: "基礎工具調用"
      description: "演示基本的工具調用功能"
      steps:
        - "獲取工具列表"
        - "調用文件系統工具"
        - "調用計算器工具"
        - "查看統計信息"
    
    - name: "錯誤處理"
      description: "演示錯誤處理機制"
      steps:
        - "調用不存在的工具"
        - "使用無效參數調用工具"
        - "測試超時處理"
    
    - name: "性能測試"
      description: "演示性能和並發處理"
      steps:
        - "並發調用多個工具"
        - "測試緩存機制"
        - "監控資源使用"

# 開發配置
development:
  # 熱重載
  hot_reload: true
  
  # 調試模式
  debug_mode: true
  
  # 詳細日誌
  verbose_logging: true
  
  # 模擬延遲（用於測試）
  simulate_delay:
    enabled: false
    min_ms: 100
    max_ms: 500

# 測試配置
testing:
  # 測試數據目錄
  data_dir: "./testdata"
  
  # 測試超時
  timeout: "30s"
  
  # 並發測試
  concurrent_tests: 5
  
  # 測試覆蓋率
  coverage:
    enabled: true
    threshold: 80
