package main

import (
"encoding/json"
"log"
"net/http"
"os"
"time"
)

type ToolDefinition struct {
Name        string `json:"name"`
Description string `json:"description"`
}

type MCPDemoServer struct {
tools []ToolDefinition
}

func NewMCPDemoServer() *MCPDemoServer {
tools := []ToolDefinition{
{Name: "demo-echo", Description: "回顯輸入的文本"},
}
return &MCPDemoServer{tools: tools}
}

func (s *MCPDemoServer) GetToolsHandler(w http.ResponseWriter, r *http.Request) {
response := map[string]interface{}{
"success": true,
"data":    map[string]interface{}{"tools": s.tools, "count": len(s.tools)},
"message": "Tools retrieved successfully",
}
w.Header().Set("Content-Type", "application/json")
json.NewEncoder(w).Encode(response)
}

func (s *MCPDemoServer) GetHealthHandler(w http.ResponseWriter, r *http.Request) {
health := map[string]interface{}{
"status":    "healthy",
"timestamp": time.Now().Format(time.RFC3339),
"version":   "1.0.0-demo",
}
response := map[string]interface{}{
"success": true,
"data":    health,
"message": "Health check completed",
}
w.Header().Set("Content-Type", "application/json")
json.NewEncoder(w).Encode(response)
}

func main() {
server := NewMCPDemoServer()

http.HandleFunc("/api/tools", server.GetToolsHandler)
http.HandleFunc("/api/health", server.GetHealthHandler)

port := os.Getenv("PORT")
if port == "" {
port = "8080"
}

log.Printf("MCP Demo Server starting on :%s", port)
log.Printf("Open http://localhost:%s/api/health in your browser", port)

if err := http.ListenAndServe(":"+port, nil); err != nil {
log.Fatalf("Server failed to start: %v", err)
}
}
