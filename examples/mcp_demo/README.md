# MCP Demo 示例應用

## 概述

這是一個完整的 MCP (Model Context Protocol) 功能演示應用，展示了如何在 brainHub 項目中使用 MCP 工具調用功能。

## 功能特性

### 核心功能
- **工具管理**: 演示 MCP 工具的註冊、發現和管理
- **工具調用**: 展示如何調用不同類型的 MCP 工具
- **多客戶端支持**: 支持 STDIO、HTTP、SSE 三種客戶端類型
- **錯誤處理**: 演示完整的錯誤處理機制
- **性能監控**: 提供工具調用統計和性能指標

### 演示工具
- **文件系統工具**: 文件讀取、目錄列表等操作
- **計算器工具**: 數學計算、平方根等功能
- **Web API 工具**: HTTP 服務調用演示

### Web 界面
- **交互式界面**: 提供友好的 Web 界面進行測試
- **API 文檔**: 內置 API 端點說明
- **實時測試**: 支持實時調用和結果查看

## 快速開始

### 1. 環境準備

#### 系統要求
- Go 1.19+
- Python 3.7+ (用於演示工具)
- 網絡連接

#### 依賴安裝
```bash
# 安裝 Go 依賴
cd examples/mcp_demo
go mod init mcp-demo
go mod tidy

# 安裝 Python（如果尚未安裝）
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3 python3-pip

# macOS
brew install python3

# Windows
# 從 https://python.org 下載並安裝
```

### 2. 運行應用

#### 直接運行
```bash
# 進入演示目錄
cd examples/mcp_demo

# 運行應用
go run main.go
```

#### 使用配置文件
```bash
# 使用自定義配置
CONFIG_FILE=config.yaml go run main.go

# 指定端口
PORT=9090 go run main.go
```

#### Docker 運行
```bash
# 構建 Docker 鏡像
docker build -t mcp-demo .

# 運行容器
docker run -p 8080:8080 mcp-demo
```

### 3. 訪問應用

打開瀏覽器訪問：
```
http://localhost:8080
```

## API 端點

### 工具管理

#### 獲取工具列表
```http
GET /api/tools
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "tools": [
      {
        "name": "demo-filesystem.read_file",
        "description": "讀取文件內容",
        "parameters": {
          "type": "object",
          "properties": {
            "path": {"type": "string", "description": "文件路徑"}
          },
          "required": ["path"]
        }
      }
    ],
    "count": 4
  },
  "message": "Tools retrieved successfully"
}
```

#### 調用工具
```http
POST /api/tools/call
Content-Type: application/json

{
  "tool_name": "demo-calculator.calculate",
  "parameters": {
    "expression": "2 + 3 * 4"
  }
}
```

**響應示例**:
```json
{
  "success": true,
  "data": {
    "tool_name": "demo-calculator.calculate",
    "result": {
      "success": true,
      "content": "14"
    }
  },
  "message": "Tool executed successfully"
}
```

### 監控和統計

#### 獲取統計信息
```http
GET /api/stats
```

#### 健康檢查
```http
GET /api/health
```

#### 獲取配置
```http
GET /api/config
```

### 演示功能

#### 運行演示
```http
GET /api/demo
```

## 使用示例

### 1. 基本工具調用

```bash
# 獲取工具列表
curl http://localhost:8080/api/tools

# 調用計算器工具
curl -X POST http://localhost:8080/api/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "demo-calculator.calculate",
    "parameters": {
      "expression": "10 + 5"
    }
  }'

# 調用文件系統工具
curl -X POST http://localhost:8080/api/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "demo-filesystem.list_files",
    "parameters": {
      "directory": "/tmp"
    }
  }'
```

### 2. 批量測試

```bash
# 運行所有演示
curl http://localhost:8080/api/demo

# 查看統計信息
curl http://localhost:8080/api/stats

# 健康檢查
curl http://localhost:8080/api/health
```

### 3. JavaScript 客戶端

```javascript
// 獲取工具列表
async function getTools() {
  const response = await fetch('/api/tools');
  const data = await response.json();
  console.log('Available tools:', data.data.tools);
}

// 調用工具
async function callTool(toolName, parameters) {
  const response = await fetch('/api/tools/call', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      tool_name: toolName,
      parameters: parameters
    })
  });
  
  const result = await response.json();
  console.log('Tool result:', result);
  return result;
}

// 示例調用
callTool('demo-calculator.calculate', { expression: '2 + 2' });
```

## 配置說明

### MCP 服務器配置

```yaml
mcp:
  enabled: true
  servers:
    - name: "demo-filesystem"
      type: "stdio"
      description: "文件系統工具"
      command: "python3"
      args: ["-c", "print('Hello MCP')"]
      timeout: "30s"
      retry_count: 3
```

### 全局配置

```yaml
mcp:
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 10
    cache_ttl: "5m"
    log_level: "info"
```

## 故障排除

### 常見問題

#### 1. 工具調用失敗
**問題**: 工具調用返回錯誤
**解決方案**:
```bash
# 檢查工具配置
curl http://localhost:8080/api/config

# 查看健康狀態
curl http://localhost:8080/api/health

# 檢查日誌
tail -f logs/mcp-demo.log
```

#### 2. Python 命令不可用
**問題**: STDIO 工具無法執行
**解決方案**:
```bash
# 檢查 Python 安裝
python3 --version

# 檢查 PATH 環境變量
echo $PATH

# 使用絕對路徑
which python3
```

#### 3. 端口被占用
**問題**: 服務器無法啟動
**解決方案**:
```bash
# 檢查端口使用情況
lsof -i :8080

# 使用不同端口
PORT=9090 go run main.go
```

### 調試技巧

#### 啟用詳細日誌
```bash
# 設置環境變量
export MCP_LOG_LEVEL=debug
go run main.go
```

#### 使用 curl 測試
```bash
# 測試所有端點
curl -v http://localhost:8080/api/tools
curl -v http://localhost:8080/api/health
curl -v http://localhost:8080/api/stats
```

## 擴展開發

### 添加自定義工具

1. **創建工具腳本**:
```python
#!/usr/bin/env python3
import json
import sys

def my_custom_tool(params):
    # 實現你的工具邏輯
    result = f"Hello, {params.get('name', 'World')}!"
    return {"success": True, "content": result}

if __name__ == "__main__":
    # MCP 協議處理
    pass
```

2. **更新配置**:
```yaml
mcp:
  servers:
    - name: "my-custom-tool"
      type: "stdio"
      command: "python3"
      args: ["./my_tool.py"]
```

### 集成到 brainHub

1. **導入 MCP 模塊**:
```go
import "brainHub/internal/llms/mcp"
```

2. **初始化工具管理器**:
```go
toolManager := mcp.NewMCPToolManager(config)
err := toolManager.Initialize(ctx)
```

3. **在 LLM 中使用**:
```go
tools, _ := toolManager.GetToolDefinitions(ctx)
result, _ := toolManager.CallTool(ctx, "tool.name", params)
```

## 性能優化

### 緩存配置
```yaml
mcp:
  global:
    cache_ttl: "10m"  # 增加緩存時間
    max_concurrent_calls: 20  # 增加並發數
```

### 連接池優化
```yaml
mcp:
  global:
    performance:
      enable_connection_pool: true
      pool_size: 10
      max_idle_time: "5m"
```

## 監控和指標

### 關鍵指標
- 工具調用成功率
- 平均響應時間
- 並發處理能力
- 錯誤率統計

### 監控端點
- `/api/stats` - 統計信息
- `/api/health` - 健康檢查
- `/metrics` - Prometheus 指標

## 貢獻指南

1. Fork 項目
2. 創建功能分支
3. 提交更改
4. 創建 Pull Request

## 許可證

本項目使用 MIT 許可證。詳見 LICENSE 文件。

## 聯繫方式

- 項目地址: https://git.qbiai.com/Nanjing/NanjingRD/ai/chatgpt/brainhub
- 問題報告: [Issues](https://git.qbiai.com/Nanjing/NanjingRD/ai/chatgpt/brainhub/issues)
- 文檔中心: [Wiki](https://git.qbiai.com/Nanjing/NanjingRD/ai/chatgpt/brainhub/wiki)
