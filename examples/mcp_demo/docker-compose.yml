version: '3.8'

services:
  # MCP Demo 應用
  mcp-demo:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mcp-demo-app
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=production
      - PORT=8080
      - CONFIG_FILE=/app/config.yaml
      - MCP_LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config.yaml:/app/config.yaml:ro
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mcp-demo.rule=Host(`mcp-demo.localhost`)"
      - "traefik.http.services.mcp-demo.loadbalancer.server.port=8080"

  # Redis 緩存服務
  redis:
    image: redis:7-alpine
    container_name: mcp-demo-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: mcp-demo-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - mcp-network
    restart: unless-stopped
    depends_on:
      - mcp-demo
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 監控
  prometheus:
    image: prom/prometheus:latest
    container_name: mcp-demo-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mcp-network
    restart: unless-stopped

  # Grafana 儀表板
  grafana:
    image: grafana/grafana:latest
    container_name: mcp-demo-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - mcp-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # 日誌聚合服務
  loki:
    image: grafana/loki:latest
    container_name: mcp-demo-loki
    ports:
      - "3100:3100"
    volumes:
      - ./loki.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - mcp-network
    restart: unless-stopped

  # 日誌收集器
  promtail:
    image: grafana/promtail:latest
    container_name: mcp-demo-promtail
    volumes:
      - ./promtail.yml:/etc/promtail/config.yml:ro
      - ./logs:/var/log/mcp-demo:ro
      - nginx_logs:/var/log/nginx:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - mcp-network
    restart: unless-stopped
    depends_on:
      - loki

  # 模擬 MCP HTTP 服務器
  mock-mcp-server:
    image: python:3.9-alpine
    container_name: mock-mcp-server
    ports:
      - "9000:9000"
    volumes:
      - ./mock_server.py:/app/server.py:ro
    working_dir: /app
    command: python server.py
    networks:
      - mcp-network
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - PORT=9000

# 網絡配置
networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 數據卷配置
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  nginx_logs:
    driver: local

# 開發環境覆蓋配置
# 使用: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
version: '3.8'

# 開發環境特定配置
x-development: &development
  environment:
    - APP_ENV=development
    - MCP_LOG_LEVEL=debug
  volumes:
    - .:/app:cached
    - /app/vendor
  command: go run main.go

services:
  mcp-demo:
    <<: *development
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
      - "6060:6060"  # pprof 端口

  # 開發工具
  adminer:
    image: adminer
    container_name: mcp-demo-adminer
    ports:
      - "8081:8080"
    networks:
      - mcp-network
    restart: unless-stopped

  # 郵件測試服務
  mailhog:
    image: mailhog/mailhog
    container_name: mcp-demo-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - mcp-network
    restart: unless-stopped
